{"APP_BUILD_BOOTLOADER": true, "APP_BUILD_GENERATE_BINARIES": true, "APP_BUILD_TYPE_APP_2NDBOOT": true, "APP_BUILD_TYPE_RAM": false, "APP_BUILD_USE_FLASH_SECTIONS": true, "APP_COMPILE_TIME_DATE": true, "APP_EXCLUDE_PROJECT_NAME_VAR": false, "APP_EXCLUDE_PROJECT_VER_VAR": false, "APP_NO_BLOBS": false, "APP_PROJECT_VER_FROM_CONFIG": false, "APP_REPRODUCIBLE_BUILD": false, "APP_RETRIEVE_LEN_ELF_SHA": 9, "BOOTLOADER_APP_ROLLBACK_ENABLE": false, "BOOTLOADER_APP_TEST": false, "BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG": false, "BOOTLOADER_COMPILER_OPTIMIZATION_NONE": false, "BOOTLOADER_COMPILER_OPTIMIZATION_PERF": false, "BOOTLOADER_COMPILER_OPTIMIZATION_SIZE": true, "BOOTLOADER_COMPILE_TIME_DATE": true, "BOOTLOADER_CUSTOM_RESERVE_RTC": false, "BOOTLOADER_FACTORY_RESET": false, "BOOTLOADER_FLASH_32BIT_ADDR": true, "BOOTLOADER_FLASH_DC_AWARE": false, "BOOTLOADER_FLASH_NEEDS_32BIT_ADDR_QUAD_FLASH": true, "BOOTLOADER_FLASH_NEEDS_32BIT_FEAT": true, "BOOTLOADER_FLASH_XMC_SUPPORT": true, "BOOTLOADER_LOG_COLORS": false, "BOOTLOADER_LOG_LEVEL": 3, "BOOTLOADER_LOG_LEVEL_DEBUG": false, "BOOTLOADER_LOG_LEVEL_ERROR": false, "BOOTLOADER_LOG_LEVEL_INFO": true, "BOOTLOADER_LOG_LEVEL_NONE": false, "BOOTLOADER_LOG_LEVEL_VERBOSE": false, "BOOTLOADER_LOG_LEVEL_WARN": false, "BOOTLOADER_LOG_TIMESTAMP_SOURCE_CPU_TICKS": true, "BOOTLOADER_OFFSET_IN_FLASH": 0, "BOOTLOADER_PROJECT_VER": 1, "BOOTLOADER_REGION_PROTECTION_ENABLE": true, "BOOTLOADER_RESERVE_RTC_SIZE": 0, "BOOTLOADER_SKIP_VALIDATE_ALWAYS": false, "BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP": false, "BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON": false, "BOOTLOADER_VDDSDIO_BOOST_1_9V": true, "BOOTLOADER_WDT_DISABLE_IN_USER_CODE": false, "BOOTLOADER_WDT_ENABLE": true, "BOOTLOADER_WDT_TIME_MS": 9000, "BOOT_ROM_LOG_ALWAYS_OFF": false, "BOOT_ROM_LOG_ALWAYS_ON": true, "BOOT_ROM_LOG_ON_GPIO_HIGH": false, "BOOT_ROM_LOG_ON_GPIO_LOW": false, "COMPILER_ASSERT_NDEBUG_EVALUATE": true, "COMPILER_CXX_EXCEPTIONS": false, "COMPILER_CXX_RTTI": false, "COMPILER_DISABLE_DEFAULT_ERRORS": true, "COMPILER_DISABLE_GCC12_WARNINGS": false, "COMPILER_DISABLE_GCC13_WARNINGS": false, "COMPILER_DISABLE_GCC14_WARNINGS": false, "COMPILER_DUMP_RTL_FILES": false, "COMPILER_FLOAT_LIB_FROM_GCCLIB": true, "COMPILER_HIDE_PATHS_MACROS": true, "COMPILER_NO_MERGE_CONSTANTS": false, "COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE": false, "COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE": true, "COMPILER_OPTIMIZATION_ASSERTIONS_SILENT": false, "COMPILER_OPTIMIZATION_ASSERTION_LEVEL": 2, "COMPILER_OPTIMIZATION_CHECKS_SILENT": false, "COMPILER_OPTIMIZATION_DEBUG": true, "COMPILER_OPTIMIZATION_NONE": false, "COMPILER_OPTIMIZATION_PERF": false, "COMPILER_OPTIMIZATION_SIZE": false, "COMPILER_ORPHAN_SECTIONS_PLACE": false, "COMPILER_ORPHAN_SECTIONS_WARNING": true, "COMPILER_RT_LIB_GCCLIB": true, "COMPILER_RT_LIB_NAME": "gcc", "COMPILER_STACK_CHECK_MODE_ALL": false, "COMPILER_STACK_CHECK_MODE_NONE": true, "COMPILER_STACK_CHECK_MODE_NORM": false, "COMPILER_STACK_CHECK_MODE_STRONG": false, "COMPILER_STATIC_ANALYZER": false, "COMPILER_WARN_WRITE_STRINGS": false, "EFUSE_CUSTOM_TABLE": false, "EFUSE_MAX_BLK_LEN": 256, "EFUSE_VIRTUAL": false, "ESP32S3_DATA_CACHE_16KB": false, "ESP32S3_DATA_CACHE_32KB": true, "ESP32S3_DATA_CACHE_4WAYS": false, "ESP32S3_DATA_CACHE_64KB": false, "ESP32S3_DATA_CACHE_8WAYS": true, "ESP32S3_DATA_CACHE_LINE_16B": false, "ESP32S3_DATA_CACHE_LINE_32B": true, "ESP32S3_DATA_CACHE_LINE_64B": false, "ESP32S3_DATA_CACHE_LINE_SIZE": 32, "ESP32S3_DATA_CACHE_SIZE": 32768, "ESP32S3_DCACHE_ASSOCIATED_WAYS": 8, "ESP32S3_ICACHE_ASSOCIATED_WAYS": 8, "ESP32S3_INSTRUCTION_CACHE_16KB": true, "ESP32S3_INSTRUCTION_CACHE_32KB": false, "ESP32S3_INSTRUCTION_CACHE_4WAYS": false, "ESP32S3_INSTRUCTION_CACHE_8WAYS": true, "ESP32S3_INSTRUCTION_CACHE_LINE_16B": false, "ESP32S3_INSTRUCTION_CACHE_LINE_32B": true, "ESP32S3_INSTRUCTION_CACHE_LINE_SIZE": 32, "ESP32S3_INSTRUCTION_CACHE_SIZE": 16384, "ESP32S3_REV_MAX_FULL": 99, "ESP32S3_REV_MIN_0": true, "ESP32S3_REV_MIN_1": false, "ESP32S3_REV_MIN_2": false, "ESP32S3_REV_MIN_FULL": 0, "ESP32S3_RTCDATA_IN_FAST_MEM": false, "ESP32S3_TRACEMEM_RESERVE_DRAM": 0, "ESP32S3_TRAX": false, "ESP32S3_UNIVERSAL_MAC_ADDRESSES": 4, "ESP32S3_UNIVERSAL_MAC_ADDRESSES_FOUR": true, "ESP32S3_UNIVERSAL_MAC_ADDRESSES_TWO": false, "ESP32S3_USE_FIXED_STATIC_RAM_SIZE": false, "ESPTOOLPY_AFTER": "hard_reset", "ESPTOOLPY_AFTER_NORESET": false, "ESPTOOLPY_AFTER_RESET": true, "ESPTOOLPY_BEFORE": "default_reset", "ESPTOOLPY_BEFORE_NORESET": false, "ESPTOOLPY_BEFORE_RESET": true, "ESPTOOLPY_FLASHFREQ": "80m", "ESPTOOLPY_FLASHFREQ_120M": false, "ESPTOOLPY_FLASHFREQ_20M": false, "ESPTOOLPY_FLASHFREQ_40M": false, "ESPTOOLPY_FLASHFREQ_80M": true, "ESPTOOLPY_FLASHMODE": "dio", "ESPTOOLPY_FLASHMODE_DIO": true, "ESPTOOLPY_FLASHMODE_DOUT": false, "ESPTOOLPY_FLASHMODE_QIO": false, "ESPTOOLPY_FLASHMODE_QOUT": false, "ESPTOOLPY_FLASHSIZE": "32MB", "ESPTOOLPY_FLASHSIZE_128MB": false, "ESPTOOLPY_FLASHSIZE_16MB": false, "ESPTOOLPY_FLASHSIZE_1MB": false, "ESPTOOLPY_FLASHSIZE_2MB": false, "ESPTOOLPY_FLASHSIZE_32MB": true, "ESPTOOLPY_FLASHSIZE_4MB": false, "ESPTOOLPY_FLASHSIZE_64MB": false, "ESPTOOLPY_FLASHSIZE_8MB": false, "ESPTOOLPY_FLASH_MODE_AUTO_DETECT": true, "ESPTOOLPY_FLASH_SAMPLE_MODE_STR": true, "ESPTOOLPY_HEADER_FLASHSIZE_UPDATE": true, "ESPTOOLPY_MONITOR_BAUD": 115200, "ESPTOOLPY_NO_STUB": false, "ESPTOOLPY_OCT_FLASH": false, "ESP_BROWNOUT_DET": true, "ESP_BROWNOUT_DET_LVL": 7, "ESP_BROWNOUT_DET_LVL_SEL_1": false, "ESP_BROWNOUT_DET_LVL_SEL_2": false, "ESP_BROWNOUT_DET_LVL_SEL_3": false, "ESP_BROWNOUT_DET_LVL_SEL_4": false, "ESP_BROWNOUT_DET_LVL_SEL_5": false, "ESP_BROWNOUT_DET_LVL_SEL_6": false, "ESP_BROWNOUT_DET_LVL_SEL_7": true, "ESP_CONSOLE_NONE": false, "ESP_CONSOLE_ROM_SERIAL_PORT_NUM": 0, "ESP_CONSOLE_SECONDARY_NONE": false, "ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG": true, "ESP_CONSOLE_UART": true, "ESP_CONSOLE_UART_BAUDRATE": 115200, "ESP_CONSOLE_UART_CUSTOM": false, "ESP_CONSOLE_UART_DEFAULT": true, "ESP_CONSOLE_UART_NUM": 0, "ESP_CONSOLE_USB_CDC": false, "ESP_CONSOLE_USB_SERIAL_JTAG": false, "ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED": true, "ESP_DEBUG_OCDAWARE": true, "ESP_DEBUG_STUBS_ENABLE": false, "ESP_DEFAULT_CPU_FREQ_MHZ": 240, "ESP_DEFAULT_CPU_FREQ_MHZ_160": false, "ESP_DEFAULT_CPU_FREQ_MHZ_240": true, "ESP_DEFAULT_CPU_FREQ_MHZ_80": false, "ESP_EFUSE_BLOCK_REV_MAX_FULL": 199, "ESP_EFUSE_BLOCK_REV_MIN_FULL": 0, "ESP_ERR_TO_NAME_LOOKUP": true, "ESP_INT_WDT": true, "ESP_INT_WDT_CHECK_CPU1": true, "ESP_INT_WDT_TIMEOUT_MS": 300, "ESP_IPC_ISR_ENABLE": true, "ESP_IPC_TASK_STACK_SIZE": 1280, "ESP_IPC_USES_CALLERS_PRIORITY": true, "ESP_MAC_ADDR_UNIVERSE_BT": true, "ESP_MAC_ADDR_UNIVERSE_ETH": true, "ESP_MAC_ADDR_UNIVERSE_WIFI_AP": true, "ESP_MAC_ADDR_UNIVERSE_WIFI_STA": true, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES": 4, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR": true, "ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC": false, "ESP_MAIN_TASK_AFFINITY": 0, "ESP_MAIN_TASK_AFFINITY_CPU0": true, "ESP_MAIN_TASK_AFFINITY_CPU1": false, "ESP_MAIN_TASK_AFFINITY_NO_AFFINITY": false, "ESP_MAIN_TASK_STACK_SIZE": 3584, "ESP_MINIMAL_SHARED_STACK_SIZE": 2048, "ESP_PANIC_HANDLER_IRAM": false, "ESP_REV_MAX_FULL": 99, "ESP_REV_MIN_FULL": 0, "ESP_ROM_GET_CLK_FREQ": true, "ESP_ROM_HAS_CACHE_SUSPEND_WAITI_BUG": true, "ESP_ROM_HAS_CACHE_WRITEBACK_BUG": true, "ESP_ROM_HAS_CRC_BE": true, "ESP_ROM_HAS_CRC_LE": true, "ESP_ROM_HAS_ENCRYPTED_WRITES_USING_LEGACY_DRV": true, "ESP_ROM_HAS_ERASE_0_REGION_BUG": true, "ESP_ROM_HAS_ETS_PRINTF_BUG": true, "ESP_ROM_HAS_FLASH_COUNT_PAGES_BUG": true, "ESP_ROM_HAS_HAL_WDT": true, "ESP_ROM_HAS_JPEG_DECODE": true, "ESP_ROM_HAS_LAYOUT_TABLE": true, "ESP_ROM_HAS_MZ_CRC32": true, "ESP_ROM_HAS_NEWLIB": true, "ESP_ROM_HAS_NEWLIB_32BIT_TIME": true, "ESP_ROM_HAS_NEWLIB_NANO_FORMAT": true, "ESP_ROM_HAS_OUTPUT_PUTC_FUNC": true, "ESP_ROM_HAS_RETARGETABLE_LOCKING": true, "ESP_ROM_HAS_SPI_FLASH": true, "ESP_ROM_HAS_SW_FLOAT": true, "ESP_ROM_HAS_VERSION": true, "ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE": true, "ESP_ROM_NEEDS_SWSETUP_WORKAROUND": true, "ESP_ROM_RAM_APP_NEEDS_MMU_INIT": true, "ESP_ROM_SUPPORT_DEEP_SLEEP_WAKEUP_STUB": true, "ESP_ROM_UART_CLK_IS_XTAL": true, "ESP_ROM_USB_OTG_NUM": 3, "ESP_ROM_USB_SERIAL_DEVICE_NUM": 4, "ESP_SLEEP_CACHE_SAFE_ASSERTION": false, "ESP_SLEEP_DEBUG": false, "ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND": true, "ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS": true, "ESP_SLEEP_GPIO_RESET_WORKAROUND": true, "ESP_SLEEP_MSPI_NEED_ALL_IO_PU": true, "ESP_SLEEP_POWER_DOWN_FLASH": false, "ESP_SLEEP_RTC_BUS_ISO_WORKAROUND": true, "ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY": 2000, "ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP": true, "ESP_SYSTEM_BBPLL_RECALIB": true, "ESP_SYSTEM_BROWNOUT_INTR": true, "ESP_SYSTEM_CHECK_INT_LEVEL_4": true, "ESP_SYSTEM_EVENT_QUEUE_SIZE": 32, "ESP_SYSTEM_EVENT_TASK_STACK_SIZE": 2304, "ESP_SYSTEM_MEMPROT_FEATURE": true, "ESP_SYSTEM_MEMPROT_FEATURE_LOCK": true, "ESP_SYSTEM_PANIC_PRINT_HALT": false, "ESP_SYSTEM_PANIC_PRINT_REBOOT": true, "ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS": 0, "ESP_SYSTEM_PANIC_SILENT_REBOOT": false, "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK": true, "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0": true, "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1": true, "ESP_TASK_WDT_EN": true, "ESP_TASK_WDT_INIT": true, "ESP_TASK_WDT_PANIC": false, "ESP_TASK_WDT_TIMEOUT_S": 5, "FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER": true, "FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE": false, "FREERTOS_CHECK_STACKOVERFLOW_CANARY": true, "FREERTOS_CHECK_STACKOVERFLOW_NONE": false, "FREERTOS_CHECK_STACKOVERFLOW_PTRVAL": false, "FREERTOS_CORETIMER_SYSTIMER_LVL1": true, "FREERTOS_CORETIMER_SYSTIMER_LVL3": false, "FREERTOS_DEBUG_OCDAWARE": true, "FREERTOS_ENABLE_BACKWARD_COMPATIBILITY": false, "FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP": false, "FREERTOS_ENABLE_TASK_SNAPSHOT": true, "FREERTOS_FPU_IN_ISR": false, "FREERTOS_GENERATE_RUN_TIME_STATS": false, "FREERTOS_HZ": 100, "FREERTOS_IDLE_TASK_STACKSIZE": 1536, "FREERTOS_INTERRUPT_BACKTRACE": true, "FREERTOS_ISR_STACKSIZE": 1536, "FREERTOS_MAX_TASK_NAME_LEN": 16, "FREERTOS_NO_AFFINITY": 2147483647, "FREERTOS_NUMBER_OF_CORES": 2, "FREERTOS_PLACE_FUNCTIONS_INTO_FLASH": false, "FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH": true, "FREERTOS_PORT": true, "FREERTOS_QUEUE_REGISTRY_SIZE": 0, "FREERTOS_SMP": false, "FREERTOS_SUPPORT_STATIC_ALLOCATION": true, "FREERTOS_SYSTICK_USES_SYSTIMER": true, "FREERTOS_TASK_FUNCTION_WRAPPER": true, "FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES": 3, "FREERTOS_TASK_PRE_DELETION_HOOK": false, "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS": 1, "FREERTOS_TICK_SUPPORT_SYSTIMER": true, "FREERTOS_TIMER_QUEUE_LENGTH": 10, "FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY": 2147483647, "FREERTOS_TIMER_SERVICE_TASK_NAME": "Tmr Svc", "FREERTOS_TIMER_TASK_AFFINITY_CPU0": false, "FREERTOS_TIMER_TASK_AFFINITY_CPU1": false, "FREERTOS_TIMER_TASK_NO_AFFINITY": true, "FREERTOS_TIMER_TASK_PRIORITY": 1, "FREERTOS_TIMER_TASK_STACK_DEPTH": 2048, "FREERTOS_TLSP_DELETION_CALLBACKS": true, "FREERTOS_UNICORE": false, "FREERTOS_USE_APPLICATION_TASK_TAG": false, "FREERTOS_USE_IDLE_HOOK": false, "FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES": false, "FREERTOS_USE_TICK_HOOK": false, "FREERTOS_USE_TIMERS": true, "FREERTOS_USE_TRACE_FACILITY": false, "FREERTOS_WATCHPOINT_END_OF_STACK": false, "GDMA_CTRL_FUNC_IN_IRAM": true, "GDMA_ENABLE_DEBUG_LOG": false, "GDMA_ISR_IRAM_SAFE": false, "HAL_ASSERTION_DISABLE": false, "HAL_ASSERTION_ENABLE": false, "HAL_ASSERTION_EQUALS_SYSTEM": true, "HAL_ASSERTION_SILENT": false, "HAL_DEFAULT_ASSERTION_LEVEL": 2, "HAL_WDT_USE_ROM_IMPL": true, "IDF_CMAKE": true, "IDF_EXPERIMENTAL_FEATURES": false, "IDF_FIRMWARE_CHIP_ID": 9, "IDF_INIT_VERSION": "5.4.1", "IDF_TARGET": "esp32s3", "IDF_TARGET_ARCH": "xtensa", "IDF_TARGET_ARCH_XTENSA": true, "IDF_TARGET_ESP32S3": true, "IDF_TOOLCHAIN": "gcc", "IDF_TOOLCHAIN_GCC": true, "LOG_COLORS": false, "LOG_DEFAULT_LEVEL": 3, "LOG_DEFAULT_LEVEL_DEBUG": false, "LOG_DEFAULT_LEVEL_ERROR": false, "LOG_DEFAULT_LEVEL_INFO": true, "LOG_DEFAULT_LEVEL_NONE": false, "LOG_DEFAULT_LEVEL_VERBOSE": false, "LOG_DEFAULT_LEVEL_WARN": false, "LOG_DYNAMIC_LEVEL_CONTROL": true, "LOG_MASTER_LEVEL": false, "LOG_MAXIMUM_EQUALS_DEFAULT": true, "LOG_MAXIMUM_LEVEL": 3, "LOG_MAXIMUM_LEVEL_DEBUG": false, "LOG_MAXIMUM_LEVEL_VERBOSE": false, "LOG_TAG_LEVEL_CACHE_ARRAY": false, "LOG_TAG_LEVEL_CACHE_BINARY_MIN_HEAP": true, "LOG_TAG_LEVEL_IMPL_CACHE_AND_LINKED_LIST": true, "LOG_TAG_LEVEL_IMPL_CACHE_SIZE": 31, "LOG_TAG_LEVEL_IMPL_LINKED_LIST": false, "LOG_TAG_LEVEL_IMPL_NONE": false, "LOG_TIMESTAMP_SOURCE_RTOS": true, "LOG_TIMESTAMP_SOURCE_SYSTEM": false, "MMU_PAGE_MODE": "64KB", "MMU_PAGE_SIZE": 65536, "MMU_PAGE_SIZE_64KB": true, "NEWLIB_NANO_FORMAT": false, "NEWLIB_STDIN_LINE_ENDING_CR": true, "NEWLIB_STDIN_LINE_ENDING_CRLF": false, "NEWLIB_STDIN_LINE_ENDING_LF": false, "NEWLIB_STDOUT_LINE_ENDING_CR": false, "NEWLIB_STDOUT_LINE_ENDING_CRLF": true, "NEWLIB_STDOUT_LINE_ENDING_LF": false, "NEWLIB_TIME_SYSCALL_USE_HRT": false, "NEWLIB_TIME_SYSCALL_USE_NONE": false, "NEWLIB_TIME_SYSCALL_USE_RTC": false, "NEWLIB_TIME_SYSCALL_USE_RTC_HRT": true, "PARTITION_TABLE_CUSTOM": true, "PARTITION_TABLE_CUSTOM_FILENAME": "partitions.csv", "PARTITION_TABLE_FILENAME": "partitions.csv", "PARTITION_TABLE_MD5": true, "PARTITION_TABLE_OFFSET": 32768, "PARTITION_TABLE_SINGLE_APP": false, "PARTITION_TABLE_SINGLE_APP_LARGE": false, "PARTITION_TABLE_TWO_OTA": false, "PARTITION_TABLE_TWO_OTA_LARGE": false, "PERIPH_CTRL_FUNC_IN_IRAM": true, "RTC_CLK_CAL_CYCLES": 1024, "RTC_CLK_SRC_EXT_CRYS": false, "RTC_CLK_SRC_EXT_OSC": false, "RTC_CLK_SRC_INT_8MD256": false, "RTC_CLK_SRC_INT_RC": true, "SECURE_BOOT": false, "SECURE_BOOT_V2_PREFERRED": true, "SECURE_BOOT_V2_RSA_SUPPORTED": true, "SECURE_FLASH_ENC_ENABLED": false, "SECURE_ROM_DL_MODE_ENABLED": true, "SECURE_SIGNED_APPS_NO_SECURE_BOOT": false, "SOC_ADC_ARBITER_SUPPORTED": true, "SOC_ADC_ATTEN_NUM": 4, "SOC_ADC_CALIBRATION_V1_SUPPORTED": true, "SOC_ADC_DIGI_CONTROLLER_NUM": 2, "SOC_ADC_DIGI_DATA_BYTES_PER_CONV": 4, "SOC_ADC_DIGI_IIR_FILTER_NUM": 2, "SOC_ADC_DIGI_MAX_BITWIDTH": 12, "SOC_ADC_DIGI_MIN_BITWIDTH": 12, "SOC_ADC_DIGI_MONITOR_NUM": 2, "SOC_ADC_DIGI_RESULT_BYTES": 4, "SOC_ADC_DIG_CTRL_SUPPORTED": true, "SOC_ADC_DIG_IIR_FILTER_SUPPORTED": true, "SOC_ADC_DMA_SUPPORTED": true, "SOC_ADC_MAX_CHANNEL_NUM": 10, "SOC_ADC_MONITOR_SUPPORTED": true, "SOC_ADC_PATT_LEN_MAX": 24, "SOC_ADC_PERIPH_NUM": 2, "SOC_ADC_RTC_CTRL_SUPPORTED": true, "SOC_ADC_RTC_MAX_BITWIDTH": 12, "SOC_ADC_RTC_MIN_BITWIDTH": 12, "SOC_ADC_SAMPLE_FREQ_THRES_HIGH": 83333, "SOC_ADC_SAMPLE_FREQ_THRES_LOW": 611, "SOC_ADC_SELF_HW_CALI_SUPPORTED": true, "SOC_ADC_SHARED_POWER": true, "SOC_ADC_SUPPORTED": true, "SOC_AES_GDMA": true, "SOC_AES_SUPPORTED": true, "SOC_AES_SUPPORT_AES_128": true, "SOC_AES_SUPPORT_AES_256": true, "SOC_AES_SUPPORT_DMA": true, "SOC_AHB_GDMA_SUPPORTED": true, "SOC_AHB_GDMA_SUPPORT_PSRAM": true, "SOC_AHB_GDMA_VERSION": 1, "SOC_APB_BACKUP_DMA": true, "SOC_APPCPU_HAS_CLOCK_GATING_BUG": true, "SOC_ASYNC_MEMCPY_SUPPORTED": true, "SOC_BLE_50_SUPPORTED": true, "SOC_BLE_DEVICE_PRIVACY_SUPPORTED": true, "SOC_BLE_MESH_SUPPORTED": true, "SOC_BLE_SUPPORTED": true, "SOC_BLUFI_SUPPORTED": true, "SOC_BOD_SUPPORTED": true, "SOC_BROWNOUT_RESET_SUPPORTED": true, "SOC_BT_SUPPORTED": true, "SOC_CACHE_FREEZE_SUPPORTED": true, "SOC_CACHE_SUPPORT_WRAP": true, "SOC_CACHE_WRITEBACK_SUPPORTED": true, "SOC_CCOMP_TIMER_SUPPORTED": true, "SOC_CLK_RC_FAST_D256_SUPPORTED": true, "SOC_CLK_RC_FAST_SUPPORT_CALIBRATION": true, "SOC_CLK_TREE_SUPPORTED": true, "SOC_CLK_XTAL32K_SUPPORTED": true, "SOC_COEX_HW_PTI": true, "SOC_CONFIGURABLE_VDDSDIO_SUPPORTED": true, "SOC_CPU_BREAKPOINTS_NUM": 2, "SOC_CPU_CORES_NUM": 2, "SOC_CPU_HAS_FPU": true, "SOC_CPU_INTR_NUM": 32, "SOC_CPU_WATCHPOINTS_NUM": 2, "SOC_CPU_WATCHPOINT_MAX_REGION_SIZE": 64, "SOC_DEDICATED_GPIO_SUPPORTED": true, "SOC_DEDIC_GPIO_IN_CHANNELS_NUM": 8, "SOC_DEDIC_GPIO_OUT_AUTO_ENABLE": true, "SOC_DEDIC_GPIO_OUT_CHANNELS_NUM": 8, "SOC_DEEP_SLEEP_SUPPORTED": true, "SOC_DIG_SIGN_SUPPORTED": true, "SOC_DS_KEY_CHECK_MAX_WAIT_US": 1100, "SOC_DS_KEY_PARAM_MD_IV_LENGTH": 16, "SOC_DS_SIGNATURE_MAX_BIT_LEN": 4096, "SOC_EFUSE_BLOCK9_KEY_PURPOSE_QUIRK": true, "SOC_EFUSE_DIS_DIRECT_BOOT": true, "SOC_EFUSE_DIS_DOWNLOAD_DCACHE": true, "SOC_EFUSE_DIS_DOWNLOAD_ICACHE": true, "SOC_EFUSE_DIS_ICACHE": true, "SOC_EFUSE_DIS_USB_JTAG": true, "SOC_EFUSE_HARD_DIS_JTAG": true, "SOC_EFUSE_KEY_PURPOSE_FIELD": true, "SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS": true, "SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS": 3, "SOC_EFUSE_SOFT_DIS_JTAG": true, "SOC_EFUSE_SUPPORTED": true, "SOC_EXTERNAL_COEX_LEADER_TX_LINE": true, "SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX": 64, "SOC_FLASH_ENCRYPTION_XTS_AES": true, "SOC_FLASH_ENCRYPTION_XTS_AES_128": true, "SOC_FLASH_ENCRYPTION_XTS_AES_256": true, "SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS": true, "SOC_FLASH_ENC_SUPPORTED": true, "SOC_GDMA_NUM_GROUPS_MAX": 1, "SOC_GDMA_PAIRS_PER_GROUP": 5, "SOC_GDMA_PAIRS_PER_GROUP_MAX": 5, "SOC_GDMA_SUPPORTED": true, "SOC_GPIO_CLOCKOUT_BY_IO_MUX": true, "SOC_GPIO_CLOCKOUT_CHANNEL_NUM": 3, "SOC_GPIO_FILTER_CLK_SUPPORT_APB": true, "SOC_GPIO_IN_RANGE_MAX": 48, "SOC_GPIO_OUT_RANGE_MAX": 48, "SOC_GPIO_PIN_COUNT": 49, "SOC_GPIO_PORT": 1, "SOC_GPIO_SUPPORT_FORCE_HOLD": true, "SOC_GPIO_SUPPORT_HOLD_IO_IN_DSLP": true, "SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER": true, "SOC_GPIO_SUPPORT_RTC_INDEPENDENT": true, "SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK": 562949886312448, "SOC_GPIO_VALID_GPIO_MASK": 562949953421311, "SOC_GPSPI_SUPPORTED": true, "SOC_GPTIMER_SUPPORTED": true, "SOC_HMAC_SUPPORTED": true, "SOC_HP_CPU_HAS_MULTIPLE_CORES": true, "SOC_HP_I2C_NUM": 2, "SOC_I2C_CMD_REG_NUM": 8, "SOC_I2C_FIFO_LEN": 32, "SOC_I2C_NUM": 2, "SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE": true, "SOC_I2C_SLAVE_SUPPORT_BROADCAST": true, "SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS": true, "SOC_I2C_SUPPORTED": true, "SOC_I2C_SUPPORT_10BIT_ADDR": true, "SOC_I2C_SUPPORT_HW_CLR_BUS": true, "SOC_I2C_SUPPORT_RTC": true, "SOC_I2C_SUPPORT_SLAVE": true, "SOC_I2C_SUPPORT_XTAL": true, "SOC_I2S_HW_VERSION_2": true, "SOC_I2S_NUM": 2, "SOC_I2S_PDM_MAX_RX_LINES": 4, "SOC_I2S_PDM_MAX_TX_LINES": 2, "SOC_I2S_SUPPORTED": true, "SOC_I2S_SUPPORTS_PCM": true, "SOC_I2S_SUPPORTS_PDM": true, "SOC_I2S_SUPPORTS_PDM_RX": true, "SOC_I2S_SUPPORTS_PDM_TX": true, "SOC_I2S_SUPPORTS_PLL_F160M": true, "SOC_I2S_SUPPORTS_TDM": true, "SOC_I2S_SUPPORTS_XTAL": true, "SOC_LCDCAM_I80_BUS_WIDTH": 16, "SOC_LCDCAM_I80_LCD_SUPPORTED": true, "SOC_LCDCAM_I80_NUM_BUSES": 1, "SOC_LCDCAM_RGB_DATA_WIDTH": 16, "SOC_LCDCAM_RGB_LCD_SUPPORTED": true, "SOC_LCDCAM_RGB_NUM_PANELS": 1, "SOC_LCDCAM_SUPPORTED": true, "SOC_LCD_I80_BUSES": 1, "SOC_LCD_I80_BUS_WIDTH": 16, "SOC_LCD_I80_SUPPORTED": true, "SOC_LCD_RGB_DATA_WIDTH": 16, "SOC_LCD_RGB_PANELS": 1, "SOC_LCD_RGB_SUPPORTED": true, "SOC_LCD_SUPPORT_RGB_YUV_CONV": true, "SOC_LEDC_CHANNEL_NUM": 8, "SOC_LEDC_SUPPORTED": true, "SOC_LEDC_SUPPORT_APB_CLOCK": true, "SOC_LEDC_SUPPORT_FADE_STOP": true, "SOC_LEDC_SUPPORT_XTAL_CLOCK": true, "SOC_LEDC_TIMER_BIT_WIDTH": 14, "SOC_LEDC_TIMER_NUM": 4, "SOC_LIGHT_SLEEP_SUPPORTED": true, "SOC_LP_IO_CLOCK_IS_INDEPENDENT": true, "SOC_LP_PERIPH_SHARE_INTERRUPT": true, "SOC_LP_TIMER_BIT_WIDTH_HI": 16, "SOC_LP_TIMER_BIT_WIDTH_LO": 32, "SOC_MAC_BB_PD_MEM_SIZE": 192, "SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER": 3, "SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP": true, "SOC_MCPWM_COMPARATORS_PER_OPERATOR": 2, "SOC_MCPWM_GENERATORS_PER_OPERATOR": 2, "SOC_MCPWM_GPIO_FAULTS_PER_GROUP": 3, "SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP": 3, "SOC_MCPWM_GROUPS": 2, "SOC_MCPWM_OPERATORS_PER_GROUP": 3, "SOC_MCPWM_SUPPORTED": true, "SOC_MCPWM_SWSYNC_CAN_PROPAGATE": true, "SOC_MCPWM_TIMERS_PER_GROUP": 3, "SOC_MCPWM_TRIGGERS_PER_OPERATOR": 2, "SOC_MEMPROT_CPU_PREFETCH_PAD_SIZE": 16, "SOC_MEMPROT_MEM_ALIGN_SIZE": 256, "SOC_MEMPROT_SUPPORTED": true, "SOC_MEMSPI_CORE_CLK_SHARED_WITH_PSRAM": true, "SOC_MEMSPI_IS_INDEPENDENT": true, "SOC_MEMSPI_SRC_FREQ_120M": true, "SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED": true, "SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED": true, "SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED": true, "SOC_MEMSPI_TIMING_TUNING_BY_MSPI_DELAY": true, "SOC_MMU_LINEAR_ADDRESS_REGION_NUM": 1, "SOC_MMU_PERIPH_NUM": 1, "SOC_MPI_MEM_BLOCKS_NUM": 4, "SOC_MPI_OPERATIONS_NUM": 3, "SOC_MPI_SUPPORTED": true, "SOC_MPU_MIN_REGION_SIZE": 536870912, "SOC_MPU_REGIONS_MAX_NUM": 8, "SOC_MPU_SUPPORTED": true, "SOC_PCNT_CHANNELS_PER_UNIT": 2, "SOC_PCNT_GROUPS": 1, "SOC_PCNT_SUPPORTED": true, "SOC_PCNT_THRES_POINT_PER_UNIT": 2, "SOC_PCNT_UNITS_PER_GROUP": 4, "SOC_PHY_COMBO_MODULE": true, "SOC_PHY_DIG_REGS_MEM_SIZE": 21, "SOC_PHY_SUPPORTED": true, "SOC_PM_CPU_RETENTION_BY_RTCCNTL": true, "SOC_PM_MODEM_PD_BY_SW": true, "SOC_PM_MODEM_RETENTION_BY_BACKUPDMA": true, "SOC_PM_SUPPORTED": true, "SOC_PM_SUPPORT_BT_WAKEUP": true, "SOC_PM_SUPPORT_CPU_PD": true, "SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY": true, "SOC_PM_SUPPORT_EXT0_WAKEUP": true, "SOC_PM_SUPPORT_EXT1_WAKEUP": true, "SOC_PM_SUPPORT_EXT_WAKEUP": true, "SOC_PM_SUPPORT_MAC_BB_PD": true, "SOC_PM_SUPPORT_MODEM_PD": true, "SOC_PM_SUPPORT_RC_FAST_PD": true, "SOC_PM_SUPPORT_RTC_PERIPH_PD": true, "SOC_PM_SUPPORT_TAGMEM_PD": true, "SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP": true, "SOC_PM_SUPPORT_VDDSDIO_PD": true, "SOC_PM_SUPPORT_WIFI_WAKEUP": true, "SOC_PSRAM_DMA_CAPABLE": true, "SOC_RISCV_COPROC_SUPPORTED": true, "SOC_RMT_CHANNELS_PER_GROUP": 8, "SOC_RMT_GROUPS": 1, "SOC_RMT_MEM_WORDS_PER_CHANNEL": 48, "SOC_RMT_RX_CANDIDATES_PER_GROUP": 4, "SOC_RMT_SUPPORTED": true, "SOC_RMT_SUPPORT_APB": true, "SOC_RMT_SUPPORT_DMA": true, "SOC_RMT_SUPPORT_RC_FAST": true, "SOC_RMT_SUPPORT_RX_DEMODULATION": true, "SOC_RMT_SUPPORT_RX_PINGPONG": true, "SOC_RMT_SUPPORT_TX_ASYNC_STOP": true, "SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY": true, "SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP": true, "SOC_RMT_SUPPORT_TX_LOOP_COUNT": true, "SOC_RMT_SUPPORT_TX_SYNCHRO": true, "SOC_RMT_SUPPORT_XTAL": true, "SOC_RMT_TX_CANDIDATES_PER_GROUP": 4, "SOC_RNG_SUPPORTED": true, "SOC_RSA_MAX_BIT_LEN": 4096, "SOC_RTCIO_HOLD_SUPPORTED": true, "SOC_RTCIO_INPUT_OUTPUT_SUPPORTED": true, "SOC_RTCIO_PIN_COUNT": 22, "SOC_RTCIO_WAKE_SUPPORTED": true, "SOC_RTC_CNTL_CPU_PD_DMA_BUS_WIDTH": 128, "SOC_RTC_CNTL_CPU_PD_REG_FILE_NUM": 549, "SOC_RTC_CNTL_TAGMEM_PD_DMA_BUS_WIDTH": 128, "SOC_RTC_FAST_MEM_SUPPORTED": true, "SOC_RTC_MEM_SUPPORTED": true, "SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256": true, "SOC_RTC_SLOW_MEM_SUPPORTED": true, "SOC_SDMMC_DELAY_PHASE_NUM": 4, "SOC_SDMMC_HOST_SUPPORTED": true, "SOC_SDMMC_NUM_SLOTS": 2, "SOC_SDMMC_SUPPORT_XTAL_CLOCK": true, "SOC_SDMMC_USE_GPIO_MATRIX": true, "SOC_SDM_CHANNELS_PER_GROUP": 8, "SOC_SDM_CLK_SUPPORT_APB": true, "SOC_SDM_GROUPS": true, "SOC_SDM_SUPPORTED": true, "SOC_SECURE_BOOT_SUPPORTED": true, "SOC_SECURE_BOOT_V2_RSA": true, "SOC_SHA_DMA_MAX_BUFFER_SIZE": 3968, "SOC_SHA_GDMA": true, "SOC_SHA_SUPPORTED": true, "SOC_SHA_SUPPORT_DMA": true, "SOC_SHA_SUPPORT_RESUME": true, "SOC_SHA_SUPPORT_SHA1": true, "SOC_SHA_SUPPORT_SHA224": true, "SOC_SHA_SUPPORT_SHA256": true, "SOC_SHA_SUPPORT_SHA384": true, "SOC_SHA_SUPPORT_SHA512": true, "SOC_SHA_SUPPORT_SHA512_224": true, "SOC_SHA_SUPPORT_SHA512_256": true, "SOC_SHA_SUPPORT_SHA512_T": true, "SOC_SIMD_INSTRUCTION_SUPPORTED": true, "SOC_SIMD_PREFERRED_DATA_ALIGNMENT": 16, "SOC_SPIRAM_SUPPORTED": true, "SOC_SPIRAM_XIP_SUPPORTED": true, "SOC_SPI_FLASH_SUPPORTED": true, "SOC_SPI_MAXIMUM_BUFFER_SIZE": 64, "SOC_SPI_MAX_CS_NUM": 6, "SOC_SPI_MAX_PRE_DIVIDER": 16, "SOC_SPI_MEM_SUPPORT_AUTO_RESUME": true, "SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND": true, "SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE": true, "SOC_SPI_MEM_SUPPORT_CACHE_32BIT_ADDR_MAP": true, "SOC_SPI_MEM_SUPPORT_CONFIG_GPIO_BY_EFUSE": true, "SOC_SPI_MEM_SUPPORT_OPI_MODE": true, "SOC_SPI_MEM_SUPPORT_SW_SUSPEND": true, "SOC_SPI_MEM_SUPPORT_TIMING_TUNING": true, "SOC_SPI_MEM_SUPPORT_WRAP": true, "SOC_SPI_PERIPH_NUM": 3, "SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT": true, "SOC_SPI_SCT_BUFFER_NUM_MAX": true, "SOC_SPI_SCT_CONF_BITLEN_MAX": 262138, "SOC_SPI_SCT_REG_NUM": 14, "SOC_SPI_SCT_SUPPORTED": true, "SOC_SPI_SLAVE_SUPPORT_SEG_TRANS": true, "SOC_SPI_SUPPORT_CD_SIG": true, "SOC_SPI_SUPPORT_CLK_APB": true, "SOC_SPI_SUPPORT_CLK_XTAL": true, "SOC_SPI_SUPPORT_CONTINUOUS_TRANS": true, "SOC_SPI_SUPPORT_DDRCLK": true, "SOC_SPI_SUPPORT_OCT": true, "SOC_SPI_SUPPORT_SLAVE_HD_VER2": true, "SOC_SUPPORTS_SECURE_DL_MODE": true, "SOC_SUPPORT_COEXISTENCE": true, "SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY": true, "SOC_SYSTIMER_ALARM_MISS_COMPENSATE": true, "SOC_SYSTIMER_ALARM_NUM": 3, "SOC_SYSTIMER_BIT_WIDTH_HI": 20, "SOC_SYSTIMER_BIT_WIDTH_LO": 32, "SOC_SYSTIMER_COUNTER_NUM": 2, "SOC_SYSTIMER_FIXED_DIVIDER": true, "SOC_SYSTIMER_INT_LEVEL": true, "SOC_SYSTIMER_SUPPORTED": true, "SOC_TEMPERATURE_SENSOR_SUPPORT_FAST_RC": true, "SOC_TEMP_SENSOR_SUPPORTED": true, "SOC_TIMER_GROUPS": 2, "SOC_TIMER_GROUP_COUNTER_BIT_WIDTH": 54, "SOC_TIMER_GROUP_SUPPORT_APB": true, "SOC_TIMER_GROUP_SUPPORT_XTAL": true, "SOC_TIMER_GROUP_TIMERS_PER_GROUP": 2, "SOC_TIMER_GROUP_TOTAL_TIMERS": 4, "SOC_TOUCH_PROXIMITY_CHANNEL_NUM": 3, "SOC_TOUCH_PROXIMITY_MEAS_DONE_SUPPORTED": true, "SOC_TOUCH_SAMPLE_CFG_NUM": 1, "SOC_TOUCH_SENSOR_NUM": 15, "SOC_TOUCH_SENSOR_SUPPORTED": true, "SOC_TOUCH_SENSOR_VERSION": 2, "SOC_TOUCH_SUPPORT_PROX_SENSING": true, "SOC_TOUCH_SUPPORT_SLEEP_WAKEUP": true, "SOC_TOUCH_SUPPORT_WATERPROOF": true, "SOC_TWAI_BRP_MAX": 16384, "SOC_TWAI_BRP_MIN": 2, "SOC_TWAI_CLK_SUPPORT_APB": true, "SOC_TWAI_CONTROLLER_NUM": 1, "SOC_TWAI_SUPPORTED": true, "SOC_TWAI_SUPPORTS_RX_STATUS": true, "SOC_UART_BITRATE_MAX": 5000000, "SOC_UART_FIFO_LEN": 128, "SOC_UART_HP_NUM": 3, "SOC_UART_NUM": 3, "SOC_UART_SUPPORTED": true, "SOC_UART_SUPPORT_APB_CLK": true, "SOC_UART_SUPPORT_FSM_TX_WAIT_SEND": true, "SOC_UART_SUPPORT_RTC_CLK": true, "SOC_UART_SUPPORT_WAKEUP_INT": true, "SOC_UART_SUPPORT_XTAL_CLK": true, "SOC_ULP_FSM_SUPPORTED": true, "SOC_ULP_HAS_ADC": true, "SOC_ULP_SUPPORTED": true, "SOC_USB_OTG_PERIPH_NUM": 1, "SOC_USB_OTG_SUPPORTED": true, "SOC_USB_SERIAL_JTAG_SUPPORTED": true, "SOC_WDT_SUPPORTED": true, "SOC_WIFI_CSI_SUPPORT": true, "SOC_WIFI_FTM_SUPPORT": true, "SOC_WIFI_GCMP_SUPPORT": true, "SOC_WIFI_HW_TSF": true, "SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH": 12, "SOC_WIFI_MESH_SUPPORT": true, "SOC_WIFI_PHY_NEEDS_USB_WORKAROUND": true, "SOC_WIFI_SUPPORTED": true, "SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW": true, "SOC_WIFI_WAPI_SUPPORT": true, "SOC_XTAL_SUPPORT_40M": true, "SOC_XT_WDT_SUPPORTED": true, "SPI_FLASH_AUTO_SUSPEND": false, "SPI_FLASH_BROWNOUT_RESET": true, "SPI_FLASH_BROWNOUT_RESET_XMC": true, "SPI_FLASH_BYPASS_BLOCK_ERASE": false, "SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED": false, "SPI_FLASH_DANGEROUS_WRITE_ABORTS": true, "SPI_FLASH_DANGEROUS_WRITE_ALLOWED": false, "SPI_FLASH_DANGEROUS_WRITE_FAILS": false, "SPI_FLASH_ENABLE_COUNTERS": false, "SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE": true, "SPI_FLASH_ERASE_YIELD_DURATION_MS": 20, "SPI_FLASH_ERASE_YIELD_TICKS": 1, "SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND": false, "SPI_FLASH_HPM_AUTO": true, "SPI_FLASH_HPM_DC_AUTO": true, "SPI_FLASH_HPM_DC_DISABLE": false, "SPI_FLASH_HPM_DIS": false, "SPI_FLASH_HPM_ENA": false, "SPI_FLASH_HPM_ON": true, "SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST": false, "SPI_FLASH_ROM_DRIVER_PATCH": true, "SPI_FLASH_ROM_IMPL": false, "SPI_FLASH_SIZE_OVERRIDE": false, "SPI_FLASH_SUPPORT_BOYA_CHIP": true, "SPI_FLASH_SUPPORT_GD_CHIP": true, "SPI_FLASH_SUPPORT_ISSI_CHIP": true, "SPI_FLASH_SUPPORT_MXIC_CHIP": true, "SPI_FLASH_SUPPORT_MXIC_OPI_CHIP": true, "SPI_FLASH_SUPPORT_TH_CHIP": true, "SPI_FLASH_SUPPORT_WINBOND_CHIP": true, "SPI_FLASH_SUSPEND_TSUS_VAL_US": 50, "SPI_FLASH_VENDOR_BOYA_SUPPORTED": true, "SPI_FLASH_VENDOR_GD_SUPPORTED": true, "SPI_FLASH_VENDOR_ISSI_SUPPORTED": true, "SPI_FLASH_VENDOR_MXIC_SUPPORTED": true, "SPI_FLASH_VENDOR_TH_SUPPORTED": true, "SPI_FLASH_VENDOR_WINBOND_SUPPORTED": true, "SPI_FLASH_VENDOR_XMC_SUPPORTED": true, "SPI_FLASH_VERIFY_WRITE": false, "SPI_FLASH_WRITE_CHUNK_SIZE": 8000, "SPI_FLASH_YIELD_DURING_ERASE": true, "XTAL_FREQ": 40, "XTAL_FREQ_40": true}