# ninja log v6
168	3448	7750729721912999	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	b5e87001bc69fec4
36	3599	7750729720590721	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	f83e44f21f901685
105	3939	7750729721281324	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	c2e49dae8332393a
497	4416	7750729725201717	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	75983807d0c6550a
248	5127	7750729722712503	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	c682aee1daebbb5e
347	5384	7750729723702340	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	dd6239af6335d820
1637	5686	7750729736599643	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	887ad8966bf87f20
1249	6039	7750729732721676	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	a7f3483c8f075d0a
1866	6321	7750729738888082	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	9869316161f1e42a
1428	6639	7750729734509571	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	2bc42ed5747bc341
3600	7563	7750729756233927	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	7d185c20b1fdc6cf
3453	7697	7750729754762747	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_longjmp.S.obj	e5d10a1737688e3
3941	7910	7750729759644305	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	60a559932a62cb97
4417	8168	7750729764402071	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_esp32s2_esp32s3.c.obj	5e5d6cab9b7f4366
5128	8491	7750729771500899	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_cache_writeback_esp32s3.S.obj	ba83749ffc209caf
6040	9298	7750729780628165	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	eedb715fc52d1998
5687	9708	7750729777107994	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	a899fb33ddbd2df4
6639	9917	7750729786626450	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	bf8515f08e6b30b1
6321	10123	7750729783451263	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/esp_cpu_intr.c.obj	e73e0cbf31510e2c
7564	10324	7750729795851595	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/cpu_region_protect.c.obj	61ebb76299390e73
5384	10766	7750729774077440	esp-idf/log/liblog.a	e54b20c7fa2964d7
7911	11043	7750729799338583	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk_init.c.obj	fd28e15f7791601
7698	11260	7750729797213493	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_clk.c.obj	5b443093f80d2fcd
8491	11966	7750729805138957	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_sleep.c.obj	c0bf79a33a3a966c
8168	12153	7750729801909298	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_init.c.obj	48ea26fbb7353a17
9298	12700	7750729813214400	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/rtc_time.c.obj	4bfe0e7c38df55ad
9709	13145	7750729817326993	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32s3/chip_info.c.obj	a3d1c232ec156460
9917	13397	7750729819401816	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	bffb6e4346a6f891
10123	13577	7750729821461438	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_table.c.obj	b17df1134d02787
10325	13742	7750729823456607	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_fields.c.obj	1298ec82f28650dd
11044	14450	7750729830670947	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_rtc_calib.c.obj	b43ecd807a7fe6e1
11260	14548	7750729832828192	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32s3/esp_efuse_utility.c.obj	984f45d1bdd858ed
11967	15681	7750729839894789	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	2e673cb66c8c557
12154	16429	7750729841764119	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	4965e824ce0d69f6
12700	16607	7750729847237156	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	ae97c3779b25d814
10766	16753	7750729827887797	esp-idf/esp_rom/libesp_rom.a	805d24816b3a60c9
13145	17046	7750729851685297	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	61f45e6509e1486a
13742	17390	7750729857653185	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	e1a9586b680c3c5f
13398	17766	7750729854210933	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	143c0cb5c4772aaa
14549	17896	7750729865725068	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	a463245cca6390ec
13577	18067	7750729856003715	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	eaa1d9f30df50085
14450	18190	7750729864743942	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	2361422639f63810
15682	18903	7750729877044471	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	ae6c203d51eef8b1
16430	19730	7750729884529941	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	71b9743d9ed5da87
16607	19982	7750729886306525	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	a30498979c3f4366
17047	20285	7750729890705745	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32s3.c.obj	9df60cefedf39ea3
17390	21381	7750729894136810	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	2a6da2106c2a3108
17896	21594	7750729899194726	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32s3.c.obj	7ad56293ddb9a5d6
16754	21789	7750729887762750	esp-idf/esp_common/libesp_common.a	b71236a9bcb917c9
17767	21940	7750729897905091	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	6f918bcff2a8774d
18191	22098	7750729902141476	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	a32df1f5c33d9256
18067	22664	7750729900896527	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	1a9c882ae89fc2b4
19982	23019	7750729920053553	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	5c7c7ccbdeb9e007
18904	23299	7750729909269045	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	e87f461648b0b1f6
19731	23481	7750729917540854	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	8d4a4faddd03c48d
20285	23661	7750729923077788	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	2b591bacdc03593f
21382	24458	7750729934061505	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	62b3cb3af83bf7c4
21595	24705	7750729936181100	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_sha.c.obj	d94e33c62e2bcda0
21940	24984	7750729939645951	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_soc.c.obj	479687e421f1e72d
22665	25920	7750729946881679	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	fc54b7091d361441
23020	26378	7750729950408868	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	44d2ddf6416cfa03
23482	26770	7750729955044618	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	58c9cc4c7ebc503a
22098	27047	7750729941222163	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32s3/bootloader_esp32s3.c.obj	269406c78723102f
23300	27421	7750729953222177	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	da612cb17c158e8f
23662	27804	7750729956853346	esp-idf/hal/CMakeFiles/__idf_hal.dir/mpu_hal.c.obj	eaf5bc897b3f041a
24458	27924	7750729964818696	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	d9590d63968d6166
24705	28345	7750729967286912	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32s3/efuse_hal.c.obj	19587ff2692ded53
21789	28539	7750729938116097	esp-idf/esp_hw_support/libesp_hw_support.a	b3e17a15a8dc9385
24985	28754	7750729970077009	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	72e5bc03abc51642
25920	29602	7750729979431246	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	a88c4ecbca3c5fe5
26770	30012	7750729987932588	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	75404385d42b11f4
27049	30177	7750729990729373	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	e17d90a7cd874f0e
27422	30410	7750729994456189	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/interrupts.c.obj	e2764f1ff992994b
27805	30561	7750729998279839	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gpio_periph.c.obj	999b773e11e29e28
27924	30719	7750729999469490	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/uart_periph.c.obj	35a22860f0b4bf73
28346	30983	7750730003686997	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/adc_periph.c.obj	7dd7c130e614c583
28755	31165	7750730007784763	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/dedic_gpio_periph.c.obj	a0809e503ef67c7f
26378	31756	7750729984007991	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	347040af540d0376
29603	32192	7750730016254209	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/gdma_periph.c.obj	571566a91022656b
30013	32765	7750730020364880	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/spi_periph.c.obj	b09220aabc2aef7d
30411	32907	7750730024341129	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/pcnt_periph.c.obj	314c349a4af25f75
30719	33085	7750730027435746	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdm_periph.c.obj	76bb0d5d8f7c150a
30178	33217	7750730022005664	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/ledc_periph.c.obj	faa4e2a4eb9342e0
30561	33465	7750730025847153	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rmt_periph.c.obj	a38a4ab26e1046f2
30984	33703	7750730030071585	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2s_periph.c.obj	371b7ab80dd4abab
28539	33853	7750730005619869	esp-idf/esp_system/libesp_system.a	24bb0c2fe6a0c356
31166	34019	7750730031885910	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/i2c_periph.c.obj	c789c90c18fa154d
31756	34274	7750730037789555	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/temperature_sensor_periph.c.obj	5cbb9b9c4ca71c65
32192	34969	7750730042155822	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/timer_periph.c.obj	d5e7d1b185545051
32766	35108	7750730047898121	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/lcd_periph.c.obj	3452635f0fabe6a8
32907	35228	7750730049310877	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mcpwm_periph.c.obj	d012e1b6cecb7fab
33085	35780	7750730051082243	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/mpi_periph.c.obj	75d5ddb1643d2728
33703	35997	7750730057266205	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/twai_periph.c.obj	19a739fcab5aa2f5
33465	36240	7750730054894327	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/touch_sensor_periph.c.obj	ed7148953d48bc5d
33217	36243	7750730052873229	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/sdmmc_periph.c.obj	fd38888be186abab
34019	37041	7750730060425275	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/wdt_periph.c.obj	40035d70a4af072f
34275	37494	7750730062978947	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/usb_dwc_periph.c.obj	b7f14fb0075c618f
35780	37589	7750730089267202	project_elf_src_esp32s3.c	fe633a5f4d72cd2d
35780	37589	7750730089267202	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/project_elf_src_esp32s3.c	fe633a5f4d72cd2d
35109	37742	7750730071324886	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/eri.c.obj	ba52318d24e50e57
34970	38032	7750730069942112	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32s3/rtc_io_periph.c.obj	cd459680db3b1a8
35228	38160	7750730072516970	esp-idf/xtensa/CMakeFiles/__idf_xtensa.dir/xt_trax.c.obj	1f7cd98b26682029
35998	38459	7750730080207988	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	80ec3e78c0894427
33853	38575	7750730058765641	esp-idf/efuse/libefuse.a	3c1c1105f6217f7f
37589	38952	7750730096123039	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32s3.c.obj	9519b40c4f9ec32
38575	40834	7750730105984786	esp-idf/bootloader_support/libbootloader_support.a	49c58ff06b01234d
40834	43001	7750730128575858	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	3a9c204458b8a058
43001	46628	7750730150236759	esp-idf/spi_flash/libspi_flash.a	af13735a3c836284
46628	50544	7750730186514515	esp-idf/hal/libhal.a	60eb6124c554fe19
50544	54421	7750730225671454	esp-idf/micro-ecc/libmicro-ecc.a	db7c16ad8cac7493
54422	59090	7750730264445636	esp-idf/soc/libsoc.a	fbb706948c61c9c9
59090	62762	7750730311133967	esp-idf/xtensa/libxtensa.a	18ee135e1db9edc2
62762	67813	7750730347858631	esp-idf/main/libmain.a	a1fa28df99b21283
67813	72803	7750730398359042	bootloader.elf	85e92948e5aa165f
72804	79043	7750730501237479	.bin_timestamp	6821153c18a480a8
72804	79043	7750730501237479	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/.bin_timestamp	6821153c18a480a8
79044	82806	7750730510670748	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
79044	82806	7750730510670748	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
64	1910	7750732511099271	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
64	1910	7750732511099271	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
44	1134	7750735132075797	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
44	1134	7750735132075797	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
66	1573	7750736400208699	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
66	1573	7750736400208699	D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	e6feb8641ee77c4f
