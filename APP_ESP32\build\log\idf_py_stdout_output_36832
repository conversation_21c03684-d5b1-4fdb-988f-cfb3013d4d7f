[1/5] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/partition_table/partition-table.bin D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/Vantage_nxESP32_Int_Rel_1_2_1_0.bin"
Vantage_nxESP32_Int_Rel_1_2_1_0.bin binary size 0x963b0 bytes. Smallest app partition is 0x100000 bytes. 0x69c50 bytes (41%) free.

[2/5] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.bin"

Bootloader binary size 0x5220 bytes. 0x2de0 bytes (36%) free.


[3/5] No install step for 'bootloader'
[4/5] Completed 'bootloader'
[4/5] C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\esp\v5.4.1\esp-idf\components\esptool_py && C:\Users\<USER>\.espressif\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Users/<USER>/esp/v5.4.1/esp-idf -D SERIAL_TOOL=C:/Users/<USER>/.espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe;;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build -P C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake"
esptool.py --chip esp32s3 -p COM33 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size detect 0x0 bootloader/bootloader.bin 0x10000 Vantage_nxESP32_Int_Rel_1_2_1_0.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.8.1
Serial port COM33
Connecting....
Chip is ESP32-S3 (QFN56) (revision v0.2)
Features: WiFi, BLE, Unknown Embedded PSRAM (AP_1v8)
Crystal is 40MHz
MAC: b4:3a:45:f7:39:08
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Auto-detected Flash size: 32MB
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x000a6fff...
Flash will be erased from 0x00008000 to 0x00008fff...
Flash params set to 0x025f
SHA digest in image updated
Compressed 21024 bytes to 13384...
Writing at 0x00000000... (100 %)
Wrote 21024 bytes (13384 compressed) at 0x00000000 in 0.6 seconds (effective 285.4 kbit/s)...
Hash of data verified.
Compressed 615344 bytes to 361691...
Writing at 0x00010000... (4 %)
Writing at 0x0001c9d8... (8 %)
Writing at 0x0002693d... (13 %)
Writing at 0x0002f920... (17 %)
Writing at 0x0003573e... (21 %)
Writing at 0x0003bfa0... (26 %)
Writing at 0x000421c6... (30 %)
Writing at 0x00047da5... (34 %)
Writing at 0x0004df0c... (39 %)
Writing at 0x00053f9e... (43 %)
Writing at 0x00059dfc... (47 %)
Writing at 0x0005f8cf... (52 %)
Writing at 0x00065035... (56 %)
Writing at 0x0006b455... (60 %)
Writing at 0x00071226... (65 %)
Writing at 0x0007766d... (69 %)
Writing at 0x0007cea1... (73 %)
Writing at 0x000834c3... (78 %)
Writing at 0x0008d900... (82 %)
Writing at 0x000938c3... (86 %)
Writing at 0x00099111... (91 %)
Writing at 0x0009f09b... (95 %)
Writing at 0x000a5cab... (100 %)
Wrote 615344 bytes (361691 compressed) at 0x00010000 in 10.0 seconds (effective 494.3 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 118...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (118 compressed) at 0x00008000 in 0.1 seconds (effective 477.3 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
