ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
Octal Flash Mode Enabled
For OPI Flash, Use Default Flash Boot Mode
mode:SLOW_RD, clock div:1
load:0x3fce2810,len:0x15a0
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0;32mI (33) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (33) boot: compile time Jul 24 2025 20:39:56[0m
[0;32mI (33) boot: Multicore bootloader[0m
[0;32mI (34) boot: chip revision: v0.2[0m
[0;32mI (36) boot: efuse block revision: v1.3[0m
[0;32mI (40) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (44) boot.esp32s3: SPI Mode       : SLOW READ[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 32MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (56) boot: Partition Table:[0m
[0;32mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (65) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (72) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (78) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (85) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18648h ( 99912) map[0m
[0;32mI (126) esp_image: segment 1: paddr=00028670 vaddr=3fc9b900 size=04a84h ( 19076) load[0m
[0;32mI (131) esp_image: segment 2: paddr=0002d0fc vaddr=40374000 size=02f1ch ( 12060) load[0m
[0;32mI (134) esp_image: segment 3: paddr=00030020 vaddr=42000020 size=61aa8h (400040) map[0m
[0;32mI (231) esp_image: segment 4: paddr=00091ad0 vaddr=40376f1c size=149c0h ( 84416) load[0m
[0;32mI (254) esp_image: segment 5: paddr=000a6498 vaddr=600fe100 size=0001ch (    28) load[0m
[0;32mI (263) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (264) boot: Disabling RNG early entropy source...[0m
[0;32mI (274) octal_psram: vendor id    : 0x0d (AP)[0m
[0;32mI (274) octal_psram: dev id       : 0x03 (generation 4)[0m
[0;32mI (274) octal_psram: density      : 0x05 (128 Mbit)[0m
[0;32mI (276) octal_psram: good-die     : 0x01 (Pass)[0m
[0;32mI (281) octal_psram: Latency      : 0x01 (Fixed)[0m
[0;32mI (285) octal_psram: VCC          : 0x00 (1.8V)[0m
[0;32mI (289) octal_psram: SRF          : 0x01 (Fast Refresh)[0m
[0;32mI (294) octal_psram: BurstType    : 0x01 (Hybrid Wrap)[0m
[0;32mI (299) octal_psram: BurstLen     : 0x01 (32 Byte)[0m
[0;32mI (304) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)[0m
[0;32mI (309) octal_psram: DriveStrength: 0x00 (1/1)[0m
[0;32mI (314) MSPI Timing: PSRAM timing tuning index: 6[0m
[0;32mI (317) esp_psram: Found 16MB PSRAM device[0m
[0;32mI (321) esp_psram: Speed: 80MHz[0m
[0;32mI (324) cpu_start: Multicore app[0m
[0;32mI (1179) esp_psram: SPI SRAM memory test OK[0m
[0;32mI (1196) cpu_start: Pro cpu start user code[0m
[0;32mI (1196) cpu_start: cpu freq: 240000000 Hz[0m
[0;32mI (1196) app_init: Application information:[0m
[0;32mI (1196) app_init: Project name:     Vantage_nxESP32_Int_Rel_1_2_1_0[0m
[0;32mI (1202) app_init: App version:      Vantage_Bin_nxESP32_1_2_0_0[0m
[0;32mI (1208) app_init: Compile time:     Jul 24 2025 20:38:22[0m
[0;32mI (1213) app_init: ELF file SHA256:  7fe8b3592...[0m
[0;32mI (1217) app_init: ESP-IDF:          v5.4.1[0m
[0;32mI (1221) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (1225) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (1229) efuse_init: Chip rev:         v0.2[0m
[0;32mI (1233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (1240) heap_init: At 3FCAA4E8 len 0003F228 (252 KiB): RAM[0m
[0;32mI (1245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (1250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (1255) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0;32mI (1261) esp_psram: Adding pool of 16121K of PSRAM memory to heap allocator[0m
[0;33mW (1268) spi_flash: Octal flash chip is using but dio mode is selected, will automatically switch to Octal mode[0m
[0;32mI (1277) spi_flash: detected chip: mxic (opi)[0m
[0;32mI (1281) spi_flash: flash io: opi_str[0m
[0;32mI (1285) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (1291) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (1298) coexist: coex firmware version: e727207[0m
[0;32mI (1311) coexist: coexist rom version e7ae62f[0m
[0;32mI (1311) main_task: Started on CPU0[0m
[0;32mI (1321) esp_psram: Reserving pool of 32K of internal memory for DMA/internal allocations[0m
[0;32mI (1321) main_task: Calling app_main()[0m
[0;32mI (1341) BLE_INIT: BT controller compile version [d74042a][0m
[0;32mI (1341) BLE_INIT: Bluetooth MAC: b4:3a:45:f7:39:0a[0m
[0;32mI (1341) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (1371) uart: queue free spaces: 80[0m
[0;32mI (1381) FLASH: : 0[0m
[0;32mI (1381) FLASH: : LittleFS mounted successfully at /storage[0m
[0;32mI (1381) HEAP: Free heap: 16688140[0m
[0;32mI (1381) FILE_HANDLE: : TASK CREATED[0m
[0;32mI (1381) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (1391) NimBLE: Device Address: [0m
[0;32mI (1391) NimBLE: b4:3a:45:f7:39:0a[0m
[0;32mI (1391) NimBLE: [0m

[0;32mI (1391) gpio: GPIO[10]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1401) gpio: GPIO[11]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1411) gpio: GPIO[13]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1421) gpio: GPIO[14]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1431) NimBLE_BLE_PRPH: BLE advertising state loaded from NVS: true[0m
[0;32mI (1431) MAIN: Resuming BLE advertising from NVS...[0m
[0;32mI (1441) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (1441) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (1451) NimBLE: disc_mode=2[0m
[0;32mI (1451) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (1461) NimBLE: [0m

[0;32mI (1461) main_task: Returned from app_main()[0m
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
Octal Flash Mode Enabled
For OPI Flash, Use Default Flash Boot Mode
mode:SLOW_RD, clock div:1
load:0x3fce2810,len:0x15a0
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0;32mI (33) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (33) boot: compile time Jul 24 2025 20:39:56[0m
[0;32mI (33) boot: Multicore bootloader[0m
[0;32mI (34) boot: chip revision: v0.2[0m
[0;32mI (36) boot: efuse block revision: v1.3[0m
[0;32mI (40) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (44) boot.esp32s3: SPI Mode       : SLOW READ[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 32MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (56) boot: Partition Table:[0m
[0;32mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (65) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (72) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (78) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (85) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18648h ( 99912) map[0m
[0;32mI (126) esp_image: segment 1: paddr=00028670 vaddr=3fc9b900 size=04a84h ( 19076) load[0m
[0;32mI (131) esp_image: segment 2: paddr=0002d0fc vaddr=40374000 size=02f1ch ( 12060) load[0m
[0;32mI (134) esp_image: segment 3: paddr=0003002