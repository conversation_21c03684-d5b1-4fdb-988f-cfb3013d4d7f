ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
Octal Flash Mode Enabled
For OPI Flash, Use Default Flash Boot Mode
mode:SLOW_RD, clock div:1
load:0x3fce2810,len:0x15a0
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0;32mI (33) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (33) boot: compile time Jul 24 2025 20:39:56[0m
[0;32mI (33) boot: Multicore bootloader[0m
[0;32mI (34) boot: chip revision: v0.2[0m
[0;32mI (36) boot: efuse block revision: v1.3[0m
[0;32mI (40) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (44) boot.esp32s3: SPI Mode       : SLOW READ[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 32MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (56) boot: Partition Table:[0m
[0;32mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (65) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (72) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (78) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (85) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18648h ( 99912) map[0m
[0;32mI (126) esp_image: segment 1: paddr=00028670 vaddr=3fc9b900 size=04a84h ( 19076) load[0m
[0;32mI (131) esp_image: segment 2: paddr=0002d0fc vaddr=40374000 size=02f1ch ( 12060) load[0m
[0;32mI (134) esp_image: segment 3: paddr=00030020 vaddr=42000020 size=61aa8h (400040) map[0m
[0;32mI (231) esp_image: segment 4: paddr=00091ad0 vaddr=40376f1c size=149c0h ( 84416) load[0m
[0;32mI (254) esp_image: segment 5: paddr=000a6498 vaddr=600fe100 size=0001ch (    28) load[0m
[0;32mI (263) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (264) boot: Disabling RNG early entropy source...[0m
[0;32mI (274) octal_psram: vendor id    : 0x0d (AP)[0m
[0;32mI (274) octal_psram: dev id       : 0x03 (generation 4)[0m
[0;32mI (274) octal_psram: density      : 0x05 (128 Mbit)[0m
[0;32mI (276) octal_psram: good-die     : 0x01 (Pass)[0m
[0;32mI (281) octal_psram: Latency      : 0x01 (Fixed)[0m
[0;32mI (285) octal_psram: VCC          : 0x00 (1.8V)[0m
[0;32mI (289) octal_psram: SRF          : 0x01 (Fast Refresh)[0m
[0;32mI (294) octal_psram: BurstType    : 0x01 (Hybrid Wrap)[0m
[0;32mI (299) octal_psram: BurstLen     : 0x01 (32 Byte)[0m
[0;32mI (304) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)[0m
[0;32mI (309) octal_psram: DriveStrength: 0x00 (1/1)[0m
[0;32mI (314) MSPI Timing: PSRAM timing tuning index: 6[0m
[0;32mI (317) esp_psram: Found 16MB PSRAM device[0m
[0;32mI (321) esp_psram: Speed: 80MHz[0m
[0;32mI (324) cpu_start: Multicore app[0m
[0;32mI (1179) esp_psram: SPI SRAM memory test OK[0m
[0;32mI (1196) cpu_start: Pro cpu start user code[0m
[0;32mI (1196) cpu_start: cpu freq: 240000000 Hz[0m
[0;32mI (1196) app_init: Application information:[0m
[0;32mI (1196) app_init: Project name:     Vantage_nxESP32_Int_Rel_1_2_1_0[0m
[0;32mI (1202) app_init: App version:      Vantage_Bin_nxESP32_1_2_0_0[0m
[0;32mI (1208) app_init: Compile time:     Jul 24 2025 20:38:22[0m
[0;32mI (1213) app_init: ELF file SHA256:  7fe8b3592...[0m
[0;32mI (1217) app_init: ESP-IDF:          v5.4.1[0m
[0;32mI (1221) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (1225) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (1229) efuse_init: Chip rev:         v0.2[0m
[0;32mI (1233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (1240) heap_init: At 3FCAA4E8 len 0003F228 (252 KiB): RAM[0m
[0;32mI (1245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (1250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (1255) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0;32mI (1261) esp_psram: Adding pool of 16121K of PSRAM memory to heap allocator[0m
[0;33mW (1268) spi_flash: Octal flash chip is using but dio mode is selected, will automatically switch to Octal mode[0m
[0;32mI (1277) spi_flash: detected chip: mxic (opi)[0m
[0;32mI (1281) spi_flash: flash io: opi_str[0m
[0;32mI (1285) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (1291) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (1298) coexist: coex firmware version: e727207[0m
[0;32mI (1311) coexist: coexist rom version e7ae62f[0m
[0;32mI (1311) main_task: Started on CPU0[0m
[0;32mI (1321) esp_psram: Reserving pool of 32K of internal memory for DMA/internal allocations[0m
[0;32mI (1321) main_task: Calling app_main()[0m
[0;32mI (1341) BLE_INIT: BT controller compile version [d74042a][0m
[0;32mI (1341) BLE_INIT: Bluetooth MAC: b4:3a:45:f7:39:0a[0m
[0;32mI (1341) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (1371) uart: queue free spaces: 80[0m
[0;32mI (1381) FLASH: : 0[0m
[0;32mI (1381) FLASH: : LittleFS mounted successfully at /storage[0m
[0;32mI (1381) HEAP: Free heap: 16688140[0m
[0;32mI (1381) FILE_HANDLE: : TASK CREATED[0m
[0;32mI (1381) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (1391) NimBLE: Device Address: [0m
[0;32mI (1391) NimBLE: b4:3a:45:f7:39:0a[0m
[0;32mI (1391) NimBLE: [0m

[0;32mI (1391) gpio: GPIO[10]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1401) gpio: GPIO[11]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1411) gpio: GPIO[13]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1421) gpio: GPIO[14]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1431) NimBLE_BLE_PRPH: BLE advertising state loaded from NVS: true[0m
[0;32mI (1431) MAIN: Resuming BLE advertising from NVS...[0m
[0;32mI (1441) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (1441) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (1451) NimBLE: disc_mode=2[0m
[0;32mI (1451) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (1461) NimBLE: [0m

[0;32mI (1461) main_task: Returned from app_main()[0m
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
Octal Flash Mode Enabled
For OPI Flash, Use Default Flash Boot Mode
mode:SLOW_RD, clock div:1
load:0x3fce2810,len:0x15a0
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0;32mI (33) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (33) boot: compile time Jul 24 2025 20:39:56[0m
[0;32mI (33) boot: Multicore bootloader[0m
[0;32mI (34) boot: chip revision: v0.2[0m
[0;32mI (36) boot: efuse block revision: v1.3[0m
[0;32mI (40) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (44) boot.esp32s3: SPI Mode       : SLOW READ[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 32MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (56) boot: Partition Table:[0m
[0;32mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (65) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (72) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (78) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (85) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18648h ( 99912) map[0m
[0;32mI (126) esp_image: segment 1: paddr=00028670 vaddr=3fc9b900 size=04a84h ( 19076) load[0m
[0;32mI (131) esp_image: segment 2: paddr=0002d0fc vaddr=40374000 size=02f1ch ( 12060) load[0m
[0;32mI (134) esp_image: segment 3: paddr=00030020 vaddr=42000020 size=61aa8h (400040) map[0m
[0;32mI (231) esp_image: segment 4: paddr=00091ad0 vaddr=40376f1c size=149c0h ( 84416) load[0m
[0;32mI (254) esp_image: segment 5: paddr=000a6498 vaddr=600fe100 size=0001ch (    28) load[0m
[0;32mI (263) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (264) boot: Disabling RNG early entropy source...[0m
[0;32mI (274) octal_psram: vendor id    : 0x0d (AP)[0m
[0;32mI (274) octal_psram: dev id       : 0x03 (generation 4)[0m
[0;32mI (274) octal_psram: density      : 0x05 (128 Mbit)[0m
[0;32mI (276) octal_psram: good-die     : 0x01 (Pass)[0m
[0;32mI (281) octal_psram: Latency      : 0x01 (Fixed)[0m
[0;32mI (285) octal_psram: VCC          : 0x00 (1.8V)[0m
[0;32mI (289) octal_psram: SRF          : 0x01 (Fast Refresh)[0m
[0;32mI (294) octal_psram: BurstType    : 0x01 (Hybrid Wrap)[0m
[0;32mI (299) octal_psram: BurstLen     : 0x01 (32 Byte)[0m
[0;32mI (304) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)[0m
[0;32mI (309) octal_psram: DriveStrength: 0x00 (1/1)[0m
[0;32mI (314) MSPI Timing: PSRAM timing tuning index: 6[0m
[0;32mI (317) esp_psram: Found 16MB PSRAM device[0m
[0;32mI (321) esp_psram: Speed: 80MHz[0m
[0;32mI (324) cpu_start: Multicore app[0m
[0;32mI (1179) esp_psram: SPI SRAM memory test OK[0m
[0;32mI (1196) cpu_start: Pro cpu start user code[0m
[0;32mI (1196) cpu_start: cpu freq: 240000000 Hz[0m
[0;32mI (1196) app_init: Application information:[0m
[0;32mI (1196) app_init: Project name:     Vantage_nxESP32_Int_Rel_1_2_1_0[0m
[0;32mI (1202) app_init: App version:      Vantage_Bin_nxESP32_1_2_0_0[0m
[0;32mI (1208) app_init: Compile time:     Jul 24 2025 20:38:22[0m
[0;32mI (1213) app_init: ELF file SHA256:  7fe8b3592...[0m
[0;32mI (1217) app_init: ESP-IDF:          v5.4.1[0m
[0;32mI (1221) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (1225) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (1229) efuse_init: Chip rev:         v0.2[0m
[0;32mI (1233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (1240) heap_init: At 3FCAA4E8 len 0003F228 (252 KiB): RAM[0m
[0;32mI (1245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (1250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (1255) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0;32mI (1261) esp_psram: Adding pool of 16121K of PSRAM memory to heap allocator[0m
[0;33mW (1268) spi_flash: Octal flash chip is using but dio mode is selected, will automatically switch to Octal mode[0m
[0;32mI (1277) spi_flash: detected chip: mxic (opi)[0m
[0;32mI (1281) spi_flash: flash io: opi_str[0m
[0;32mI (1285) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (1291) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (1298) coexist: coex firmware version: e727207[0m
[0;32mI (1311) coexist: coexist rom version e7ae62f[0m
[0;32mI (1311) main_task: Started on CPU0[0m
[0;32mI (1321) esp_psram: Reserving pool of 32K of internal memory for DMA/internal allocations[0m
[0;32mI (1321) main_task: Calling app_main()[0m
[0;32mI (1341) BLE_INIT: BT controller compile version [d74042a][0m
[0;32mI (1341) BLE_INIT: Bluetooth MAC: b4:3a:45:f7:39:0a[0m
[0;32mI (1341) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (1371) uart: queue free spaces: 80[0m
[0;32mI (1381) FLASH: : 0[0m
[0;32mI (1381) FLASH: : LittleFS mounted successfully at /storage[0m
[0;32mI (1381) HEAP: Free heap: 16688140[0m
[0;32mI (1381) FILE_HANDLE: : TASK CREATED[0m
[0;32mI (1381) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (1391) NimBLE: Device Address: [0m
[0;32mI (1391) NimBLE: b4:3a:45:f7:39:0a[0m
[0;32mI (1391) NimBLE: [0m

[0;32mI (1391) gpio: GPIO[10]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1401) gpio: GPIO[11]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1411) gpio: GPIO[13]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1421) gpio: GPIO[14]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1431) NimBLE_BLE_PRPH: BLE advertising state loaded from NVS: true[0m
[0;32mI (1431) MAIN: Resuming BLE advertising from NVS...[0m
[0;32mI (1441) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (1441) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (1451) NimBLE: disc_mode=2[0m
[0;32mI (1451) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (1461) NimBLE: [0m

[0;32mI (1461) main_task: Returned from app_main()[0m
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
Octal Flash Mode Enabled
For OPI Flash, Use Default Flash Boot Mode
mode:SLOW_RD, clock div:1
load:0x3fce2810,len:0x15a0
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0;32mI (33) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (33) boot: compile time Jul 24 2025 20:39:56[0m
[0;32mI (33) boot: Multicore bootloader[0m
[0;32mI (34) boot: chip revision: v0.2[0m
[0;32mI (36) boot: efuse block revision: v1.3[0m
[0;32mI (40) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (44) boot.esp32s3: SPI Mode       : SLOW READ[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 32MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (56) boot: Partition Table:[0m
[0;32mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (65) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (72) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (78) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (85) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18648h ( 99912) map[0m
[0;32mI (126) esp_image: segment 1: paddr=00028670 vaddr=3fc9b900 size=04a84h ( 19076) load[0m
[0;32mI (131) esp_image: segment 2: paddr=0002d0fc vaddr=40374000 size=02f1ch ( 12060) load[0m
[0;32mI (134) esp_image: segment 3: paddr=00030020 vaddr=42000020 size=61aa8h (400040) map[0m
[0;32mI (231) esp_image: segment 4: paddr=00091ad0 vaddr=40376f1c size=149c0h ( 84416) load[0m
[0;32mI (254) esp_image: segment 5: paddr=000a6498 vaddr=600fe100 size=0001ch (    28) load[0m
[0;32mI (263) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (264) boot: Disabling RNG early entropy source...[0m
[0;32mI (274) octal_psram: vendor id    : 0x0d (AP)[0m
[0;32mI (274) octal_psram: dev id       : 0x03 (generation 4)[0m
[0;32mI (274) octal_psram: density      : 0x05 (128 Mbit)[0m
[0;32mI (276) octal_psram: good-die     : 0x01 (Pass)[0m
[0;32mI (281) octal_psram: Latency      : 0x01 (Fixed)[0m
[0;32mI (285) octal_psram: VCC          : 0x00 (1.8V)[0m
[0;32mI (289) octal_psram: SRF          : 0x01 (Fast Refresh)[0m
[0;32mI (294) octal_psram: BurstType    : 0x01 (Hybrid Wrap)[0m
[0;32mI (299) octal_psram: BurstLen     : 0x01 (32 Byte)[0m
[0;32mI (304) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)[0m
[0;32mI (309) octal_psram: DriveStrength: 0x00 (1/1)[0m
[0;32mI (314) MSPI Timing: PSRAM timing tuning index: 6[0m
[0;32mI (317) esp_psram: Found 16MB PSRAM device[0m
[0;32mI (321) esp_psram: Speed: 80MHz[0m
[0;32mI (324) cpu_start: Multicore app[0m
[0;32mI (1179) esp_psram: SPI SRAM memory test OK[0m
[0;32mI (1196) cpu_start: Pro cpu start user code[0m
[0;32mI (1196) cpu_start: cpu freq: 240000000 Hz[0m
[0;32mI (1196) app_init: Application information:[0m
[0;32mI (1196) app_init: Project name:     Vantage_nxESP32_Int_Rel_1_2_1_0[0m
[0;32mI (1202) app_init: App version:      Vantage_Bin_nxESP32_1_2_0_0[0m
[0;32mI (1208) app_init: Compile time:     Jul 24 2025 20:38:22[0m
[0;32mI (1213) app_init: ELF file SHA256:  7fe8b3592...[0m
[0;32mI (1217) app_init: ESP-IDF:          v5.4.1[0m
[0;32mI (1221) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (1225) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (1229) efuse_init: Chip rev:         v0.2[0m
[0;32mI (1233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (1240) heap_init: At 3FCAA4E8 len 0003F228 (252 KiB): RAM[0m
[0;32mI (1245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (1250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (1255) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0;32mI (1261) esp_psram: Adding pool of 16121K of PSRAM memory to heap allocator[0m
[0;33mW (1268) spi_flash: Octal flash chip is using but dio mode is selected, will automatically switch to Octal mode[0m
[0;32mI (1277) spi_flash: detected chip: mxic (opi)[0m
[0;32mI (1281) spi_flash: flash io: opi_str[0m
[0;32mI (1285) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (1291) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (1298) coexist: coex firmware version: e727207[0m
[0;32mI (1311) coexist: coexist rom version e7ae62f[0m
[0;32mI (1311) main_task: Started on CPU0[0m
[0;32mI (1321) esp_psram: Reserving pool of 32K of internal memory for DMA/internal allocations[0m
[0;32mI (1321) main_task: Calling app_main()[0m
[0;32mI (1341) BLE_INIT: BT controller compile version [d74042a][0m
[0;32mI (1341) BLE_INIT: Bluetooth MAC: b4:3a:45:f7:39:0a[0m
[0;32mI (1341) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (1371) uart: queue free spaces: 80[0m
[0;32mI (1381) FLASH: : 0[0m
[0;32mI (1381) FLASH: : LittleFS mounted successfully at /storage[0m
[0;32mI (1381) HEAP: Free heap: 16688140[0m
[0;32mI (1381) FILE_HANDLE: : TASK CREATED[0m
[0;32mI (1381) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (1391) NimBLE: Device Address: [0m
[0;32mI (1391) NimBLE: b4:3a:45:f7:39:0a[0m
[0;32mI (1391) NimBLE: [0m

[0;32mI (1391) gpio: GPIO[10]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1401) gpio: GPIO[11]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1411) gpio: GPIO[13]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1421) gpio: GPIO[14]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1431) NimBLE_BLE_PRPH: BLE advertising state loaded from NVS: true[0m
[0;32mI (1431) MAIN: Resuming BLE advertising from NVS...[0m
[0;32mI (1441) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (1441) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (1451) NimBLE: disc_mode=2[0m
[0;32mI (1451) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (1461) NimBLE: [0m

[0;32mI (1461) main_task: Returned from app_main()[0m
[0;32mI (10931) UART: uart[1] event[0m
[0;32mI (10931) UART: uart rx break[0m
[0;32mI (10931) UART: uart[1] event[0m
[0;32mI (10931) UART: uart rx break[0m
[0;32mI (10931) UART: uart[1] event[0m
[0;32mI (10931) UART: uart rx break[0m
[0;32mI (10941) UART: uart[1] event[0m
[0;32mI (10941) UART: uart rx break[0m
[0;32mI (10941) UART: uart[1] event[0m
[0;32mI (10941) UART: uart rx break[0m
[0;32mI (10951) UART: uart[1] event[0m
[0;32mI (10951) UART: uart rx break[0m
[0;32mI (10951) UART: uart[1] event[0m
[0;32mI (10961) UART: uart rx break[0m
[0;32mI (10961) UART: uart[1] event[0m
[0;32mI (10961) UART: uart rx break[0m
[0;32mI (10961) UART: uart[1] event[0m
[0;32mI (10971) UART: uart rx break[0m
[0;32mI (10971) UART: uart[1] event[0m
[0;32mI (10971) UART: uart rx break[0m
[0;32mI (10971) UART: uart[1] event[0m
[0;32mI (10981) UART: uart rx break[0m
[0;32mI (10981) UART: uart[1] event[0m
[0;32mI (10981) UART: uart rx break[0m
[0;32mI (10991) UART: uart[1] event[0m
[0;32mI (10991) UART: uart rx break[0m
[0;32mI (10991) UART: uart[1] event[0m
[0;32mI (10991) UART: uart rx break[0m
[0;32mI (11001) UART: uart[1] event[0m
[0;32mI (11001) UART: uart rx break[0m
[0;32mI (11001) UART: uart[1] event[0m
[0;32mI (11001) UART: uart rx break[0m
[0;32mI (11011) UART: uart[1] event[0m
[0;32mI (11011) UART: uart rx break[0m
[0;32mI (11011) UART: uart[1] event[0m
[0;32mI (11011) UART: uart rx break[0m
[0;32mI (11021) UART: uart[1] event[0m
[0;32mI (11021) UART: uart rx break[0m
[0;32mI (11021) UART: uart[1] event[0m
[0;32mI (11031) UART: uart rx break[0m
[0;32mI (11031) UART: uart[1] event[0m
[0;32mI (11031) UART: uart rx break[0m
[0;32mI (11031) UART: uart[1] event[0m
[0;32mI (11041) UART: uart rx break[0m
[0;32mI (11041) UART: uart[1] event[0m
[0;32mI (11041) UART: uart rx break[0m
[0;32mI (11041) UART: uart[1] event[0m
[0;32mI (11051) UART: uart rx break[0m
[0;32mI (11051) UART: uart[1] event[0m
[0;32mI (11051) UART: uart rx break[0m
[0;32mI (11061) UART: uart[1] event[0m
[0;32mI (11061) UART: uart rx break[0m
[0;32mI (11061) UART: uart[1] event[0m
[0;32mI (11061) UART: uart rx break[0m
[0;32mI (11071) UART: uart[1] event[0m
[0;32mI (11071) UART: uart rx break[0m
[0;32mI (11071) UART: uart[1] event[0m
[0;32mI (11071) UART: uart rx break[0m
[0;32mI (11081) UART: uart[1] event[0m
[0;32mI (11081) UART: uart rx break[0m
[0;32mI (11081) UART: uart[1] event[0m
[0;32mI (11081) UART: uart rx break[0m
[0;32mI (11091) UART: uart[0] event[0m
[0;32mI (11091) UART_READ: : 0[0m
[0;32mI (11091) UART_READ: : UART_READ_START[0m
[0;32mI (11101) UART_READ: : ERROR[0m
[0;32mI (11101) UART READ: [LEN: ]: 65[0m
[0;32mI (11101) UART: uart[1] event[0m
[0;32mI (11101) UART: uart rx break[0m
[0;32mI (11111) UART: uart[1] event[0m
[0;32mI (11111) UART: uart rx break[0m
[0;32mI (11111) UART: uart[1] event[0m
[0;32mI (11111) UART: uart rx break[0m
[0;32mI (11121) UART: uart[1] event[0m
[0;32mI (11121) UART: uart rx break[0m
[0;32mI (11121) UART: uart[1] event[0m
[0;32mI (11131) UART: uart rx break[0m
[0;32mI (11131) UART: uart[1] event[0m
[0;32mI (11131) UART: uart rx break[0m
[0;32mI (11131) UART: uart[1] event[0m
[0;32mI (11141) UART: uart rx break[0m
[0;32mI (11141) UART: uart[1] event[0m
[0;32mI (11141) UART: uart rx break[0m
[0;32mI (11141) UART: uart[1] event[0m
[0;32mI (11151) UART: uart rx break[0m
[0;32mI (11151) UART: uart[1] event[0m
[0;32mI (11151) UART: uart rx break[0m
[0;32mI (11161) UART: uart[1] event[0m
[0;32mI (11161) UART: uart rx break[0m
[0;32mI (11161) UART: uart[1] event[0m
[0;32mI (11161) UART: uart rx break[0m
[0;32mI (11171) UART: uart[1] event[0m
[0;32mI (11171) UART: uart rx break[0m
[0;32mI (11171) UART: uart[1] event[0m
[0;32mI (11171) UART: uart rx break[0m
[0;32mI (11181) UART: uart[1] event[0m
[0;32mI (11181) UART: uart rx break[0m
[0;32mI (11181) UART: uart[1] event[0m
[0;32mI (11181) UART: uart rx break[0m
[0;32mI (11191) UART: uart[1] event[0m
[0;32mI (11191) UART: uart rx break[0m
[0;32mI (11191) UART: uart[0] event[0m
[0;32mI (11201) UART_READ: : 2[0m
[0;32mI (11201) UART_READ: : : UART_READ_END[0m
[0;32mI (11201) UART READ: [LEN: ]: 102[0m
[0;32mI (11201) UART: uart[1] event[0m
[0;32mI (11211) UART: uart rx break[0m
[0;32mI (11211) UART: uart[1] event[0m
[0;32mI (11211) UART: uart rx break[0m
[0;32mI (11221) UART: uart[1] event[0m
[0;32mI (11221) UART: uart rx break[0m
[0;32mI (11221) UART: uart[1] event[0m
[0;32mI (11221) UART: uart rx break[0m
[0;32mI (11231) UART: uart[1] event[0m
[0;32mI (11231) UART: uart rx break[0m
[0;32mI (11231) UART: uart[1] event[0m
[0;32mI (11231) UART: uart rx break[0m
[0;32mI (11241) UART: uart[1] event[0m
[0;32mI (11241) UART: uart rx break[0m
[0;32mI (11241) UART: uart[1] event[0m
[0;32mI (11241) UART: uart rx break[0m
[0;32mI (11251) UART: uart[1] event[0m
[0;32mI (11251) UART: uart rx break[0m
[0;32mI (11251) UART: uart[1] event[0m
[0;32mI (11261) UART: uart rx break[0m
[0;32mI (11261) UART: uart[1] event[0m
[0;32mI (11261) UART: uart rx break[0m
[0;32mI (11261) UART: uart[1] event[0m
[0;32mI (11271) UART: uart rx break[0m
[0;32mI (11271) UART: uart[1] event[0m
[0;32mI (11271) UART: uart rx break[0m
[0;32mI (11271) UART: uart[1] event[0m
[0;32mI (11281) UART: uart rx break[0m
[0;32mI (11281) UART: uart[1] event[0m
[0;32mI (11281) UART: uart rx break[0m
[0;32mI (11291) UART: uart[1] event[0m
[0;32mI (11291) UART: uart rx break[0m
[0;32mI (11291) UART: uart[1] event[0m
[0;32mI (11291) UART: uart rx break[0m
[0;32mI (11301) UART: uart[1] event[0m
[0;32mI (11301) UART: uart rx break[0m
[0;32mI (11301) UART: uart[1] event[0m
[0;32mI (11301) UART: uart rx break[0m
[0;32mI (11311) UART: uart[1] event[0m
[0;32mI (11311) UART: uart rx break[0m
[0;32mI (11311) UART: uart[1] event[0m
[0;32mI (11311) UART: uart rx break[0m
[0;32mI (11321) UART: uart[0] event[0m
[0;32mI (11321) UART_READ: : 2[0m
[0;32mI (11321) UART_READ: : : UART_READ_END[0m
[0;32mI (11331) UART READ: [LEN: ]: 137[0m
[0;32mI (11331) UART: uart[1] event[0m
[0;32mI (11331) UART: uart rx break[0m
[0;32mI (11331) UART: uart[1] event[0m
[0;32mI (11341) UART: uart rx break[0m
[0;32mI (11341) UART: uart[1] event[0m
[0;32mI (11341) UART: uart rx break[0m
[0;32mI (11351) UART: uart[1] event[0m
[0;32mI (11351) UART: uart rx break[0m
[0;32mI (11351) UART: uart[1] event[0m
[0;32mI (11351) UART: uart rx break[0m
[0;32mI (11361) UART: uart[1] event[0m
[0;32mI (11361) UART: uart rx break[0m
[0;32mI (11361) UART: uart[1] event[0m
[0;32mI (11361) UART: uart rx break[0m
[0;32mI (11371) UART: uart[1] event[0m
[0;32mI (11371) UART: uart rx break[0m
[0;32mI (11371) UART: uart[1] event[0m
[0;32mI (11371) UART: uart rx break[0m
[0;32mI (11381) UART: uart[1] event[0m
[0;32mI (11381) UART: uart rx break[0m
[0;32mI (11381) UART: uart[1] event[0m
[0;32mI (11391) UART: uart rx break[0m
[0;32mI (11391) UART: uart[1] event[0m
[0;32mI (11391) UART: uart rx break[0m
[0;32mI (11391) UART: uart[1] event[0m
[0;32mI (11401) UART: uart rx break[0m
[0;32mI (11401) UART: uart[1] event[0m
[0;32mI (11401) UART: uart rx break[0m
[0;32mI (11401) UART: uart[1] event[0m
[0;32mI (11411) UART: uart rx break[0m
[0;32mI (11411) UART: uart[1] event[0m
[0;32mI (11411) UART: uart rx break[0m
[0;32mI (11421) UART: uart[1] event[0m
[0;32mI (11421) UART: uart rx break[0m
[0;32mI (11421) UART: uart[1] event[0m
[0;32mI (11421) UART: uart rx break[0m
[0;32mI (11431) UART: uart[1] event[0m
[0;32mI (11431) UART: uart rx break[0m
[0;32mI (11431) UART: uart[1] event[0m
[0;32mI (11431) UART: uart rx break[0m
[0;32mI (11441) UART: uart[0] event[0m
[0;32mI (11441) UART_READ: : 2[0m
[0;32mI (11441) UART_READ: : : UART_READ_END[0m
[0;32mI (11441) UART READ: [LEN: ]: 177[0m
[0;32mI (11451) UART: uart[1] event[0m
[0;32mI (11451) UART: uart rx break[0m
[0;32mI (11451) UART: uart[1] event[0m
[0;32mI (11461) UART: uart rx break[0m
[0;32mI (11461) UART: uart[1] event[0m
[0;32mI (11461) UART: uart rx break[0m
[0;32mI (11461) UART: uart[1] event[0m
[0;32mI (11471) UART: uart rx break[0m
[0;32mI (11471) UART: uart[1] event[0m
[0;32mI (11471) UART: uart rx break[0m
[0;32mI (11471) UART: uart[1] event[0m
[0;32mI (11481) UART: uart rx break[0m
[0;32mI (11481) UART: uart[1] event[0m
[0;32mI (11481) UART: uart rx break[0m
[0;32mI (11491) UART: uart[1] event[0m
[0;32mI (11491) UART: uart rx break[0m
[0;32mI (11491) UART: uart[1] event[0m
[0;32mI (11491) UART: uart rx break[0m
[0;32mI (11501) UART: uart[0] event[0m
[0;32mI (11501) UART_READ: : 2[0m
[0;32mI (11501) UART_READ: : : UART_READ_END[0m
[0;32mI (11501) UART READ: [LEN: ]: 212[0m
[0;32mI (11511) UART: uart[1] event[0m
[0;32mI (11511) UART: uart rx break[0m
[0;32mI (11511) UART: uart[1] event[0m
[0;32mI (11521) UART: uart rx break[0m
[0;32mI (11521) UART: uart[1] event[0m
[0;32mI (11521) UART: uart rx break[0m
[0;32mI (11521) UART: uart[1] event[0m
[0;32mI (11531) UART: uart rx break[0m
[0;32mI (11531) UART: uart[1] event[0m
[0;32mI (11531) UART: uart rx break[0m
[0;32mI (11531) UART: uart[1] event[0m
[0;32mI (11541) UART: uart rx break[0m
[0;32mI (11541) UART: uart[1] event[0m
[0;32mI (11541) UART: uart rx break[0m
[0;32mI (11551) UART: uart[1] event[0m
[0;32mI (11551) UART: uart rx break[0m
[0;32mI (11551) UART: uart[1] event[0m
[0;32mI (11551) UART: uart rx break[0m
[0;32mI (11561) UART: uart[1] event[0m
[0;32mI (11561) UART: uart rx break[0m
[0;32mI (11561) UART: uart[1] event[0m
[0;32mI (11561) UART: uart rx break[0m
[0;32mI (11571) UART: uart[1] event[0m
[0;32mI (11571) UART: uart rx break[0m
[0;32mI (11571) UART: uart[1] event[0m
[0;32mI (11581) UART: uart rx break[0m
[0;32mI (11581) UART: uart[1] event[0m
[0;32mI (11581) UART: uart rx break[0m
[0;32mI (11581) UART: uart[1] event[0m
[0;32mI (11591) UART: uart rx break[0m
[0;32mI (11591) UART: uart[1] event[0m
[0;32mI (11591) UART: uart rx break[0m
[0;32mI (11591) UART: uart[1] event[0m
[0;32mI (11601) UART: uart rx break[0m
[0;32mI (11601) UART: uart[1] event[0m
[0;32mI (11601) UART: uart rx break[0m
[0;32mI (11601) UART: uart[1] event[0m
[0;32mI (11611) UART: uart rx break[0m
[0;32mI (11611) UART: uart[1] event[0m
[0;32mI (11611) UART: uart rx break[0m
[0;32mI (11621) UART: uart[1] event[0m
[0;32mI (11621) UART: uart rx break[0m
[0;32mI (11621) UART: uart[1] event[0m
[0;32mI (11621) UART: uart rx break[0m
[0;32mI (11631) UART: uart[1] event[0m
[0;32mI (11631) UART: uart rx break[0m
[0;32mI (11631) UART: uart[1] event[0m
[0;32mI (11631) UART: uart rx break[0m
[0;32mI (11641) UART: uart[1] event[0m
[0;32mI (11641) UART: uart rx break[0m
[0;32mI (11641) UART: uart[1] event[0m
[0;32mI (11651) UART: uart rx break[0m
[0;32mI (11651) UART: uart[1] event[0m
[0;32mI (11651) UART: uart rx break[0m
[0;32mI (11651) UART: uart[1] event[0m
[0;32mI (11661) UART: uart rx break[0m
[0;32mI (11661) UART: uart[1] event[0m
[0;32mI (11661) UART: uart rx break[0m
[0;32mI (11661) UART: uart[1] event[0m
[0;32mI (11671) UART: uart rx break[0m
[0;32mI (11671) UART: uart[1] event[0m
[0;32mI (11671) UART: uart rx break[0m
[0;32mI (11671) UART: uart[1] event[0m
[0;32mI (11681) UART: uart rx break[0m
[0;32mI (11681) UART: uart[1] event[0m
[0;32mI (11681) UART: uart rx break[0m
[0;32mI (11691) UART: uart[1] event[0m
[0;32mI (11691) UART: uart rx break[0m
[0;32mI (11691) UART: uart[1] event[0m
[0;32mI (11691) UART: uart rx break[0m
[0;32mI (11701) UART: uart[1] event[0m
[0;32mI (11701) UART: uart rx break[0m
[0;32mI (11701) UART: uart[1] event[0m
[0;32mI (11701) UART: uart rx break[0m
[0;32mI (11711) UART: uart[1] event[0m
[0;32mI (11711) UART: uart rx break[0m
[0;32mI (11711) UART: uart[1] event[0m
[0;32mI (11721) UART: uart rx break[0m
[0;32mI (11721) UART: uart[1] event[0m
[0;32mI (11721) UART: uart rx break[0m
[0;32mI (11721) UART: uart[1] event[0m
[0;32mI (11731) UART: uart rx break[0m
[0;32mI (11731) UART: uart[1] event[0m
[0;32mI (11731) UART: uart rx break[0m
[0;32mI (11731) UART: uart[1] event[0m
[0;32mI (11741) UART: uart rx break[0m
[0;32mI (11741) UART: uart[1] event[0m
[0;32mI (11741) UART: uart rx break[0m
[0;32mI (11741) UART: uart[1] event[0m
[0;32mI (11751) UART: uart rx break[0m
[0;32mI (11751) UART: uart[1] event[0m
[0;32mI (11751) UART: uart rx break[0m
[0;32mI (11761) UART: uart[1] event[0m
[0;32mI (11761) UART: uart rx break[0m
[0;32mI (11761) UART: uart[1] event[0m
[0;32mI (11761) UART: uart rx break[0m
[0;32mI (11771) UART: uart[1] event[0m
[0;32mI (11771) UART: uart rx break[0m
[0;32mI (11771) UART: uart[1] event[0m
[0;32mI (11771) UART: uart rx break[0m
[0;32mI (11781) UART: uart[1] event[0m
[0;32mI (11781) UART: uart rx break[0m
[0;32mI (11781) UART: uart[1] event[0m
[0;32mI (11791) UART: uart rx break[0m
[0;32mI (11791) UART: uart[1] event[0m
[0;32mI (11791) UART: uart rx break[0m
[0;32mI (11791) UART: uart[1] event[0m
[0;32mI (11801) UART: uart rx break[0m
[0;32mI (11801) UART: uart[1] event[0m
[0;32mI (11801) UART: uart rx break[0m
[0;32mI (11801) UART: uart[1] event[0m
[0;32mI (11811) UART: uart rx break[0m
[0;32mI (11811) UART: uart[1] event[0m
[0;32mI (11811) UART: uart rx break[0m
[0;32mI (11811) UART: uart[1] event[0m
[0;32mI (11821) UART: uart rx break[0m
[0;32mI (11821) UART: uart[1] event[0m
[0;32mI (11821) UART: uart rx break[0m
[0;32mI (11831) UART: uart[0] event[0m
[0;32mI (11831) UART_READ: : 2[0m
[0;32mI (11831) UART_READ: : : UART_READ_END[0m
[0;32mI (11831) UART READ: [LEN: ]: 213[0m
[0;32mI (11841) UART: uart[1] event[0m
[0;32mI (11841) UART: uart rx break[0m
[0;32mI (11841) UART: uart[1] event[0m
[0;32mI (11841) UART: uart rx break[0m
[0;32mI (11851) UART: uart[1] event[0m
[0;32mI (11851) UART: uart rx break[0m
[0;32mI (11851) UART: uart[1] event[0m
[0;32mI (11861) UART: uart rx break[0m
[0;32mI (11861) UART: uart[1] event[0m
[0;32mI (11861) UART: uart rx break[0m
[0;32mI (11861) UART: uart[1] event[0m
[0;32mI (11871) UART: uart rx break[0m
[0;32mI (11871) UART: uart[1] event[0m
[0;32mI (11871) UART: uart rx break[0m
[0;32mI (11871) UART: uart[1] event[0m
[0;32mI (11881) UART: uart rx break[0m
[0;32mI (11881) UART: uart[1] event[0m
[0;32mI (11881) UART: uart rx break[0m
[0;32mI (11891) UART: uart[1] event[0m
[0;32mI (11891) UART: uart rx break[0m
[0;32mI (11891) UART: uart[1] event[0m
[0;32mI (11891) UART: uart rx break[0m
[0;32mI (11901) UART: uart[1] event[0m
[0;32mI (11901) UART: uart rx break[0m
[0;32mI (11901) UART: uart[1] event[0m
[0;32mI (11901) UART: uart rx break[0m
[0;32mI (11911) UART: uart[1] event[0m
[0;32mI (11911) UART: uart rx break[0m
[0;32mI (11911) UART: uart[1] event[0m
[0;32mI (11911) UART: uart rx break[0m
[0;32mI (11921) UART: uart[1] event[0m
[0;32mI (11921) UART: uart rx break[0m
[0;32mI (11921) UART: uart[1] event[0m
[0;32mI (11931) UART: uart rx break[0m
[0;32mI (11931) UART: uart[1] event[0m
[0;32mI (11931) UART: uart rx break[0m
[0;32mI (11931) UART: uart[1] event[0m
[0;32mI (11941) UART: uart rx break[0m
[0;32mI (11941) UART: uart[1] event[0m
[0;32mI (11941) UART: uart rx break[0m
[0;32mI (11941) UART: uart[1] event[0m
[0;32mI (11951) UART: uart rx break[0m
[0;32mI (11951) UART: uart[1] event[0m
[0;32mI (11951) UART: uart rx break[0m
[0;32mI (11961) UART: uart[1] event[0m
[0;32mI (11961) UART: uart rx break[0m
[0;32mI (11961) UART: uart[1] event[0m
[0;32mI (11961) UART: uart rx break[0m
[0;32mI (11971) UART: uart[1] event[0m
[0;32mI (11971) UART: uart rx break[0m
[0;32mI (11971) UART: uart[1] event[0m
[0;32mI (11971) UART: uart rx break[0m
[0;32mI (11981) UART: uart[1] event[0m
[0;32mI (11981) UART: uart rx break[0m
[0;32mI (11981) UART: uart[1] event[0m
[0;32mI (11981) UART: uart rx break[0m
[0;32mI (11991) UART: uart[1] event[0m
[0;32mI (11991) UART: uart rx break[0m
[0;32mI (11991) UART: uart[1] event[0m
[0;32mI (12001) UART: uart rx break[0m
[0;32mI (12001) UART: uart[1] event[0m
[0;32mI (12001) UART: uart rx break[0m
[0;32mI (12001) UART: uart[1] event[0m
[0;32mI (12011) UART: uart rx break[0m
[0;32mI (12011) UART: uart[1] event[0m
[0;32mI (12011) UART: uart rx break[0m
[0;32mI (12011) UART: uart[1] event[0m
[0;32mI (12021) UART: uart rx break[0m
[0;32mI (12021) UART: uart[0] event[0m
[0;32mI (12021) UART_READ: : 2[0m
[0;32mI (12021) UART_READ: : : UART_READ_END[0m
[0;32mI (12031) UART READ: [LEN: ]: 223[0m
[0;32mI (12031) UART: uart[1] event[0m
[0;32mI (12031) UART: uart rx break[0m
[0;32mI (12041) UART: uart[1] event[0m
[0;32mI (12041) UART: uart rx break[0m
[0;32mI (12041) UART: uart[1] event[0m
[0;32mI (12041) UART: uart rx break[0m
[0;32mI (12051) UART: uart[1] event[0m
[0;32mI (12051) UART: uart rx break[0m
[0;32mI (12051) UART: uart[1] event[0m
[0;32mI (12061) UART: uart rx break[0m
[0;32mI (12061) UART: uart[1] event[0m
[0;32mI (12061) UART: uart rx break[0m
[0;32mI (12061) UART: uart[1] event[0m
[0;32mI (12071) UART: uart rx break[0m
[0;32mI (12071) UART: uart[1] event[0m
[0;32mI (12071) UART: uart rx break[0m
[0;32mI (12071) UART: uart[1] event[0m
[0;32mI (12081) UART: uart rx break[0m
[0;32mI (12081) UART: uart[1] event[0m
[0;32mI (12081) UART: uart rx break[0m
[0;32mI (12091) UART: uart[1] event[0m
[0;32mI (12091) UART: uart rx break[0m
[0;32mI (12091) UART: uart[1] event[0m
[0;32mI (12091) UART: uart rx break[0m
[0;32mI (12101) UART: uart[1] event[0m
[0;32mI (12101) UART: uart rx break[0m
[0;32mI (12101) UART: uart[1] event[0m
[0;32mI (12101) UART: uart rx break[0m
[0;32mI (12111) UART: uart[1] event[0m
[0;32mI (12111) UART: uart rx break[0m
[0;32mI (12111) UART: uart[1] event[0m
[0;32mI (12111) UART: uart rx break[0m
[0;32mI (12121) UART: uart[1] event[0m
[0;32mI (12121) UART: uart rx break[0m
[0;32mI (12121) UART: uart[1] event[0m
[0;32mI (12131) UART: uart rx break[0m
[0;32mI (12131) UART: uart[1] event[0m
[0;32mI (12131) UART: uart rx break[0m
[0;32mI (12131) UART: uart[1] event[0m
[0;32mI (12141) UART: uart rx break[0m
[0;32mI (12141) UART: uart[1] event[0m
[0;32mI (12141) UART: uart rx break[0m
[0;32mI (12141) UART: uart[1] event[0m
[0;32mI (12151) UART: uart rx break[0m
[0;32mI (12151) UART: uart[1] event[0m
[0;32mI (12151) UART: uart rx break[0m
[0;32mI (12161) UART: uart[1] event[0m
[0;32mI (12161) UART: uart rx break[0m
[0;32mI (12161) UART: uart[1] event[0m
[0;32mI (12161) UART: uart rx break[0m
[0;32mI (12171) UART: uart[1] event[0m
[0;32mI (12171) UART: uart rx break[0m
[0;32mI (12171) UART: uart[1] event[0m
[0;32mI (12171) UART: uart rx break[0m
[0;32mI (12181) UART: uart[1] event[0m
[0;32mI (12181) UART: uart rx break[0m
[0;32mI (12181) UART: uart[1] event[0m
[0;32mI (12181) UART: uart rx break[0m
[0;32mI (12191) UART: uart[1] event[0m
[0;32mI (12191) UART: uart rx break[0m
[0;32mI (12191) UART: uart[1] event[0m
[0;32mI (12201) UART: uart rx break[0m
[0;32mI (12201) UART: uart[1] event[0m
[0;32mI (12201) UART: uart rx break[0m
[0;32mI (12201) UART: uart[1] event[0m
[0;32mI (12211) UART: uart rx break[0m
[0;32mI (12211) UART: uart[1] event[0m
[0;32mI (12211) UART: uart rx break[0m
[0;32mI (12211) UART: uart[1] event[0m
[0;32mI (12221) UART: uart rx break[0m
[0;32mI (12221) UART: uart[1] event[0m
[0;32mI (12221) UART: uart rx break[0m
[0;32mI (12231) UART: uart[1] event[0m
[0;32mI (12231) UART: uart rx break[0m
[0;32mI (12231) UART: uart[1] event[0m
[0;32mI (12231) UART: uart rx break[0m
[0;32mI (12241) UART: uart[1] event[0m
[0;32mI (12241) UART: uart rx break[0m
[0;32mI (12241) UART: uart[1] event[0m
[0;32mI (12241) UART: uart rx break[0m
[0;32mI (12251) UART: uart[1] event[0m
[0;32mI (12251) UART: uart rx break[0m
[0;32mI (12251) UART: uart[1] event[0m
[0;32mI (12251) UART: uart rx break[0m
[0;32mI (12261) UART: uart[1] event[0m
[0;32mI (12261) UART: uart rx break[0m
[0;32mI (12261) UART: uart[1] event[0m
[0;32mI (12271) UART: uart rx break[0m
[0;32mI (12271) UART: uart[1] event[0m
[0;32mI (12271) UART: uart rx break[0m
[0;32mI (12271) UART: uart[1] event[0m
[0;32mI (12281) UART: uart rx break[0m
[0;32mI (12281) UART: uart[1] event[0m
[0;32mI (12281) UART: uart rx break[0m
[0;32mI (12281) UART: uart[1] event[0m
[0;32mI (12291) UART: uart rx break[0m
[0;32mI (12291) UART: uart[1] event[0m
[0;32mI (12291) UART: uart rx break[0m
[0;32mI (12291) UART: uart[1] event[0m
[0;32mI (12301) UART: uart rx break[0m
[0;32mI (12301) UART: uart[1] event[0m
[0;32mI (12301) UART: uart rx break[0m
[0;32mI (12311) UART: uart[1] event[0m
[0;32mI (12311) UART: uart rx break[0m
[0;32mI (12311) UART: uart[1] event[0m
[0;32mI (12311) UART: uart rx break[0m
[0;32mI (12321) UART: uart[1] event[0m
[0;32mI (12321) UART: uart rx break[0m
[0;32mI (12321) UART: uart[1] event[0m
[0;32mI (12321) UART: uart rx break[0m
[0;32mI (12331) UART: uart[1] event[0m
[0;32mI (12331) UART: uart rx break[0m
[0;32mI (12331) UART: uart[1] event[0m
[0;32mI (12341) UART: uart rx break[0m
[0;32mI (12341) UART: uart[1] event[0m
[0;32mI (12341) UART: uart rx break[0m
[0;32mI (12341) UART: uart[1] event[0m
[0;32mI (12351) UART: uart rx break[0m
[0;32mI (12351) UART: uart[1] event[0m
[0;32mI (12351) UART: uart rx break[0m
[0;32mI (12351) UART: uart[1] event[0m
[0;32mI (12361) UART: uart rx break[0m
[0;32mI (12361) UART: uart[1] event[0m
[0;32mI (12361) UART: uart rx break[0m
[0;32mI (12361) UART: uart[1] event[0m
[0;32mI (12371) UART: uart rx break[0m
[0;32mI (12371) UART: uart[1] event[0m
[0;32mI (12371) UART: uart rx break[0m
[0;32mI (12381) UART: uart[1] event[0m
[0;32mI (12381) UART: uart rx break[0m
[0;32mI (12381) UART: uart[1] event[0m
[0;32mI (12381) UART: uart rx break[0m
[0;32mI (12391) UART: uart[1] event[0m
[0;32mI (12391) UART: uart rx break[0m
[0;32mI (12391) UART: uart[1] event[0m
[0;32mI (12391) UART: uart rx break[0m
[0;32mI (12401) UART: uart[1] event[0m
[0;32mI (12401) UART: uart rx break[0m
[0;32mI (12401) UART: uart[1] event[0m
[0;32mI (12411) UART: uart rx break[0m
[0;32mI (12411) UART: uart[1] event[0m
[0;32mI (12411) UART: uart rx break[0m
[0;32mI (12411) UART: uart[1] event[0m
[0;32mI (12421) UART: uart rx break[0m
[0;32mI (12421) UART: uart[1] event[0m
[0;32mI (12421) UART: uart rx break[0m
[0;32mI (12421) UART: uart[1] event[0m
[0;32mI (12431) UART: uart rx break[0m
[0;32mI (12431) UART: uart[1] event[0m
[0;32mI (12431) UART: uart rx break[0m
[0;32mI (12431) UART: uart[1] event[0m
[0;32mI (12441) UART: uart rx break[0m
[0;32mI (12441) UART: uart[1] event[0m
[0;32mI (12441) UART: uart rx break[0m
[0;32mI (12451) UART: uart[1] event[0m
[0;32mI (12451) UART: uart rx break[0m
[0;32mI (12451) UART: uart[1] event[0m
[0;32mI (12451) UART: uart rx break[0m
[0;32mI (12461) UART: uart[1] event[0m
[0;32mI (12461) UART: uart rx break[0m
[0;32mI (12461) UART: uart[1] event[0m
[0;32mI (12461) UART: uart rx break[0m
[0;32mI (12471) UART: uart[1] event[0m
[0;32mI (12471) UART: uart rx break[0m
[0;32mI (12471) UART: uart[1] event[0m
[0;32mI (12481) UART: uart rx break[0m
[0;32mI (12481) UART: uart[1] event[0m
[0;32mI (12481) UART: uart rx break[0m
[0;32mI (12481) UART: uart[1] event[0m
[0;32mI (12491) UART: uart rx break[0m
[0;32mI (12491) UART: uart[1] event[0m
[0;32mI (12491) UART: uart rx break[0m
[0;32mI (12491) UART: uart[1] event[0m
[0;32mI (12501) UART: uart rx break[0m
[0;32mI (12501) UART: uart[1] event[0m
[0;32mI (12501) UART: uart rx break[0m
[0;32mI (12501) UART: uart[1] event[0m
[0;32mI (12511) UART: uart rx break[0m
[0;32mI (12511) UART: uart[1] event[0m
[0;32mI (12511) UART: uart rx break[0m
[0;32mI (12521) UART: uart[1] event[0m
[0;32mI (12521) UART: uart rx break[0m
[0;32mI (12521) UART: uart[1] event[0m
[0;32mI (12521) UART: uart rx break[0m
[0;32mI (12531) UART: uart[1] event[0m
[0;32mI (12531) UART: uart rx break[0m
[0;32mI (12531) UART: uart[1] event[0m
[0;32mI (12531) UART: uart rx break[0m
[0;32mI (12541) UART: uart[1] event[0m
[0;32mI (12541) UART: uart rx break[0m
[0;32mI (12541) UART: uart[1] event[0m
[0;32mI (12551) UART: uart rx break[0m
[0;32mI (12551) UART: uart[1] event[0m
[0;32mI (12551) UART: uart rx break[0m
[0;32mI (12551) UART: uart[1] event[0m
[0;32mI (12561) UART: uart rx break[0m
[0;32mI (12561) UART: uart[1] event[0m
[0;32mI (12561) UART: uart rx break[0m
[0;32mI (12561) UART: uart[1] event[0m
[0;32mI (12571) UART: uart rx break[0m
[0;32mI (12571) UART: uart[1] event[0m
[0;32mI (12571) UART: uart rx break[0m
[0;32mI (12571) UART: uart[1] event[0m
[0;32mI (12581) UART: uart rx break[0m
[0;32mI (12581) UART: uart[1] event[0m
[0;32mI (12581) UART: uart rx break[0m
[0;32mI (12591) UART: uart[1] event[0m
[0;32mI (12591) UART: uart rx break[0m
[0;32mI (12591) UART: uart[0] event[0m
[0;32mI (12591) UART_READ: : 2[0m
[0;32mI (12601) UART_READ: : : UART_READ_END[0m
[0;32mI (12601) UART READ: [LEN: ]: 343[0m
[0;32mI (12601) UART: uart[1] event[0m
[0;32mI (12611) UART: uart rx break[0m
[0;32mI (12611) UART: uart[1] event[0m
[0;32mI (12611) UART: uart rx break[0m
[0;32mI (12611) UART: uart[1] event[0m
[0;32mI (12621) UART: uart rx break[0m
[0;32mI (12621) UART: uart[1] event[0m
[0;32mI (12621) UART: uart rx break[0m
[0;32mI (12621) UART: uart[1] event[0m
[0;32mI (12631) UART: uart rx break[0m
[0;32mI (12631) UART: uart[1] event[0m
[0;32mI (12631) UART: uart rx break[0m
[0;32mI (12631) UART: uart[1] event[0m
[0;32mI (12641) UART: uart rx break[0m
[0;32mI (12641) UART: uart[1] event[0m
[0;32mI (12641) UART: uart rx break[0m
[0;32mI (12651) UART: uart[1] event[0m
[0;32mI (12651) UART: uart rx break[0m
[0;32mI (12651) UART: uart[1] event[0m
[0;32mI (12651) UART: uart rx break[0m
[0;32mI (12661) UART: uart[1] event[0m
[0;32mI (12661) UART: uart rx break[0m
[0;32mI (12661) UART: uart[1] event[0m
[0;32mI (12661) UART: uart rx break[0m
[0;32mI (12671) UART: uart[1] event[0m
[0;32mI (12671) UART: uart rx break[0m
[0;32mI (12671) UART: uart[1] event[0m
[0;32mI (12681) UART: uart rx break[0m
[0;32mI (12681) UART: uart[1] event[0m
[0;32mI (12681) UART: uart rx break[0m
[0;32mI (12681) UART: uart[1] event[0m
[0;32mI (12691) UART: uart rx break[0m
[0;32mI (12691) UART: uart[1] event[0m
[0;32mI (12691) UART: uart rx break[0m
[0;32mI (12691) UART: uart[1] event[0m
[0;32mI (12701) UART: uart rx break[0m
[0;32mI (12701) UART: uart[1] event[0m
[0;32mI (12701) UART: uart rx break[0m
[0;32mI (12701) UART: uart[1] event[0m
[0;32mI (12711) UART: uart rx break[0m
[0;32mI (12711) UART: uart[1] event[0m
[0;32mI (12711) UART: uart rx break[0m
[0;32mI (12721) UART: uart[1] event[0m
[0;32mI (12721) UART: uart rx break[0m
[0;32mI (12721) UART: uart[1] event[0m
[0;32mI (12721) UART: uart rx break[0m
[0;32mI (12731) UART: uart[1] event[0m
[0;32mI (12731) UART: uart rx break[0m
[0;32mI (12731) UART: uart[1] event[0m
[0;32mI (12731) UART: uart rx break[0m
[0;32mI (12741) UART: uart[1] event[0m
[0;32mI (12741) UART: uart rx break[0m
[0;32mI (12741) UART: uart[1] event[0m
[0;32mI (12741) UART: uart rx break[0m
[0;32mI (12751) UART: uart[1] event[0m
[0;32mI (12751) UART: uart rx break[0m
[0;32mI (12751) UART: uart[1] event[0m
[0;32mI (12761) UART: uart rx break[0m
[0;32mI (12761) UART: uart[1] event[0m
[0;32mI (12761) UART: uart rx break[0m
[0;32mI (12761) UART: uart[1] event[0m
[0;32mI (12771) UART: uart rx break[0m
[0;32mI (12771) UART: uart[1] event[0m
[0;32mI (12771) UART: uart rx break[0m
[0;32mI (12771) UART: uart[1] event[0m
[0;32mI (12781) UART: uart rx break[0m
[0;32mI (12781) UART: uart[1] event[0m
[0;32mI (12781) UART: uart rx break[0m
[0;32mI (12791) UART: uart[1] event[0m
[0;32mI (12791) UART: uart rx break[0m
[0;32mI (12791) UART: uart[1] event[0m
[0;32mI (12791) UART: uart rx break[0m
[0;32mI (12801) UART: uart[1] event[0m
[0;32mI (12801) UART: uart rx break[0m
[0;32mI (12801) UART: uart[1] event[0m
[0;32mI (12801) UART: uart rx break[0m
[0;32mI (12811) UART: uart[1] event[0m
[0;32mI (12811) UART: uart rx break[0m
[0;32mI (12811) UART: uart[1] event[0m
[0;32mI (12811) UART: uart rx break[0m
[0;32mI (12821) UART: uart[1] event[0m
[0;32mI (12821) UART: uart rx break[0m
[0;32mI (12821) UART: uart[1] event[0m
[0;32mI (12831) UART: uart rx break[0m
[0;32mI (12831) UART: uart[1] event[0m
[0;32mI (12831) UART: uart rx break[0m
[0;32mI (12831) UART: uart[1] event[0m
[0;32mI (12841) UART: uart rx break[0m
[0;32mI (12841) UART: uart[1] event[0m
[0;32mI (12841) UART: uart rx break[0m
[0;32mI (12841) UART: uart[1] event[0m
[0;32mI (12851) UART: uart rx break[0m
[0;32mI (12851) UART: uart[1] event[0m
[0;32mI (12851) UART: uart rx break[0m
[0;32mI (12861) UART: uart[1] event[0m
[0;32mI (12861) UART: uart rx break[0m
[0;32mI (12861) UART: uart[1] event[0m
[0;32mI (12861) UART: uart rx break[0m
[0;32mI (12871) UART: uart[1] event[0m
[0;32mI (12871) UART: uart rx break[0m
[0;32mI (12871) UART: uart[1] event[0m
[0;32mI (12871) UART: uart rx break[0m
[0;32mI (12881) UART: uart[1] event[0m
[0;32mI (12881) UART: uart rx break[0m
[0;32mI (12881) UART: uart[1] event[0m
[0;32mI (12881) UART: uart rx break[0m
[0;32mI (12891) UART: uart[1] event[0m
[0;32mI (12891) UART: uart rx break[0m
[0;32mI (12891) UART: uart[1] event[0m
[0;32mI (12901) UART: uart rx break[0m
[0;32mI (12901) UART: uart[1] event[0m
[0;32mI (12901) UART: uart rx break[0m
[0;32mI (12901) UART: uart[1] event[0m
[0;32mI (12911) UART: uart rx break[0m
[0;32mI (12911) UART: uart[1] event[0m
[0;32mI (12911) UART: uart rx break[0m
[0;32mI (12911) UART: uart[1] event[0m
[0;32mI (12921) UART: uart rx break[0m
[0;32mI (12921) UART: uart[1] event[0m
[0;32mI (12921) UART: uart rx break[0m
[0;32mI (12931) UART: uart[1] event[0m
[0;32mI (12931) UART: uart rx break[0m
[0;32mI (12931) UART: uart[1] event[0m
[0;32mI (12931) UART: uart rx break[0m
[0;32mI (12941) UART: uart[1] event[0m
[0;32mI (12941) UART: uart rx break[0m
[0;32mI (12941) UART: uart[1] event[0m
[0;32mI (12941) UART: uart rx break[0m
[0;32mI (12951) UART: uart[1] event[0m
[0;32mI (12951) UART: uart rx break[0m
[0;32mI (12951) UART: uart[1] event[0m
[0;32mI (12951) UART: uart rx break[0m
[0;32mI (12961) UART: uart[1] event[0m
[0;32mI (12961) UART: uart rx break[0m
[0;32mI (12961) UART: uart[1] event[0m
[0;32mI (12971) UART: uart rx break[0m
[0;32mI (12971) UART: uart[1] event[0m
[0;32mI (12971) UART: uart rx break[0m
[0;32mI (12971) UART: uart[1] event[0m
[0;32mI (12981) UART: uart rx break[0m
[0;32mI (12981) UART: uart[1] event[0m
[0;32mI (12981) UART: uart rx break[0m
[0;32mI (12981) UART: uart[1] event[0m
[0;32mI (12991) UART: uart rx break[0m
[0;32mI (12991) UART: uart[1] event[0m
[0;32mI (12991) UART: uart rx break[0m
[0;32mI (13001) UART: uart[1] event[0m
[0;32mI (13001) UART: uart rx break[0m
[0;32mI (13001) UART: uart[1] event[0m
[0;32mI (13001) UART: uart rx break[0m
[0;32mI (13011) UART: uart[1] event[0m
[0;32mI (13011) UART: uart rx break[0m
[0;32mI (13011) UART: uart[1] event[0m
[0;32mI (13011) UART: uart rx break[0m
[0;32mI (13021) UART: uart[1] event[0m
[0;32mI (13021) UART: uart rx break[0m
[0;32mI (13021) UART: uart[1] event[0m
[0;32mI (13021) UART: uart rx break[0m
[0;32mI (13031) UART: uart[1] event[0m
[0;32mI (13031) UART: uart rx break[0m
[0;32mI (13031) UART: uart[1] event[0m
[0;32mI (13041) UART: uart rx break[0m
[0;32mI (13041) UART: uart[1] event[0m
[0;32mI (13041) UART: uart rx break[0m
[0;32mI (13041) UART: uart[1] event[0m
[0;32mI (13051) UART: uart rx break[0m
[0;32mI (13051) UART: uart[0] event[0m
[0;32mI (13051) UART_READ: : 2[0m
[0;32mI (13051) UART_READ: : : UART_READ_END[0m
[0;32mI (13061) UART READ: [LEN: ]: 463[0m
[0;32mI (13061) UART: uart[1] event[0m
[0;32mI (13061) UART: uart rx break[0m
[0;32mI (13071) UART: uart[1] event[0m
[0;32mI (13071) UART: uart rx break[0m
[0;32mI (13071) UART: uart[1] event[0m
[0;32mI (13071) UART: uart rx break[0m
[0;32mI (13081) UART: uart[1] event[0m
[0;32mI (13081) UART: uart rx break[0m
[0;32mI (13081) UART: uart[1] event[0m
[0;32mI (13081) UART: uart rx break[0m
[0;32mI (13091) UART: uart[1] event[0m
[0;32mI (13091) UART: uart rx break[0m
[0;32mI (13091) UART: uart[1] event[0m
[0;32mI (13101) UART: uart rx break[0m
[0;32mI (13101) UART: uart[1] event[0m
[0;32mI (13101) UART: uart rx break[0m
[0;32mI (13101) UART: uart[1] event[0m
[0;32mI (13111) UART: uart rx break[0m
[0;32mI (13111) UART: uart[1] event[0m
[0;32mI (13111) UART: uart rx break[0m
[0;32mI (13111) UART: uart[1] event[0m
[0;32mI (13121) UART: uart rx break[0m
[0;32mI (13121) UART: uart[1] event[0m
[0;32mI (13121) UART: uart rx break[0m
[0;32mI (13131) UART: uart[1] event[0m
[0;32mI (13131) UART: uart rx break[0m
[0;32mI (13131) UART: uart[1] event[0m
[0;32mI (13131) UART: uart rx break[0m
[0;32mI (13141) UART: uart[0] event[0m
[0;32mI (13141) UART_READ: : 2[0m
[0;32mI (13141) UART_READ: : : UART_READ_END[0m
[0;32mI (13141) UART READ: [LEN: ]: 489[0m
[0;32mI (13151) UART: uart[1] event[0m
[0;32mI (13151) UART: uart rx break[0m
[0;32mI (13151) UART: uart[1] event[0m
[0;32mI (13161) UART: uart rx break[0m
[0;32mI (13161) UART: uart[1] event[0m
[0;32mI (13161) UART: uart rx break[0m
[0;32mI (13161) UART: uart[1] event[0m
[0;32mI (13171) UART: uart rx break[0m
[0;32mI (13171) UART: uart[1] event[0m
[0;32mI (13171) UART: uart rx break[0m
[0;32mI (13171) UART: uart[1] event[0m
[0;32mI (13181) UART: uart rx break[0m
[0;32mI (13181) UART: uart[1] event[0m
[0;32mI (13181) UART: uart rx break[0m
[0;32mI (13181) UART: uart[1] event[0m
[0;32mI (13191) UART: uart rx break[0m
[0;32mI (13191) UART: uart[1] event[0m
[0;32mI (13191) UART: uart rx break[0m
[0;32mI (13201) UART: uart[1] event[0m
[0;32mI (13201) UART: uart rx break[0m
[0;32mI (13201) UART: uart[1] event[0m
[0;32mI (13201) UART: uart rx break[0m
[0;32mI (13211) UART: uart[1] event[0m
[0;32mI (13211) UART: uart rx break[0m
[0;32mI (13211) UART: uart[1] event[0m
[0;32mI (13211) UART: uart rx break[0m
[0;32mI (13221) UART: uart[1] event[0m
[0;32mI (13221) UART: uart rx break[0m
[0;32mI (13221) UART: uart[1] event[0m
[0;32mI (13231) UART: uart rx break[0m
[0;32mI (13231) UART: uart[1] event[0m
[0;32mI (13231) UART: uart rx break[0m
[0;32mI (13231) UART: uart[1] event[0m
[0;32mI (13241) UART: uart rx break[0m
[0;32mI (13241) UART: uart[1] event[0m
[0;32mI (13241) UART: uart rx break[0m
[0;32mI (13241) UART: uart[1] event[0m
[0;32mI (13251) UART: uart rx break[0m
[0;32mI (13251) UART: uart[1] event[0m
[0;32mI (13251) UART: uart rx break[0m
[0;32mI (13251) UART: uart[1] event[0m
[0;32mI (13261) UART: uart rx break[0m
[0;32mI (13261) UART: uart[1] event[0m
[0;32mI (13261) UART: uart rx break[0m
[0;32mI (13271) UART: uart[1] event[0m
[0;32mI (13271) UART: uart rx break[0m
[0;32mI (13271) UART: uart[1] event[0m
[0;32mI (13271) UART: uart rx break[0m
[0;32mI (13281) UART: uart[1] event[0m
[0;32mI (13281) UART: uart rx break[0m
[0;32mI (13281) UART: uart[1] event[0m
[0;32mI (13281) UART: uart rx break[0m
[0;32mI (13291) UART: uart[1] event[0m
[0;32mI (13291) UART: uart rx break[0m
[0;32mI (13291) UART: uart[1] event[0m
[0;32mI (13301) UART: uart rx break[0m
[0;32mI (13301) UART: uart[1] event[0m
[0;32mI (13301) UART: uart rx break[0m
[0;32mI (13301) UART: uart[1] event[0m
[0;32mI (13311) UART: uart rx break[0m
[0;32mI (13311) UART: uart[1] event[0m
[0;32mI (13311) UART: uart rx break[0m
[0;32mI (13311) UART: uart[1] event[0m
[0;32mI (13321) UART: uart rx break[0m
[0;32mI (13321) UART: uart[1] event[0m
[0;32mI (13321) UART: uart rx break[0m
[0;32mI (13321) UART: uart[1] event[0m
[0;32mI (13331) UART: uart rx break[0m
[0;32mI (13331) UART: uart[1] event[0m
[0;32mI (13331) UART: uart rx break[0m
[0;32mI (13341) UART: uart[1] event[0m
[0;32mI (13341) UART: uart rx break[0m
[0;32mI (13341) UART: uart[0] event[0m
[0;32mI (13341) UART_READ: : 2[0m
[0;32mI (13351) UART_READ: : : UART_READ_END[0m
[0;32mI (13351) UART READ: [LEN: ]: 508[0m
[0;32mI (13351) UART: uart[1] event[0m
[0;32mI (13361) UART: uart rx break[0m
[0;32mI (13361) UART: uart[1] event[0m
[0;32mI (13361) UART: uart rx break[0m
[0;32mI (13361) UART: uart[1] event[0m
[0;32mI (13371) UART: uart rx break[0m
[0;32mI (13371) UART: uart[1] event[0m
[0;32mI (13371) UART: uart rx break[0m
[0;32mI (13371) UART: uart[1] event[0m
[0;32mI (13381) UART: uart rx break[0m
[0;32mI (13381) UART: uart[1] event[0m
[0;32mI (13381) UART: uart rx break[0m
[0;32mI (13381) UART: uart[1] event[0m
[0;32mI (13391) UART: uart rx break[0m
[0;32mI (13391) UART: uart[1] event[0m
[0;32mI (13391) UART: uart rx break[0m
[0;32mI (13401) UART: uart[1] event[0m
[0;32mI (13401) UART: uart rx break[0m
[0;32mI (13401) UART: uart[1] event[0m
[0;32mI (13401) UART: uart rx break[0m
[0;32mI (13411) UART: uart[1] event[0m
[0;32mI (13411) UART: uart rx break[0m
[0;32mI (13411) UART: uart[1] event[0m
[0;32mI (13411) UART: uart rx break[0m
[0;32mI (13421) UART: uart[1] event[0m
[0;32mI (13421) UART: uart rx break[0m
[0;32mI (13421) UART: uart[1] event[0m
[0;32mI (13431) UART: uart rx break[0m
[0;32mI (13431) UART: uart[0] event[0m
[0;32mI (13431) UART_READ: : 2[0m
[0;32mI (13431) UART_READ: : : UART_READ_END[0m
[0;32mI (13441) UART READ: [LEN: ]: 525[0m
[0;32mI (13441) UART: uart[1] event[0m
[0;32mI (13441) UART: uart rx break[0m
[0;32mI (13441) UART: uart[1] event[0m
[0;32mI (13451) UART: uart rx break[0m
[0;32mI (13451) UART: uart[1] event[0m
[0;32mI (13451) UART: uart rx break[0m
[0;32mI (13461) UART: uart[1] event[0m
[0;32mI (13461) UART: uart rx break[0m
[0;32mI (13461) UART: uart[1] event[0m
[0;32mI (13461) UART: uart rx break[0m
[0;32mI (13471) UART: uart[1] event[0m
[0;32mI (13471) UART: uart rx break[0m
[0;32mI (13471) UART: uart[1] event[0m
[0;32mI (13471) UART: uart rx break[0m
[0;32mI (13481) UART: uart[1] event[0m
[0;32mI (13481) UART: uart rx break[0m
[0;32mI (13481) UART: uart[1] event[0m
[0;32mI (13491) UART: uart rx break[0m
[0;32mI (13491) UART: uart[1] event[0m
[0;32mI (13491) UART: uart rx break[0m
[0;32mI (13491) UART: uart[1] event[0m
[0;32mI (13501) UART: uart rx break[0m
[0;32mI (13501) UART: uart[1] event[0m
[0;32mI (13501) UART: uart rx break[0m
[0;32mI (13501) UART: uart[1] event[0m
[0;32mI (13511) UART: uart rx break[0m
[0;32mI (13511) UART: uart[1] event[0m
[0;32mI (13511) UART: uart rx break[0m
[0;32mI (13511) UART: uart[1] event[0m
[0;32mI (13521) UART: uart rx break[0m
[0;32mI (13521) UART: uart[1] event[0m
[0;32mI (13521) UART: uart rx break[0m
[0;32mI (13531) UART: uart[0] event[0m
[0;32mI (13531) UART_READ: : 2[0m
[0;32mI (13531) UART_READ: : : UART_READ_END[0m
[0;32mI (13531) UART READ: [LEN: ]: 541[0m
[0;32mI (13541) UART: uart[1] event[0m
[0;32mI (13541) UART: uart rx break[0m
[0;32mI (13541) UART: uart[1] event[0m
[0;32mI (13541) UART: uart rx break[0m
[0;32mI (13551) UART: uart[1] event[0m
[0;32mI (13551) UART: uart rx break[0m
[0;32mI (13551) UART: uart[1] event[0m
[0;32mI (13561) UART: uart rx break[0m
[0;32mI (13561) UART: uart[1] event[0m
[0;32mI (13561) UART: uart rx break[0m
[0;32mI (13561) UART: uart[1] event[0m
[0;32mI (13571) UART: uart rx break[0m
[0;32mI (13571) UART: uart[1] event[0m
[0;32mI (13571) UART: uart rx break[0m
[0;32mI (13571) UART: uart[1] event[0m
[0;32mI (13581) UART: uart rx break[0m
[0;32mI (13581) UART: uart[1] event[0m
[0;32mI (13581) UART: uart rx break[0m
[0;32mI (13591) UART: uart[1] event[0m
[0;32mI (13591) UART: uart rx break[0m
[0;32mI (13591) UART: uart[1] event[0m
[0;32mI (13591) UART: uart rx break[0m
[0;32mI (13601) UART: uart[1] event[0m
[0;32mI (13601) UART: uart rx break[0m
[0;32mI (13601) UART: uart[0] event[0m
[0;32mI (13601) UART_READ: : 2[0m
[0;32mI (13611) UART_READ: : : UART_READ_END[0m
[0;32mI (13611) UART READ: [LEN: ]: 561[0m
[0;32mI (13611) UART: uart[1] event[0m
[0;32mI (13621) UART: uart rx break[0m
[0;32mI (13621) UART: uart[1] event[0m
[0;32mI (13621) UART: uart rx break[0m
[0;32mI (13621) UART: uart[1] event[0m
[0;32mI (13631) UART: uart rx break[0m
[0;32mI (13631) UART: uart[1] event[0m
[0;32mI (13631) UART: uart rx break[0m
[0;32mI (13631) UART: uart[1] event[0m
[0;32mI (13641) UART: uart rx break[0m
[0;32mI (13641) UART: uart[1] event[0m
[0;32mI (13641) UART: uart rx break[0m
[0;32mI (13651) UART: uart[1] event[0m
[0;32mI (13651) UART: uart rx break[0m
[0;32mI (13651) UART: uart[1] event[0m
[0;32mI (13651) UART: uart rx break[0m
[0;32mI (13661) UART: uart[1] event[0m
[0;32mI (13661) UART: uart rx break[0m
[0;32mI (13661) UART: uart[1] event[0m
[0;32mI (13661) UART: uart rx break[0m
[0;32mI (13671) UART: uart[1] event[0m
[0;32mI (13671) UART: uart rx break[0m
[0;32mI (13671) UART: uart[1] event[0m
[0;32mI (13671) UART: uart rx break[0m
[0;32mI (13681) UART: uart[1] event[0m
[0;32mI (13681) UART: uart rx break[0m
[0;32mI (13681) UART: uart[1] event[0m
[0;32mI (13691) UART: uart rx break[0m
[0;32mI (13691) UART: uart[1] event[0m
[0;32mI (13691) UART: uart rx break[0m
[0;32mI (13691) UART: uart[1] event[0m
[0;32mI (13701) UART: uart rx break[0m
[0;32mI (13701) UART: uart[1] event[0m
[0;32mI (13701) UART: uart rx break[0m
[0;32mI (13701) UART: uart[1] event[0m
[0;32mI (13711) UART: uart rx break[0m
[0;32mI (13711) UART: uart[1] event[0m
[0;32mI (13711) UART: uart rx break[0m
[0;32mI (13721) UART: uart[1] event[0m
[0;32mI (13721) UART: uart rx break[0m
[0;32mI (13721) UART: uart[1] event[0m
[0;32mI (13721) UART: uart rx break[0m
[0;32mI (13731) UART: uart[1] event[0m
[0;32mI (13731) UART: uart rx break[0m
[0;32mI (13731) UART: uart[1] event[0m
[0;32mI (13731) UART: uart rx break[0m
[0;32mI (13741) UART: uart[0] event[0m
[0;32mI (13741) UART_READ: : 2[0m
[0;32mI (13741) UART_READ: : : UART_READ_END[0m
[0;32mI (13751) UART READ: [LEN: ]: 566[0m
[0;32mI (13751) UART: uart[1] event[0m
[0;32mI (13751) UART: uart rx break[0m
[0;32mI (13751) UART: uart[1] event[0m
[0;32mI (13761) UART: uart rx break[0m
[0;32mI (13761) UART: uart[1] event[0m
[0;32mI (13761) UART: uart rx break[0m
[0;32mI (13761) UART: uart[1] event[0m
[0;32mI (13771) UART: uart rx break[0m
[0;32mI (13771) UART: uart[1] event[0m
[0;32mI (13771) UART: uart rx break[0m
[0;32mI (13781) UART: uart[1] event[0m
[0;32mI (13781) UART: uart rx break[0m
[0;32mI (13781) UART: uart[1] event[0m
[0;32mI (13781) UART: uart rx break[0m
[0;32mI (13791) UART: uart[1] event[0m
[0;32mI (13791) UART: uart rx break[0m
[0;32mI (13791) UART: uart[1] event[0m
[0;32mI (13791) UART: uart rx break[0m
[0;32mI (13801) UART: uart[1] event[0m
[0;32mI (13801) UART: uart rx break[0m
[0;32mI (13801) UART: uart[1] event[0m
[0;32mI (13801) UART: uart rx break[0m
[0;32mI (13811) UART: uart[1] event[0m
[0;32mI (13811) UART: uart rx break[0m
[0;32mI (13811) UART: uart[1] event[0m
[0;32mI (13821) UART: uart rx break[0m
[0;32mI (13821) UART: uart[1] event[0m
[0;32mI (13821) UART: uart rx break[0m
[0;32mI (13821) UART: uart[1] event[0m
[0;32mI (13831) UART: uart rx break[0m
[0;32mI (13831) UART: uart[1] event[0m
[0;32mI (13831) UART: uart rx break[0m
[0;32mI (13831) UART: uart[0] event[0m
[0;32mI (13841) UART_READ: : 2[0m
[0;32mI (13841) UART_READ: : : UART_READ_END[0m
[0;32mI (13841) UART READ: [LEN: ]: 585[0m
[0;32mI (13851) UART: uart[1] event[0m
[0;32mI (13851) UART: uart rx break[0m
[0;32mI (13851) UART: uart[1] event[0m
[0;32mI (13851) UART: uart rx break[0m
[0;32mI (13861) UART: uart[1] event[0m
[0;32mI (13861) UART: uart rx break[0m
[0;32mI (13861) UART: uart[1] event[0m
[0;32mI (13861) UART: uart rx break[0m
[0;32mI (13871) UART: uart[0] event[0m
[0;32mI (13871) UART_READ: : 2[0m
[0;32mI (13871) UART_READ: : : UART_READ_END[0m
[0;32mI (13881) UART READ: [LEN: ]: 602[0m
[0;32mI (13881) UART: uart[1] event[0m
[0;32mI (13881) UART: uart rx break[0m
[0;32mI (13881) UART: uart[1] event[0m
[0;32mI (13891) UART: uart rx break[0m
[0;32mI (13891) UART: uart[1] event[0m
[0;32mI (13891) UART: uart rx break[0m
[0;32mI (13891) UART: uart[1] event[0m
[0;32mI (13901) UART: uart rx break[0m
[0;32mI (13901) UART: uart[0] event[0m
[0;32mI (13901) UART_READ: : 2[0m
[0;32mI (13911) UART_READ: : : UART_READ_END[0m
[0;32mI (13911) UART READ: [LEN: ]: 622[0m
[0;32mI (13911) UART: uart[1] event[0m
[0;32mI (13911) UART: uart rx break[0m
[0;32mI (13921) UART: uart[1] event[0m
[0;32mI (13921) UART: uart rx break[0m
[0;32mI (13921) UART: uart[1] event[0m
[0;32mI (13931) UART: uart rx break[0m
[0;32mI (13931) UART: uart[1] event[0m
[0;32mI (13931) UART: uart rx break[0m
[0;32mI (13931) UART: uart[1] event[0m
[0;32mI (13941) UART: uart rx break[0m
[0;32mI (13941) UART: uart[1] event[0m
[0;32mI (13941) UART: uart rx break[0m
[0;32mI (13941) UART: uart[1] event[0m
[0;32mI (13951) UART: uart rx break[0m
[0;32mI (13951) UART: uart[1] event[0m
[0;32mI (13951) UART: uart rx break[0m
[0;32mI (13951) UART: uart[1] event[0m
[0;32mI (13961) UART: uart rx break[0m
[0;32mI (13961) UART: uart[1] event[0m
[0;32mI (13961) UART: uart rx break[0m
[0;32mI (13971) UART: uart[1] event[0m
[0;32mI (13971) UART: uart rx break[0m
[0;32mI (13971) UART: uart[0] event[0m
[0;32mI (13971) UART_READ: : 2[0m
[0;32mI (13981) UART_READ: : : UART_READ_END[0m
[0;32mI (13981) UART READ: [LEN: ]: 623[0m
[0;32mI (13981) UART: uart[0] event[0m
[0;32mI (13981) UART_READ: : 2[0m
[0;32mI (13991) UART_READ: : : UART_READ_END[0m
[0;32mI (13991) UART READ: [LEN: ]: 637[0m
[0;32mI (13991) UART: uart[1] event[0m
[0;32mI (14001) UART: uart rx break[0m
[0;32mI (14001) UART: uart[1] event[0m
[0;32mI (14001) UART: uart rx break[0m
[0;32mI (14001) UART: uart[1] event[0m
[0;32mI (14011) UART: uart rx break[0m
[0;32mI (14011) UART: uart[1] event[0m
[0;32mI (14011) UART: uart rx break[0m
[0;32mI (14021) UART: uart[1] event[0m
[0;32mI (14021) UART: uart rx break[0m
[0;32mI (14021) UART: uart[1] event[0m
[0;32mI (14021) UART: uart rx break[0m
[0;32mI (14031) UART: uart[0] event[0m
[0;32mI (14031) UART_READ: : 2[0m
[0;32mI (14031) UART_READ: : : UART_READ_END[0m
[0;32mI (14031) UART READ: [LEN: ]: 657[0m
[0;32mI (14041) UART: uart[1] event[0m
[0;32mI (14041) UART: uart rx break[0m
[0;32mI (14041) UART: uart[1] event[0m
[0;32mI (14051) UART: uart rx break[0m
[0;32mI (14051) UART: uart[1] event[0m
[0;32mI (14051) UART: uart rx break[0m
[0;32mI (14051) UART: uart[1] event[0m
[0;32mI (14061) UART: uart rx break[0m
[0;32mI (14061) UART: uart[1] event[0m
[0;32mI (14061) UART: uart rx break[0m
[0;32mI (14061) UART: uart[1] event[0m
[0;32mI (14071) UART: uart rx break[0m
[0;32mI (14071) UART: uart[1] event[0m
[0;32mI (14071) UART: uart rx break[0m
[0;32mI (14081) UART: uart[0] event[0m
[0;32mI (14081) UART_READ: : 2[0m
[0;32mI (14081) UART_READ: : : UART_READ_END[0m
[0;32mI (14081) UART READ: [LEN: ]: 676[0m
[0;32mI (14091) UART: uart[1] event[0m
[0;32mI (14091) UART: uart rx break[0m
[0;32mI (14091) UART: uart[1] event[0m
[0;32mI (14091) UART: uart rx break[0m
[0;32mI (14101) UART: uart[1] event[0m
[0;32mI (14101) UART: uart rx break[0m
[0;32mI (14101) UART: uart[1] event[0m
[0;32mI (14111) UART: uart rx break[0m
[0;32mI (14111) UART: uart[1] event[0m
[0;32mI (14111) UART: uart rx break[0m
[0;32mI (14111) UART: uart[1] event[0m
[0;32mI (14121) UART: uart rx break[0m
[0;32mI (14121) UART: uart[1] event[0m
[0;32mI (14121) UART: uart rx break[0m
[0;32mI (14121) UART: uart[1] event[0m
[0;32mI (14131) UART: uart rx break[0m
[0;32mI (14131) UART: uart[1] event[0m
[0;32mI (14131) UART: uart rx break[0m
[0;32mI (14141) UART: uart[1] event[0m
[0;32mI (14141) UART: uart rx break[0m
[0;32mI (14141) UART: uart[1] event[0m
[0;32mI (14141) UART: uart rx break[0m
[0;32mI (14151) UART: uart[1] event[0m
[0;32mI (14151) UART: uart rx break[0m
[0;32mI (14151) UART: uart[1] event[0m
[0;32mI (14151) UART: uart rx break[0m
[0;32mI (14161) UART: uart[1] event[0m
[0;32mI (14161) UART: uart rx break[0m
[0;32mI (14161) UART: uart[1] event[0m
[0;32mI (14161) UART: uart rx break[0m
[0;32mI (14171) UART: uart[1] event[0m
[0;32mI (14171) UART: uart rx break[0m
[0;32mI (14171) UART: uart[1] event[0m
[0;32mI (14181) UART: uart rx break[0m
[0;32mI (14181) UART: uart[1] event[0m
[0;32mI (14181) UART: uart rx break[0m
[0;32mI (14181) UART: uart[1] event[0m
[0;32mI (14191) UART: uart rx break[0m
[0;32mI (14191) UART: uart[1] event[0m
[0;32mI (14191) UART: uart rx break[0m
[0;32mI (14191) UART: uart[1] event[0m
[0;32mI (14201) UART: uart rx break[0m
[0;32mI (14201) UART: uart[1] event[0m
[0;32mI (14201) UART: uart rx break[0m
[0;32mI (14211) UART: uart[1] event[0m
[0;32mI (14211) UART: uart rx break[0m
[0;32mI (14211) UART: uart[1] event[0m
[0;32mI (14211) UART: uart rx break[0m
[0;32mI (14221) UART: uart[1] event[0m
[0;32mI (14221) UART: uart rx break[0m
[0;32mI (14221) UART: uart[1] event[0m
[0;32mI (14221) UART: uart rx break[0m
[0;32mI (14231) UART: uart[1] event[0m
[0;32mI (14231) UART: uart rx break[0m
[0;32mI (14231) UART: uart[1] event[0m
[0;32mI (14231) UART: uart rx break[0m
[0;32mI (14241) UART: uart[1] event[0m
[0;32mI (14241) UART: uart rx break[0m
[0;32mI (14241) UART: uart[1] event[0m
[0;32mI (14251) UART: uart rx break[0m
[0;32mI (14251) UART: uart[1] event[0m
[0;32mI (14251) UART: uart rx break[0m
[0;32mI (14251) UART: uart[1] event[0m
[0;32mI (14261) UART: uart rx break[0m
[0;32mI (14261) UART: uart[1] event[0m
[0;32mI (14261) UART: uart rx break[0m
[0;32mI (14261) UART: uart[1] event[0m
[0;32mI (14271) UART: uart rx break[0m
[0;32mI (14271) UART: uart[1] event[0m
[0;32mI (14271) UART: uart rx break[0m
[0;32mI (14271) UART: uart[1] event[0m
[0;32mI (14281) UART: uart rx break[0m
[0;32mI (14281) UART: uart[1] event[0m
[0;32mI (14281) UART: uart rx break[0m
[0;32mI (14291) UART: uart[0] event[0m
[0;32mI (14291) UART_READ: : 2[0m
[0;32mI (14291) UART_READ: : : UART_READ_END[0m
[0;32mI (14291) UART READ: [LEN: ]: 694[0m
[0;32mI (14301) UART: uart[1] event[0m
[0;32mI (14301) UART: uart rx break[0m
[0;32mI (14301) UART: uart[1] event[0m
[0;32mI (14311) UART: uart rx break[0m
[0;32mI (14311) UART: uart[1] event[0m
[0;32mI (14311) UART: uart rx break[0m
[0;32mI (14311) UART: uart[1] event[0m
[0;32mI (14321) UART: uart rx break[0m
[0;32mI (14321) UART: uart[1] event[0m
[0;32mI (14321) UART: uart rx break[0m
[0;32mI (14321) UART: uart[1] event[0m
[0;32mI (14331) UART: uart rx break[0m
[0;32mI (14331) UART: uart[1] event[0m
[0;32mI (14331) UART: uart rx break[0m
[0;32mI (14331) UART: uart[1] event[0m
[0;32mI (14341) UART: uart rx break[0m
[0;32mI (14341) UART: uart[1] event[0m
[0;32mI (14341) UART: uart rx break[0m
[0;32mI (14351) UART: uart[1] event[0m
[0;32mI (14351) UART: uart rx break[0m
[0;32mI (14351) UART: uart[1] event[0m
[0;32mI (14351) UART: uart rx break[0m
[0;32mI (14361) UART: uart[1] event[0m
[0;32mI (14361) UART: uart rx break[0m
[0;32mI (14361) UART: uart[1] event[0m
[0;32mI (14361) UART: uart rx break[0m
[0;32mI (14371) UART: uart[1] event[0m
[0;32mI (14371) UART: uart rx break[0m
[0;32mI (14371) UART: uart[1] event[0m
[0;32mI (14381) UART: uart rx break[0m
[0;32mI (14381) UART: uart[1] event[0m
[0;32mI (14381) UART: uart rx break[0m
[0;32mI (14381) UART: uart[1] event[0m
[0;32mI (14391) UART: uart rx break[0m
[0;32mI (14391) UART: uart[0] event[0m
[0;32mI (14391) UART_READ: : 2[0m
[0;32mI (14391) UART_READ: : : UART_READ_END[0m
[0;32mI (14401) UART READ: [LEN: ]: 708[0m
[0;32mI (14401) UART: uart[1] event[0m
[0;32mI (14401) UART: uart rx break[0m
[0;32mI (14411) UART: uart[1] event[0m
[0;32mI (14411) UART: uart rx break[0m
[0;32mI (14411) UART: uart[1] event[0m
[0;32mI (14411) UART: uart rx break[0m
[0;32mI (14421) UART: uart[1] event[0m
[0;32mI (14421) UART: uart rx break[0m
[0;32mI (14421) UART: uart[1] event[0m
[0;32mI (14421) UART: uart rx break[0m
[0;32mI (14431) UART: uart[1] event[0m
[0;32mI (14431) UART: uart rx break[0m
[0;32mI (14431) UART: uart[1] event[0m
[0;32mI (14441) UART: uart rx break[0m
[0;32mI (14441) UART: uart[1] event[0m
[0;32mI (14441) UART: uart rx break[0m
[0;32mI (14441) UART: uart[1] event[0m
[0;32mI (14451) UART: uart rx break[0m
[0;32mI (14451) UART: uart[1] event[0m
[0;32mI (14451) UART: uart rx break[0m
[0;32mI (14451) UART: uart[1] event[0m
[0;32mI (14461) UART: uart rx break[0m
[0;32mI (14461) UART: uart[1] event[0m
[0;32mI (14461) UART: uart rx break[0m
[0;32mI (14461) UART: uart[1] event[0m
[0;32mI (14471) UART: uart rx break[0m
[0;32mI (14471) UART: uart[1] event[0m
[0;32mI (14471) UART: uart rx break[0m
[0;32mI (14481) UART: uart[1] event[0m
[0;32mI (14481) UART: uart rx break[0m
[0;32mI (14481) UART: uart[1] event[0m
[0;32mI (14481) UART: uart rx break[0m
[0;32mI (14491) UART: uart[1] event[0m
[0;32mI (14491) UART: uart rx break[0m
[0;32mI (14491) UART: uart[1] event[0m
[0;32mI (14491) UART: uart rx break[0m
[0;32mI (14501) UART: uart[1] event[0m
[0;32mI (14501) UART: uart rx break[0m
[0;32mI (14501) UART: uart[1] event[0m
[0;32mI (14511) UART: uart rx break[0m
[0;32mI (14511) UART: uart[0] event[0m
[0;32mI (14511) UART_READ: : 2[0m
[0;32mI (14511) UART_READ: : : UART_READ_END[0m
[0;32mI (14521) UART READ: [LEN: ]: 725[0m
[0;32mI (14521) UART: uart[0] event[0m
[0;32mI (14521) UART_READ: : 2[0m
[0;32mI (14521) UART_READ: : : UART_READ_END[0m
[0;32mI (14531) UART READ: [LEN: ]: 734[0m
[0;32mI (14531) UART: uart[1] event[0m
[0;32mI (14531) UART: uart rx break[0m
[0;32mI (14541) UART: uart[1] event[0m
[0;32mI (14541) UART: uart rx break[0m
[0;32mI (14541) UART: uart[1] event[0m
[0;32mI (14541) UART: uart rx break[0m
[0;32mI (14551) UART: uart[1] event[0m
[0;32mI (14551) UART: uart rx break[0m
[0;32mI (14551) UART: uart[1] event[0m
[0;32mI (14551) UART: uart rx break[0m
[0;32mI (14561) UART: uart[1] event[0m
[0;32mI (14561) UART: uart rx break[0m
[0;32mI (14561) UART: uart[1] event[0m
[0;32mI (14571) UART: uart rx break[0m
[0;32mI (14571) UART: uart[1] event[0m
[0;32mI (14571) UART: uart rx break[0m
[0;32mI (14571) UART: uart[0] event[0m
[0;32mI (14581) UART_READ: : 2[0m
[0;32mI (14581) UART_READ: : : UART_READ_END[0m
[0;32mI (14581) UART READ: [LEN: ]: 766[0m
[0;32mI (14591) UART: uart[1] event[0m
[0;32mI (14591) UART: uart rx break[0m
[0;32mI (14591) UART: uart[1] event[0m
[0;32mI (14591) UART: uart rx break[0m
[0;32mI (14601) UART: uart[1] event[0m
[0;32mI (14601) UART: uart rx break[0m
[0;32mI (14601) UART: uart[1] event[0m
[0;32mI (14601) UART: uart rx break[0m
[0;32mI (14611) UART: uart[1] event[0m
[0;32mI (14611) UART: uart rx break[0m
[0;32mI (14611) UART: uart[1] event[0m
[0;32mI (14611) UART: uart rx break[0m
[0;32mI (14621) UART: uart[1] event[0m
[0;32mI (14621) UART: uart rx break[0m
[0;32mI (14621) UART: uart[0] event[0m
[0;32mI (14631) UART_READ: : 2[0m
[0;32mI (14631) UART_READ: : : UART_READ_END[0m
[0;32mI (14631) UART READ: [LEN: ]: 767[0m
[0;32mI (14631) UART: uart[1] event[0m
[0;32mI (14641) UART: uart rx break[0m
[0;32mI (14641) UART: uart[1] event[0m
[0;32mI (14641) UART: uart rx break[0m
[0;32mI (14651) UART: uart[1] event[0m
[0;32mI (14651) UART: uart rx break[0m
[0;32mI (14651) UART: uart[1] event[0m
[0;32mI (14651) UART: uart rx break[0m
[0;32mI (14661) UART: uart[1] event[0m
[0;32mI (14661) UART: uart rx break[0m
[0;32mI (14661) UART: uart[1] event[0m
[0;32mI (14661) UART: uart rx break[0m
[0;32mI (14671) UART: uart[1] event[0m
[0;32mI (14671) UART: uart rx break[0m
[0;32mI (14671) UART: uart[1] event[0m
[0;32mI (14671) UART: uart rx break[0m
[0;32mI (14681) UART: uart[1] event[0m
[0;32mI (14681) UART: uart rx break[0m
[0;32mI (14681) UART: uart[0] event[0m
[0;32mI (14691) UART_READ: : 2[0m
[0;32mI (14691) UART_READ: : : UART_READ_END[0m
[0;32mI (14691) UART READ: [LEN: ]: 803[0m
[0;32mI (14691) UART: uart[1] event[0m
[0;32mI (14701) UART: uart rx break[0m
[0;32mI (14701) UART: uart[1] event[0m
[0;32mI (14701) UART: uart rx break[0m
[0;32mI (14701) UART: uart[1] event[0m
[0;32mI (14711) UART: uart rx break[0m
[0;32mI (14711) UART: uart[1] event[0m
[0;32mI (14711) UART: uart rx break[0m
[0;32mI (14721) UART: uart[1] event[0m
[0;32mI (14721) UART: uart rx break[0m
[0;32mI (14721) UART: uart[1] event[0m
[0;32mI (14721) UART: uart rx break[0m
[0;32mI (14731) UART: uart[1] event[0m
[0;32mI (14731) UART: uart rx break[0m
[0;32mI (14731) UART: uart[1] event[0m
[0;32mI (14731) UART: uart rx break[0m
[0;32mI (14741) UART: uart[1] event[0m
[0;32mI (14741) UART: uart rx break[0m
[0;32mI (14741) UART: uart[1] event[0m
[0;32mI (14751) UART: uart rx break[0m
[0;32mI (14751) UART: uart[1] event[0m
[0;32mI (14751) UART: uart rx break[0m
[0;32mI (14751) UART: uart[0] event[0m
[0;32mI (14761) UART_READ: : 2[0m
[0;32mI (14761) UART_READ: : : UART_READ_END[0m
[0;32mI (14761) UART READ: [LEN: ]: 846[0m
[0;32mI (14761) UART: uart[1] event[0m
[0;32mI (14771) UART: uart rx break[0m
[0;32mI (14771) UART: uart[1] event[0m
[0;32mI (14771) UART: uart rx break[0m
[0;32mI (14781) UART: uart[1] event[0m
[0;32mI (14781) UART: uart rx break[0m
[0;32mI (14781) UART: uart[1] event[0m
[0;32mI (14781) UART: uart rx break[0m
[0;32mI (14791) UART: uart[1] event[0m
[0;32mI (14791) UART: uart rx break[0m
[0;32mI (14791) UART: uart[1] event[0m
[0;32mI (14791) UART: uart rx break[0m
[0;32mI (14801) UART: uart[1] event[0m
[0;32mI (14801) UART: uart rx break[0m
[0;32mI (14801) UART: uart[1] event[0m
[0;32mI (14811) UART: uart rx break[0m
[0;32mI (14811) UART: uart[1] event[0m
[0;32mI (14811) UART: uart rx break[0m
[0;32mI (14811) UART: uart[0] event[0m
[0;32mI (14821) UART_READ: : 2[0m
[0;32mI (14821) UART_READ: : : UART_READ_END[0m
[0;32mI (14821) UART READ: [LEN: ]: 886[0m
[0;32mI (14821) UART: uart[1] event[0m
[0;32mI (14831) UART: uart rx break[0m
[0;32mI (14831) UART: uart[1] event[0m
[0;32mI (14831) UART: uart rx break[0m
[0;32mI (14841) UART: uart[1] event[0m
[0;32mI (14841) UART: uart rx break[0m
[0;32mI (14841) UART: uart[1] event[0m
[0;32mI (14841) UART: uart rx break[0m
[0;32mI (14851) UART: uart[1] event[0m
[0;32mI (14851) UART: uart rx break[0m
[0;32mI (14851) UART: uart[1] event[0m
[0;32mI (14851) UART: uart rx break[0m
[0;32mI (14861) UART: uart[1] event[0m
[0;32mI (14861) UART: uart rx break[0m
[0;32mI (14861) UART: uart[1] event[0m
[0;32mI (14871) UART: uart rx break[0m
[0;32mI (14871) UART: uart[1] event[0m
[0;32mI (14871) UART: uart rx break[0m
[0;32mI (14871) UART: uart[0] event[0m
[0;32mI (14881) UART_READ: : 2[0m
[0;32mI (14881) UART_READ: : : UART_READ_END[0m
[0;32mI (14881) UART READ: [LEN: ]: 927[0m
[0;32mI (14881) UART: uart[1] event[0m
[0;32mI (14891) UART: uart rx break[0m
[0;32mI (14891) UART: uart[1] event[0m
[0;32mI (14891) UART: uart rx break[0m
[0;32mI (14901) UART: uart[1] event[0m
[0;32mI (14901) UART: uart rx break[0m
[0;32mI (14901) UART: uart[1] event[0m
[0;32mI (14901) UART: uart rx break[0m
[0;32mI (14911) UART: uart[1] event[0m
[0;32mI (14911) UART: uart rx break[0m
[0;32mI (14911) UART: uart[1] event[0m
[0;32mI (14911) UART: uart rx break[0m
[0;32mI (14921) UART: uart[0] event[0m
[0;32mI (14921) UART_READ: : 2[0m
[0;32mI (14921) UART_READ: : : UART_READ_END[0m
[0;32mI (14931) UART READ: [LEN: ]: 948[0m
[0;32mI (14931) UART: uart[1] event[0m
[0;32mI (14931) UART: uart rx break[0m
[0;32mI (14931) UART: uart[1] event[0m
[0;32mI (14941) UART: uart rx break[0m
[0;32mI (14941) UART: uart[1] event[0m
[0;32mI (14941) UART: uart rx break[0m
[0;32mI (14941) UART: uart[0] event[0m
[0;32mI (14951) UART_READ: : 2[0m
[0;32mI (14951) UART_READ: : : UART_READ_END[0m
[0;32mI (14951) UART READ: [LEN: ]: 966[0m
[0;32mI (14961) UART: uart[1] event[0m
[0;32mI (14961) UART: uart rx break[0m
[0;32mI (14961) UART: uart[1] event[0m
[0;32mI (14961) UART: uart rx break[0m
[0;32mI (14971) UART: uart[1] event[0m
[0;32mI (14971) UART: uart rx break[0m
[0;32mI (14971) UART: uart[1] event[0m
[0;32mI (14971) UART: uart rx break[0m
[0;32mI (14981) UART: uart[1] event[0m
[0;32mI (14981) UART: uart rx break[0m
[0;32mI (14981) UART: uart[1] event[0m
[0;32mI (14991) UART: uart rx break[0m
[0;32mI (14991) UART: uart[1] event[0m
[0;32mI (14991) UART: uart rx break[0m
[0;32mI (14991) UART: uart[1] event[0m
[0;32mI (15001) UART: uart rx break[0m
[0;32mI (15001) UART: uart[1] event[0m
[0;32mI (15001) UART: uart rx break[0m
[0;32mI (15001) UART: uart[1] event[0m
[0;32mI (15011) UART: uart rx break[0m
[0;32mI (15011) UART: uart[1] event[0m
[0;32mI (15011) UART: uart rx break[0m
[0;32mI (15021) UART: uart[1] event[0m
[0;32mI (15021) UART: uart rx break[0m
[0;32mI (15021) UART: uart[1] event[0m
[0;32mI (15021) UART: uart rx break[0m
[0;32mI (15031) UART: uart[1] event[0m
[0;32mI (15031) UART: uart rx break[0m
[0;32mI (15031) UART: uart[1] event[0m
[0;32mI (15031) UART: uart rx break[0m
[0;32mI (15041) UART: uart[1] event[0m
[0;32mI (15041) UART: uart rx break[0m
[0;32mI (15041) UART: uart[1] event[0m
[0;32mI (15041) UART: uart rx break[0m
[0;32mI (15051) UART: uart[1] event[0m
[0;32mI (15051) UART: uart rx break[0m
[0;32mI (15051) UART: uart[1] event[0m
[0;32mI (15061) UART: uart rx break[0m
[0;32mI (15061) UART: uart[1] event[0m
[0;32mI (15061) UART: uart rx break[0m
[0;32mI (15061) UART: uart[1] event[0m
[0;32mI (15071) UART: uart rx break[0m
[0;32mI (15071) UART: uart[1] event[0m
[0;32mI (15071) UART: uart rx break[0m
[0;32mI (15071) UART: uart[1] event[0m
[0;32mI (15081) UART: uart rx break[0m
[0;32mI (15081) UART: uart[1] event[0m
[0;32mI (15081) UART: uart rx break[0m
[0;32mI (15091) UART: uart[1] event[0m
[0;32mI (15091) UART: uart rx break[0m
[0;32mI (15091) UART: uart[1] event[0m
[0;32mI (15091) UART: uart rx break[0m
[0;32mI (15101) UART: uart[1] event[0m
[0;32mI (15101) UART: uart rx break[0m
[0;32mI (15101) UART: uart[1] event[0m
[0;32mI (15101) UART: uart rx break[0m
[0;32mI (15111) UART: uart[1] event[0m
[0;32mI (15111) UART: uart rx break[0m
[0;32mI (15111) UART: uart[1] event[0m
[0;32mI (15111) UART: uart rx break[0m
[0;32mI (15121) UART: uart[1] event[0m
[0;32mI (15121) UART: uart rx break[0m
[0;32mI (15121) UART: uart[1] event[0m
[0;32mI (15131) UART: uart rx break[0m
[0;32mI (15131) UART: uart[1] event[0m
[0;32mI (15131) UART: uart rx break[0m
[0;32mI (15131) UART: uart[1] event[0m
[0;32mI (15141) UART: uart rx break[0m
[0;32mI (15141) UART: uart[1] event[0m
[0;32mI (15141) UART: uart rx break[0m
[0;32mI (15141) UART: uart[1] event[0m
[0;32mI (15151) UART: uart rx break[0m
[0;32mI (15151) UART: uart[1] event[0m
[0;32mI (15151) UART: uart rx break[0m
[0;32mI (15161) UART: uart[1] event[0m
[0;32mI (15161) UART: uart rx break[0m
[0;32mI (15161) UART: uart[1] event[0m
[0;32mI (15161) UART: uart rx break[0m
[0;32mI (15171) UART: uart[1] event[0m
[0;32mI (15171) UART: uart rx break[0m
[0;32mI (15171) UART: uart[1] event[0m
[0;32mI (15171) UART: uart rx break[0m
[0;32mI (15181) UART: uart[0] event[0m
[0;32mI (15181) UART_READ: : 2[0m
[0;32mI (15181) UART_READ: : : UART_READ_END[0m
[0;32mI (15191) UART READ: [LEN: ]: 984[0m
[0;32mI (15191) UART: uart[1] event[0m
[0;32mI (15191) UART: uart rx break[0m
[0;32mI (15191) UART: uart[1] event[0m
[0;32mI (15201) UART: uart rx break[0m
[0;32mI (15201) UART: uart[1] event[0m
[0;32mI (15201) UART: uart rx break[0m
[0;32mI (15201) UART: uart[1] event[0m
[0;32mI (15211) UART: uart rx break[0m
[0;32mI (15211) UART: uart[1] event[0m
[0;32mI (15211) UART: uart rx break[0m
[0;32mI (15211) UART: uart[1] event[0m
[0;32mI (15221) UART: uart rx break[0m
[0;32mI (15221) UART: uart[1] event[0m
[0;32mI (15221) UART: uart rx break[0m
[0;32mI (15231) UART: uart[1] event[0m
[0;32mI (15231) UART: uart rx break[0m
[0;32mI (15231) UART: uart[1] event[0m
[0;32mI (15231) UART: uart rx break[0m
[0;32mI (15241) UART: uart[1] event[0m
[0;32mI (15241) UART: uart rx break[0m
[0;32mI (15241) UART: uart[1] event[0m
[0;32mI (15241) UART: uart rx break[0m
[0;32mI (15251) UART: uart[1] event[0m
[0;32mI (15251) UART: uart rx break[0m
[0;32mI (15251) UART: uart[1] event[0m
[0;32mI (15261) UART: uart rx break[0m
[0;32mI (15261) UART: uart[1] event[0m
[0;32mI (15261) UART: uart rx break[0m
[0;32mI (15261) UART: uart[1] event[0m
[0;32mI (15271) UART: uart rx break[0m
[0;32mI (15271) UART: uart[1] event[0m
[0;32mI (15271) UART: uart rx break[0m
[0;32mI (15271) UART: uart[1] event[0m
[0;32mI (15281) UART: uart rx break[0m
[0;32mI (15281) UART: uart[1] event[0m
[0;32mI (15281) UART: uart rx break[0m
[0;32mI (15281) UART: uart[1] event[0m
[0;32mI (15291) UART: uart rx break[0m
[0;32mI (15291) UART: uart[1] event[0m
[0;32mI (15291) UART: uart rx break[0m
[0;32mI (15301) UART: uart[1] event[0m
[0;32mI (15301) UART: uart rx break[0m
[0;32mI (15301) UART: uart[1] event[0m
[0;32mI (15301) UART: uart rx break[0m
[0;32mI (15311) UART: uart[1] event[0m
[0;32mI (15311) UART: uart rx break[0m
[0;32mI (15311) UART: uart[0] event[0m
[0;32mI (15311) UART_READ: : 2[0m
[0;32mI (15321) UART_READ: : : UART_READ_END[0m
[0;32mI (15321) UART READ: [LEN: ]: 1002[0m
[0;32mI (15321) UART: uart[1] event[0m
[0;32mI (15331) UART: uart rx break[0m
[0;32mI (15331) UART: uart[1] event[0m
[0;32mI (15331) UART: uart rx break[0m
[0;32mI (15331) UART: uart[1] event[0m
[0;32mI (15341) UART: uart rx break[0m
[0;32mI (15341) UART: uart[1] event[0m
[0;32mI (15341) UART: uart rx break[0m
[0;32mI (15341) UART: uart[1] event[0m
[0;32mI (15351) UART: uart rx break[0m
[0;32mI (15351) UART: uart[0] event[0m
[0;32mI (15351) UART_READ: : 2[0m
[0;32mI (15361) UART_READ: : : UART_READ_END[0m
[0;32mI (15361) UART READ: [LEN: ]: 1020[0m
[0;32mI (15361) UART: uart[1] event[0m
[0;32mI (15361) UART: uart rx break[0m
[0;32mI (15371) UART: uart[1] event[0m
[0;32mI (15371) UART: uart rx break[0m
[0;32mI (15371) UART: uart[1] event[0m
[0;32mI (15381) UART: uart rx break[0m
[0;32mI (15381) UART: uart[0] event[0m
[0;32mI (15381) UART_READ: : 2[0m
[0;32mI (15381) UART_READ: : : UART_READ_END[0m
[0;32mI (15391) UART READ: [LEN: ]: 1037[0m
[0;32mI (15391) UART: uart[1] event[0m
[0;32mI (15391) UART: uart rx break[0m
[0;32mI (15391) UART: uart[1] event[0m
[0;32mI (15401) UART: uart rx break[0m
[0;32mI (15401) UART: uart[1] event[0m
[0;32mI (15401) UART: uart rx break[0m
[0;32mI (15411) UART: uart[0] event[0m
[0;32mI (15411) UART_READ: : 2[0m
[0;32mI (15411) UART_READ: : : UART_READ_END[0m
[0;32mI (15411) UART READ: [LEN: ]: 1045[0m
[0;32mI (15421) UART: uart[1] event[0m
[0;32mI (15421) UART: uart rx break[0m
[0;32mI (15421) UART: uart[1] event[0m
[0;32mI (15431) UART: uart rx break[0m
[0;32mI (15431) UART: uart[1] event[0m
[0;32mI (15431) UART: uart rx break[0m
[0;32mI (15431) UART: uart[0] event[0m
[0;32mI (15441) UART_READ: : 2[0m
[0;32mI (15441) UART_READ: : : UART_READ_END[0m
[0;32mI (15441) UART READ: [LEN: ]: 1053[0m
[0;32mI (15441) UART: uart[1] event[0m
[0;32mI (15451) UART: uart rx break[0m
[0;32mI (15451) UART: uart[1] event[0m
[0;32mI (15451) UART: uart rx break[0m
[0;32mI (15461) UART: uart[1] event[0m
[0;32mI (15461) UART: uart rx break[0m
[0;32mI (15461) UART: uart[0] event[0m
[0;32mI (15461) UART_READ: : 2[0m
[0;32mI (15471) UART_READ: : : UART_READ_END[0m
[0;32mI (15471) UART READ: [LEN: ]: 1066[0m
[0;32mI (15471) UART: uart[1] event[0m
[0;32mI (15481) UART: uart rx break[0m
[0;32mI (15481) UART: uart[1] event[0m
[0;32mI (15481) UART: uart rx break[0m
[0;32mI (15481) UART: uart[0] event[0m
[0;32mI (15491) UART_READ: : 2[0m
[0;32mI (15491) UART_READ: : : UART_READ_END[0m
[0;32mI (15491) UART READ: [LEN: ]: 1072[0m
[0;32mI (15491) UART: uart[1] event[0m
[0;32mI (15501) UART: uart rx break[0m
[0;32mI (15501) UART: uart[1] event[0m
[0;32mI (15501) UART: uart rx break[0m
[0;32mI (15511) UART: uart[1] event[0m
[0;32mI (15511) UART: uart rx break[0m
[0;32mI (15511) UART: uart[1] event[0m
[0;32mI (15511) UART: uart rx break[0m
[0;32mI (15521) UART: uart[0] event[0m
[0;32mI (15521) UART_READ: : 2[0m
[0;32mI (15521) UART_READ: : : UART_READ_END[0m
[0;32mI (15521) UART READ: [LEN: ]: 1090[0m
[0;32mI (15531) UART: uart[1] event[0m
[0;32mI (15531) UART: uart rx break[0m
[0;32mI (15531) UART: uart[0] event[0m
[0;32mI (15541) UART_READ: : 2[0m
[0;32mI (15541) UART_READ: : : UART_READ_END[0m
[0;32mI (15541) UART READ: [LEN: ]: 1103[0m
[0;32mI (15541) UART: uart[1] event[0m
[0;32mI (15551) UART: uart rx break[0m
[0;32mI (15551) UART: uart[1] event[0m
[0;32mI (15551) UART: uart rx break[0m
[0;32mI (15561) UART: uart[1] event[0m
[0;32mI (15561) UART: uart rx break[0m
[0;32mI (15561) UART: uart[1] event[0m
[0;32mI (15561) UART: uart rx break[0m
[0;32mI (15571) UART: uart[0] event[0m
[0;32mI (15571) UART_READ: : 2[0m
[0;32mI (15571) UART_READ: : : UART_READ_END[0m
[0;32mI (15571) UART READ: [LEN: ]: 1117[0m
[0;32mI (15581) UART: uart[1] event[0m
[0;32mI (15581) UART: uart rx break[0m
[0;32mI (15581) UART: uart[0] event[0m
[0;32mI (15591) UART_READ: : 2[0m
[0;32mI (15591) UART_READ: : : UART_READ_END[0m
[0;32mI (15591) UART READ: [LEN: ]: 1131[0m
[0;32mI (15591) UART: uart[0] event[0m
[0;32mI (15601) UART_READ: : 2[0m
[0;32mI (15601) UART_READ: : : UART_READ_END[0m
[0;32mI (15601) UART READ: [LEN: ]: 1132[0m
[0;32mI (15611) UART: uart[1] event[0m
[0;32mI (15611) UART: uart rx break[0m
[0;32mI (15611) UART: uart[1] event[0m
[0;32mI (15611) UART: uart rx break[0m
[0;32mI (15621) UART: uart[1] event[0m
[0;32mI (15621) UART: uart rx break[0m
[0;32mI (15621) UART: uart[1] event[0m
[0;32mI (15631) UART: uart rx break[0m
[0;32mI (15631) UART: uart[0] event[0m
[0;32mI (15631) UART_READ: : 2[0m
[0;32mI (15631) UART_READ: : : UART_READ_END[0m
[0;32mI (15641) UART READ: [LEN: ]: 1148[0m
[0;32mI (15641) UART: uart[1] event[0m
[0;32mI (15641) UART: uart rx break[0m
[0;32mI (15641) UART: uart[1] event[0m
[0;32mI (15651) UART: uart rx break[0m
[0;32mI (15651) UART: uart[1] event[0m
[0;32mI (15651) UART: uart rx break[0m
[0;32mI (15661) UART: uart[1] event[0m
[0;32mI (15661) UART: uart rx break[0m
[0;32mI (15661) UART: uart[1] event[0m
[0;32mI (15661) UART: uart rx break[0m
[0;32mI (15671) UART: uart[0] event[0m
[0;32mI (15671) UART_READ: : 2[0m
[0;32mI (15671) UART_READ: : : UART_READ_END[0m
[0;32mI (15671) UART READ: [LEN: ]: 1161[0m
[0;32mI (15681) UART: uart[1] event[0m
[0;32mI (15681) UART: uart rx break[0m
[0;32mI (15681) UART: uart[1] event[0m
[0;32mI (15691) UART: uart rx break[0m
[0;32mI (15691) UART: uart[1] event[0m
[0;32mI (15691) UART: uart rx break[0m
[0;32mI (15691) UART: uart[1] event[0m
[0;32mI (15701) UART: uart rx break[0m
[0;32mI (15701) UART: uart[1] event[0m
[0;32mI (15701) UART: uart rx break[0m
[0;32mI (15701) UART: uart[1] event[0m
[0;32mI (15711) UART: uart rx break[0m
[0;32mI (15711) UART: uart[1] event[0m
[0;32mI (15711) UART: uart rx break[0m
[0;32mI (15721) UART: uart[1] event[0m
[0;32mI (15721) UART: uart rx break[0m
[0;32mI (15721) UART: uart[0] event[0m
[0;32mI (15721) UART_READ: : 2[0m
[0;32mI (15731) UART_READ: : : UART_READ_END[0m
[0;32mI (15731) UART READ: [LEN: ]: 1179[0m
[0;32mI (15731) UART: uart[1] event[0m
[0;32mI (15741) UART: uart rx break[0m
[0;32mI (15741) UART: uart[1] event[0m
[0;32mI (15741) UART: uart rx break[0m
[0;32mI (15741) UART: uart[1] event[0m
[0;32mI (15751) UART: uart rx break[0m
[0;32mI (15751) UART: uart[1] event[0m
[0;32mI (15751) UART: uart rx break[0m
[0;32mI (15751) UART: uart[1] event[0m
[0;32mI (15761) UART: uart rx break[0m
[0;32mI (15761) UART: uart[0] event[0m
[0;32mI (15761) UART_READ: : 2[0m
[0;32mI (15761) UART_READ: : : UART_READ_END[0m
[0;32mI (15771) UART READ: [LEN: ]: 1190[0m
[0;32mI (15771) UART: uart[1] event[0m
[0;32mI (15771) UART: uart rx break[0m
[0;32mI (15781) UART: uart[0] event[0m
[0;32mI (15781) UART_READ: : 2[0m
[0;32mI (15781) UART_READ: : : UART_READ_END[0m
[0;32mI (15781) UART READ: [LEN: ]: 1203[0m
[0;32mI (15791) UART: uart[1] event[0m
[0;32mI (15791) UART: uart rx break[0m
[0;32mI (15791) UART: uart[1] event[0m
[0;32mI (15801) UART: uart rx break[0m
[0;32mI (15801) UART: uart[1] event[0m
[0;32mI (15801) UART: uart rx break[0m
[0;32mI (15801) UART: uart[1] event[0m
[0;32mI (15811) UART: uart rx break[0m
[0;32mI (15811) UART: uart[0] event[0m
[0;32mI (15811) UART_READ: : 2[0m
[0;32mI (15811) UART_READ: : : UART_READ_END[0m
[0;32mI (15821) UART READ: [LEN: ]: 1220[0m
[0;32mI (15821) UART: uart[1] event[0m
[0;32mI (15821) UART: uart rx break[0m
[0;32mI (15831) UART: uart[1] event[0m
[0;32mI (15831) UART: uart rx break[0m
[0;32mI (15831) UART: uart[0] event[0m
[0;32mI (15831) UART_READ: : 2[0m
[0;32mI (15841) UART_READ: : : UART_READ_END[0m
[0;32mI (15841) UART READ: [LEN: ]: 1239[0m
[0;32mI (15841) UART: uart[1] event[0m
[0;32mI (15851) UART: uart rx break[0m
[0;32mI (15851) UART: uart[1] event[0m
[0;32mI (15851) UART: uart rx break[0m
[0;32mI (15851) UART: uart[1] event[0m
[0;32mI (15861) UART: uart rx break[0m
[0;32mI (15861) UART: uart[1] event[0m
[0;32mI (15861) UART: uart rx break[0m
[0;32mI (15861) UART: uart[1] event[0m
[0;32mI (15871) UART: uart rx break[0m
[0;32mI (15871) UART: uart[1] event[0m
[0;32mI (15871) UART: uart rx break[0m
[0;32mI (15881) UART: uart[1] event[0m
[0;32mI (15881) UART: uart rx break[0m
[0;32mI (15881) UART: uart[1] event[0m
[0;32mI (15881) UART: uart rx break[0m
[0;32mI (15891) UART: uart[1] event[0m
[0;32mI (15891) UART: uart rx break[0m
[0;32mI (15891) UART: uart[1] event[0m
[0;32mI (15891) UART: uart rx break[0m
[0;32mI (15901) UART: uart[1] event[0m
[0;32mI (15901) UART: uart rx break[0m
[0;32mI (15901) UART: uart[1] event[0m
[0;32mI (15911) UART: uart rx break[0m
[0;32mI (15911) UART: uart[1] event[0m
[0;32mI (15911) UART: uart rx break[0m
[0;32mI (15911) UART: uart[1] event[0m
[0;32mI (15921) UART: uart rx break[0m
[0;32mI (15921) UART: uart[1] event[0m
[0;32mI (15921) UART: uart rx break[0m
[0;32mI (15921) UART: uart[1] event[0m
[0;32mI (15931) UART: uart rx break[0m
I (15E (15931) task_wdt: Task watchdog got triggered. The following tasks/users did not reset the watchdog in time:
[1;31mE (15931) task_wdt:  - IDLE0 (CPU 0)[0m
[1;31mE (15931) task_wdt: Tasks currently running:[0m
[1;31mE (15931) task_wdt: CPU 0: uart_event_task[0m
[1;31mE (15931) task_wdt: CPU 1: IDLE1[0m
[1;31mE (15931) task_wdt: Print CPU 0 (current core) backtrace[0m


Backtrace: 0x42037E4E:0x3FC9D400 0x42038264:0x3FC9D420 0x4037BE19:0x3FC9D450 0x420082FD:0x3FCA2F00 0x42009217:0x3FCA2F30 0x4200CA12:0x3FCA2F50 0x42050ABE:0x3FCA2F70 0x4200C150:0x3FCA2F90 0x4200CA12:0x3FCA2FB0 0x42050436:0x3FCA2FD0 0x4204F929:0x3FCA2FF0 0x4204F97A:0x3FCA3010 0x42050061:0x3FCA3030 0x420571A3:0x3FCA3060 0x42056BAA:0x3FCA3080 0x420504AD:0x3FCA33A0 0x42061A46:0x3FCA33D0 0x4038979D:0x3FCA3400 0x4200DEAE:0x3FCA3450 0x40383701:0x3FCA3480

931) UART: uart[1] event
[0;32mI (16001) UART: uart rx break[0m
[0;32mI (16001) UART: uart[1] event[0m
[0;32mI (16011) UART: uart rx break[0m
[0;32mI (16011) UART: uart[1] event[0m
[0;32mI (16011) UART: uart rx break[0m
[0;32mI (16011) UART: uart[1] event[0m
[0;32mI (16021) UART: uart rx break[0m
[0;32mI (16021) UART: uart[1] event[0m
[0;32mI (16021) UART: uart rx break[0m
[0;32mI (16021) UART: uart[1] event[0m
[0;32mI (16031) UART: uart rx break[0m
[0;32mI (16031) UART: uart[1] event[0m
[0;32mI (16031) UART: uart rx break[0m
[0;32mI (16041) UART: uart[0] event[0m
[0;32mI (16041) UART_READ: : 2[0m
[0;32mI (16041) UART_READ: : : UART_READ_END[0m
[0;32mI (16041) UART READ: [LEN: ]: 1251[0m
[0;32mI (16051) UART: uart[1] event[0m
[0;32mI (16051) UART: uart rx break[0m
[0;32mI (16051) UART: uart[1] event[0m
[0;32mI (16061) UART: uart rx break[0m
[0;32mI (16061) UART: uart[1] event[0m
[0;32mI (16061) UART: uart rx break[0m
[0;32mI (16061) UART: uart[1] event[0m
[0;32mI (16071) UART: uart rx break[0m
[0;32mI (16071) UART: uart[1] event[0m
[0;32mI (16071) UART: uart rx break[0m
[0;32mI (16071) UART: uart[1] event[0m
[0;32mI (16081) UART: uart rx break[0m
[0;32mI (16081) UART: uart[1] event[0m
[0;32mI (16081) UART: uart rx break[0m
[0;32mI (16081) UART: uart[1] event[0m
[0;32mI (16091) UART: uart rx break[0m
[0;32mI (16091) UART: uart[0] event[0m
[0;32mI (16091) UART_READ: : 2[0m
[0;32mI (16101) UART_READ: : : UART_READ_END[0m
[0;32mI (16101) UART READ: [LEN: ]: 1265[0m
[0;32mI (16101) UART: uart[1] event[0m
[0;32mI (16101) UART: uart rx break[0m
[0;32mI (16111) UART: uart[1] event[0m
[0;32mI (16111) UART: uart rx break[0m
[0;32mI (16111) UART: uart[1] event[0m
[0;32mI (16121) UART: uart rx break[0m
[0;32mI (16121) UART: uart[0] event[0m
[0;32mI (16121) UART_READ: : 2[0m
[0;32mI (16121) UART_READ: : : UART_READ_END[0m
[0;32mI (16131) UART READ: [LEN: ]: 1274[0m
[0;32mI (16131) UART: uart[1] event[0m
[0;32mI (16131) UART: uart rx break[0m
[0;32mI (16131) UART: uart[1] event[0m
[0;32mI (16141) UART: uart rx break[0m
[0;32mI (16141) UART: uart[1] event[0m
[0;32mI (16141) UART: uart rx break[0m
[0;32mI (16151) UART: uart[1] event[0m
[0;32mI (16151) UART: uart rx break[0m
[0;32mI (16151) UART: uart[1] event[0m
[0;32mI (16151) UART: uart rx break[0m
[0;32mI (16161) UART: uart[1] event[0m
[0;32mI (16161) UART: uart rx break[0m
[0;32mI (16161) UART: uart[1] event[0m
[0;32mI (16161) UART: uart rx break[0m
[0;32mI (16171) UART: uart[1] event[0m
[0;32mI (16171) UART: uart rx break[0m
[0;32mI (16171) UART: uart[1] event[0m
[0;32mI (16181) UART: uart rx break[0m
[0;32mI (16181) UART: uart[1] event[0m
[0;32mI (16181) UART: uart rx break[0m
[0;32mI (16181) UART: uart[1] event[0m
[0;32mI (16191) UART: uart rx break[0m
[0;32mI (16191) UART: uart[1] event[0m
[0;32mI (16191) UART: uart rx break[0m
[0;32mI (16191) UART: uart[1] event[0m
[0;32mI (16201) UART: uart rx break[0m
[0;32mI (16201) UART: uart[1] event[0m
[0;32mI (16201) UART: uart rx break[0m
[0;32mI (16201) UART: uart[1] event[0m
[0;32mI (16211) UART: uart rx break[0m
[0;32mI (16211) UART: uart[1] event[0m
[0;32mI (16211) UART: uart rx break[0m
[0;32mI (16221) UART: uart[1] event[0m
[0;32mI (16221) UART: uart rx break[0m
[0;32mI (16221) UART: uart[1] event[0m
[0;32mI (16221) UART: uart rx break[0m
[0;32mI (16231) UART: uart[1] event[0m
[0;32mI (16231) UART: uart rx break[0m
[0;32mI (16231) UART: uart[1] event[0m
[0;32mI (16231) UART: uart rx break[0m
[0;32mI (16241) UART: uart[1] event[0m
[0;32mI (16241) UART: uart rx break[0m
[0;32mI (16241) UART: uart[1] event[0m
[0;32mI (16251) UART: uart rx break[0m
[0;32mI (16251) UART: uart[1] event[0m
[0;32mI (16251) UART: uart rx break[0m
[0;32mI (16251) UART: uart[1] event[0m
[0;32mI (16261) UART: uart rx break[0m
[0;32mI (16261) UART: uart[1] event[0m
[0;32mI (16261) UART: uart rx break[0m
[0;32mI (16261) UART: uart[1] event[0m
[0;32mI (16271) UART: uart rx break[0m
[0;32mI (16271) UART: uart[1] event[0m
[0;32mI (16271) UART: uart rx break[0m
[0;32mI (16271) UART: uart[1] event[0m
[0;32mI (16281) UART: uart rx break[0m
[0;32mI (16281) UART: uart[1] event[0m
[0;32mI (16281) UART: uart rx break[0m
[0;32mI (16291) UART: uart[1] event[0m
[0;32mI (16291) UART: uart rx break[0m
[0;32mI (16291) UART: uart[1] event[0m
[0;32mI (16291) UART: uart rx break[0m
[0;32mI (16301) UART: uart[1] event[0m
[0;32mI (16301) UART: uart rx break[0m
[0;32mI (16301) UART: uart[1] event[0m
[0;32mI (16301) UART: uart rx break[0m
[0;32mI (16311) UART: uart[1] event[0m
[0;32mI (16311) UART: uart rx break[0m
[0;32mI (16311) UART: uart[1] event[0m
[0;32mI (16321) UART: uart rx break[0m
[0;32mI (16321) UART: uart[1] event[0m
[0;32mI (16321) UART: uart rx break[0m
[0;32mI (16321) UART: uart[1] event[0m
[0;32mI (16331) UART: uart rx break[0m
[0;32mI (16331) UART: uart[1] event[0m
[0;32mI (16331) UART: uart rx break[0m
[0;32mI (16331) UART: uart[1] event[0m
[0;32mI (16341) UART: uart rx break[0m
[0;32mI (16341) UART: uart[1] event[0m
[0;32mI (16341) UART: uart rx break[0m
[0;32mI (16341) UART: uart[1] event[0m
[0;32mI (16351) UART: uart rx break[0m
[0;32mI (16351) UART: uart[1] event[0m
[0;32mI (16351) UART: uart rx break[0m
[0;32mI (16361) UART: uart[1] event[0m
[0;32mI (16361) UART: uart rx break[0m
[0;32mI (16361) UART: uart[1] event[0m
[0;32mI (16361) UART: uart rx break[0m
[0;32mI (16371) UART: uart[1] event[0m
[0;32mI (16371) UART: uart rx break[0m
[0;32mI (16371) UART: uart[1] event[0m
[0;32mI (16371) UART: uart rx break[0m
[0;32mI (16381) UART: uart[1] event[0m
[0;32mI (16381) UART: uart rx break[0m
[0;32mI (16381) UART: uart[1] event[0m
[0;32mI (16391) UART: uart rx break[0m
[0;32mI (16391) UART: uart[1] event[0m
[0;32mI (16391) UART: uart rx break[0m
[0;32mI (16391) UART: uart[1] event[0m
[0;32mI (16401) UART: uart rx break[0m
[0;32mI (16401) UART: uart[1] event[0m
[0;32mI (16401) UART: uart rx break[0m
[0;32mI (16401) UART: uart[1] event[0m
[0;32mI (16411) UART: uart rx break[0m
[0;32mI (16411) UART: uart[1] event[0m
[0;32mI (16411) UART: uart rx break[0m
[0;32mI (16411) UART: uart[1] event[0m
[0;32mI (16421) UART: uart rx break[0m
[0;32mI (16421) UART: uart[1] event[0m
[0;32mI (16421) UART: uart rx break[0m
[0;32mI (16431) UART: uart[1] event[0m
[0;32mI (16431) UART: uart rx break[0m
[0;32mI (16431) UART: uart[1] event[0m
[0;32mI (16431) UART: uart rx break[0m
[0;32mI (16441) UART: uart[1] event[0m
[0;32mI (16441) UART: uart rx break[0m
[0;32mI (16441) UART: uart[0] event[0m
[0;32mI (16441) UART_READ: : 2[0m
[0;32mI (16451) UART_READ: : : UART_READ_END[0m
[0;32mI (16451) UART READ: [LEN: ]: 1402[0m
[0;32mI (16451) UART: uart[3] event[0m
[0;32mI (16461) UART: UART_FIFO_OVF[0m
[0;32mI (16461) UART: uart[1] event[0m
[0;32mI (16461) UART: uart rx break[0m
[0;32mI (16461) UART: uart[1] event[0m
[0;32mI (16471) UART: uart rx break[0m
[0;32mI (16471) UART: uart[1] event[0m
[0;32mI (16471) UART: uart rx break[0m
[0;32mI (16471) UART: uart[1] event[0m
[0;32mI (16481) UART: uart rx break[0m
[0;32mI (16481) UART: uart[1] event[0m
[0;32mI (16481) UART: uart rx break[0m
[0;32mI (16491) UART: uart[1] event[0m
[0;32mI (16491) UART: uart rx break[0m
[0;32mI (16491) UART: uart[1] event[0m
[0;32mI (16491) UART: uart rx break[0m
[0;32mI (16501) UART: uart[1] event[0m
[0;32mI (16501) UART: uart rx break[0m
[0;32mI (16501) UART: uart[1] event[0m
[0;32mI (16501) UART: uart rx break[0m
[0;32mI (16511) UART: uart[1] event[0m
[0;32mI (16511) UART: uart rx break[0m
[0;32mI (16511) UART: uart[1] event[0m
[0;32mI (16521) UART: uart rx break[0m
[0;32mI (16521) UART: uart[1] event[0m
[0;32mI (16521) UART: uart rx break[0m
[0;32mI (16521) UART: uart[1] event[0m
[0;32mI (16531) UART: uart rx break[0m
[0;32mI (16531) UART: uart[1] event[0m
[0;32mI (16531) UART: uart rx break[0m
[0;32mI (16531) UART: uart[1] event[0m
[0;32mI (16541) UART: uart rx break[0m
[0;32mI (16541) UART: uart[1] event[0m
[0;32mI (16541) UART: uart rx break[0m
[0;32mI (16541) UART: uart[1] event[0m
[0;32mI (16551) UART: uart rx break[0m
[0;32mI (16551) UART: uart[1] event[0m
[0;32mI (16551) UART: uart rx break[0m
[0;32mI (16561) UART: uart[1] event[0m
[0;32mI (16561) UART: uart rx break[0m
[0;32mI (16561) UART: uart[1] event[0m
[0;32mI (16561) UART: uart rx break[0m
[0;32mI (16571) UART: uart[1] event[0m
[0;32mI (16571) UART: uart rx break[0m
[0;32mI (16571) UART: uart[1] event[0m
[0;32mI (16571) UART: uart rx break[0m
[0;32mI (16581) UART: uart[1] event[0m
[0;32mI (16581) UART: uart rx break[0m
[0;32mI (16581) UART: uart[1] event[0m
[0;32mI (16581) UART: uart rx break[0m
[0;32mI (16591) UART: uart[1] event[0m
[0;32mI (16591) UART: uart rx break[0m
[0;32mI (16591) UART: uart[1] event[0m
[0;32mI (16601) UART: uart rx break[0m
[0;32mI (16601) UART: uart[1] event[0m
[0;32mI (16601) UART: uart rx break[0m
[0;32mI (16601) UART: uart[1] event[0m
[0;32mI (16611) UART: uart rx break[0m
[0;32mI (16611) UART: uart[1] event[0m
[0;32mI (16611) UART: uart rx break[0m
[0;32mI (16611) UART: uart[1] event[0m
[0;32mI (16621) UART: uart rx break[0m
[0;32mI (16621) UART: uart[1] event[0m
[0;32mI (16621) UART: uart rx break[0m
[0;32mI (16631) UART: uart[1] event[0m
[0;32mI (16631) UART: uart rx break[0m
[0;32mI (16631) UART: uart[1] event[0m
[0;32mI (16631) UART: uart rx break[0m
[0;32mI (16641) UART: uart[1] event[0m
[0;32mI (16641) UART: uart rx break[0m
[0;32mI (16641) UART: uart[1] event[0m
[0;32mI (16641) UART: uart rx break[0m
[0;32mI (16651) UART: uart[1] event[0m
[0;32mI (16651) UART: uart rx break[0m
[0;32mI (16651) UART: uart[0] event[0m
[0;32mI (16651) UART_READ: : 2[0m
[0;32mI (16661) UART_READ: : : UART_READ_END[0m
[0;32mI (16661) UART READ: [LEN: ]: 1459[0m
[0;32mI (16661) UART: uart[1] event[0m
[0;32mI (16671) UART: uart rx break[0m
[0;32mI (16671) UART: uart[1] event[0m
[0;32mI (16671) UART: uart rx break[0m
[0;32mI (16671) UART: uart[1] event[0m
[0;32mI (16681) UART: uart rx break[0m
[0;32mI (16681) UART: uart[1] event[0m
[0;32mI (16681) UART: uart rx break[0m
[0;32mI (16691) UART: uart[1] event[0m
[0;32mI (16691) UART: uart rx break[0m
[0;32mI (16691) UART: uart[1] event[0m
[0;32mI (16691) UART: uart rx break[0m
[0;32mI (16701) UART: uart[1] event[0m
[0;32mI (16701) UART: uart rx break[0m
[0;32mI (16701) UART: uart[1] event[0m
[0;32mI (16701) UART: uart rx break[0m
[0;32mI (16711) UART: uart[1] event[0m
[0;32mI (16711) UART: uart rx break[0m
[0;32mI (16711) UART: uart[1] event[0m
[0;32mI (16711) UART: uart rx break[0m
[0;32mI (16721) UART: uart[1] event[0m
[0;32mI (16721) UART: uart rx break[0m
[0;32mI (16721) UART: uart[1] event[0m
[0;32mI (16731) UART: uart rx break[0m
[0;32mI (16731) UART: uart[1] event[0m
[0;32mI (16731) UART: uart rx break[0m
[0;32mI (16731) UART: uart[1] event[0m
[0;32mI (16741) UART: uart rx break[0m
[0;32mI (16741) UART: uart[1] event[0m
[0;32mI (16741) UART: uart rx break[0m
[0;32mI (16741) UART: uart[1] event[0m
[0;32mI (16751) UART: uart rx break[0m
[0;32mI (16751) UART: uart[1] event[0m
[0;32mI (16751) UART: uart rx break[0m
[0;32mI (16761) UART: uart[1] event[0m
[0;32mI (16761) UART: uart rx break[0m
[0;32mI (16761) UART: uart[1] event[0m
[0;32mI (16761) UART: uart rx break[0m
[0;32mI (16771) UART: uart[1] event[0m
[0;32mI (16771) UART: uart rx break[0m
[0;32mI (16771) UART: uart[1] event[0m
[0;32mI (16771) UART: uart rx break[0m
[0;32mI (16781) UART: uart[1] event[0m
[0;32mI (16781) UART: uart rx break[0m
[0;32mI (16781) UART: uart[1] event[0m
[0;32mI (16781) UART: uart rx break[0m
[0;32mI (16791) UART: uart[1] event[0m
[0;32mI (16791) UART: uart rx break[0m
[0;32mI (16791) UART: uart[1] event[0m
[0;32mI (16801) UART: uart rx break[0m
[0;32mI (16801) UART: uart[1] event[0m
[0;32mI (16801) UART: uart rx break[0m
[0;32mI (16801) UART: uart[1] event[0m
[0;32mI (16811) UART: uart rx break[0m
[0;32mI (16811) UART: uart[1] event[0m
[0;32mI (16811) UART: uart rx break[0m
[0;32mI (16811) UART: uart[1] event[0m
[0;32mI (16821) UART: uart rx break[0m
[0;32mI (16821) UART: uart[1] event[0m
[0;32mI (16821) UART: uart rx break[0m
[0;32mI (16831) UART: uart[1] event[0m
[0;32mI (16831) UART: uart rx break[0m
[0;32mI (16831) UART: uart[1] event[0m
[0;32mI (16831) UART: uart rx break[0m
[0;32mI (16841) UART: uart[1] event[0m
[0;32mI (16841) UART: uart rx break[0m
[0;32mI (16841) UART: uart[1] event[0m
[0;32mI (16841) UART: uart rx break[0m
[0;32mI (16851) UART: uart[1] event[0m
[0;32mI (16851) UART: uart rx break[0m
[0;32mI (16851) UART: uart[1] event[0m
[0;32mI (16851) UART: uart rx break[0m
[0;32mI (16861) UART: uart[1] event[0m
[0;32mI (16861) UART: uart rx break[0m
[0;32mI (16861) UART: uart[1] event[0m
[0;32mI (16871) UART: uart rx break[0m
[0;32mI (16871) UART: uart[1] event[0m
[0;32mI (16871) UART: uart rx break[0m
[0;32mI (16871) UART: uart[1] event[0m
[0;32mI (16881) UART: uart rx break[0m
[0;32mI (16881) UART: uart[1] event[0m
[0;32mI (16881) UART: uart rx break[0m
[0;32mI (16881) UART: uart[1] event[0m
[0;32mI (16891) UART: uart rx break[0m
[0;32mI (16891) UART: uart[1] event[0m
[0;32mI (16891) UART: uart rx break[0m
[0;32mI (16901) UART: uart[1] event[0m
[0;32mI (16901) UART: uart rx break[0m
[0;32mI (16901) UART: uart[1] event[0m
[0;32mI (16901) UART: uart rx break[0m
[0;32mI (16911) UART: uart[1] event[0m
[0;32mI (16911) UART: uart rx break[0m
[0;32mI (16911) UART: uart[1] event[0m
[0;32mI (16911) UART: uart rx break[0m
[0;32mI (16921) UART: uart[0] event[0m
[0;32mI (16921) UART_READ: : 2[0m
[0;32mI (16921) UART_READ: : : UART_READ_END[0m
[0;32mI (16931) UART READ: [LEN: ]: 1481[0m
[0;32mI (16931) UART: uart[1] event[0m
[0;32mI (16931) UART: uart rx break[0m
[0;32mI (16931) UART: uart[1] event[0m
[0;32mI (16941) UART: uart rx break[0m
[0;32mI (16941) UART: uart[1] event[0m
[0;32mI (16941) UART: uart rx break[0m
[0;32mI (16941) UART: uart[1] event[0m
[0;32mI (16951) UART: uart rx break[0m
[0;32mI (16951) UART: uart[1] event[0m
[0;32mI (16951) UART: uart rx break[0m
[0;32mI (16961) UART: uart[1] event[0m
[0;32mI (16961) UART: uart rx break[0m
[0;32mI (16961) UART: uart[1] event[0m
[0;32mI (16961) UART: uart rx break[0m
[0;32mI (16971) UART: uart[1] event[0m
[0;32mI (16971) UART: uart rx break[0m
[0;32mI (16971) UART: uart[1] event[0m
[0;32mI (16971) UART: uart rx break[0m
[0;32mI (16981) UART: uart[1] event[0m
[0;32mI (16981) UART: uart rx break[0m
[0;32mI (16981) UART: uart[1] event[0m
[0;32mI (16981) UART: uart rx break[0m
[0;32mI (16991) UART: uart[1] event[0m
[0;32mI (16991) UART: uart rx break[0m
[0;32mI (16991) UART: uart[0] event[0m
[0;32mI (17001) UART_READ: : 2[0m
[0;32mI (17001) UART_READ: : : UART_READ_END[0m
[0;32mI (17001) UART READ: [LEN: ]: 1601[0m
[0;32mI (17001) UART: uart[1] event[0m
[0;32mI (17011) UART: uart rx break[0m
[0;32mI (17011) UART: uart[1] event[0m
[0;32mI (17011) UART: uart rx break[0m
[0;32mI (17021) UART: uart[1] event[0m
[0;32mI (17021) UART: uart rx break[0m
[0;32mI (17021) UART: uart[1] event[0m
[0;32mI (17021) UART: uart rx break[0m
[0;32mI (17031) UART: uart[1] event[0m
[0;32mI (17031) UART: uart rx break[0m
[0;32mI (17921) UART: uart[1] event[0m
[0;32mI (17921) UART: uart rx break[0m
[0;32mI (17921) UART: uart[0] event[0m
[0;32mI (17921) UART_READ: : 2[0m
[0;32mI (17921) UART_READ: : : UART_READ_END[0m
[0;32mI (17921) UART READ: [LEN: ]: 1721[0m
[0;32mI (17921) UART: uart[1] event[0m
[0;32mI (17931) UART: uart rx break[0m
[0;32mI (17931) UART: uart[1] event[0m
[0;32mI (17931) UART: uart rx break[0m
[0;32mI (17941) UART: uart[1] event[0m
[0;32mI (17941) UART: uart rx break[0m
[0;32mI (17941) UART: uart[1] event[0m
[0;32mI (17941) UART: uart rx break[0m
[0;32mI (17951) UART: uart[1] event[0m
[0;32mI (17951) UART: uart rx break[0m
[0;32mI (17951) UART: uart[1] event[0m
[0;32mI (17951) UART: uart rx break[0m
[0;32mI (17961) UART: uart[1] event[0m
[0;32mI (17961) UART: uart rx break[0m
[0;32mI (17961) UART: uart[1] event[0m
[0;32mI (17961) UART: uart rx break[0m
[0;32mI (17971) UART: uart[1] event[0m
[0;32mI (17971) UART: uart rx break[0m
[0;32mI (17971) UART: uart[1] event[0m
[0;32mI (17981) UART: uart rx break[0m
[0;32mI (17981) UART: uart[1] event[0m
[0;32mI (17981) UART: uart rx break[0m
[0;32mI (17981) UART: uart[1] event[0m
[0;32mI (17991) UART: uart rx break[0m
[0;32mI (17991) UART: uart[1] event[0m
[0;32mI (17991) UART: uart rx break[0m
[0;32mI (17991) UART: uart[1] event[0m
[0;32mI (18001) UART: uart rx break[0m
[0;32mI (18001) UART: uart[1] event[0m
[0;32mI (18001) UART: uart rx break[0m
[0;32mI (18011) UART: uart[1] event[0m
[0;32mI (18011) UART: uart rx break[0m
[0;32mI (18011) UART: uart[1] event[0m
[0;32mI (18011) UART: uart rx break[0m
[0;32mI (18021) UART: uart[1] event[0m
[0;32mI (18021) UART: uart rx break[0m
[0;32mI (18021) UART: uart[1] event[0m
[0;32mI (18021) UART: uart rx break[0m
[0;32mI (18031) UART: uart[1] event[0m
[0;32mI (18031) UART: uart rx break[0m
[0;32mI (18031) UART: uart[1] event[0m
[0;32mI (18031) UART: uart rx break[0m
[0;32mI (18041) UART: uart[1] event[0m
[0;32mI (18041) UART: uart rx break[0m
[0;32mI (18041) UART: uart[1] event[0m
[0;32mI (18051) UART: uart rx break[0m
[0;32mI (18051) UART: uart[1] event[0m
[0;32mI (18051) UART: uart rx break[0m
[0;32mI (18051) UART: uart[1] event[0m
[0;32mI (18061) UART: uart rx break[0m
[0;32mI (18061) UART: uart[1] event[0m
[0;32mI (18061) UART: uart rx break[0m
[0;32mI (18061) UART: uart[1] event[0m
[0;32mI (18071) UART: uart rx break[0m
[0;32mI (18071) UART: uart[1] event[0m
[0;32mI (18071) UART: uart rx break[0m
[0;32mI (18081) UART: uart[1] event[0m
[0;32mI (18081) UART: uart rx break[0m
[0;32mI (18081) UART: uart[1] event[0m
[0;32mI (18081) UART: uart rx break[0m
[0;32mI (18091) UART: uart[1] event[0m
[0;32mI (18091) UART: uart rx break[0m
[0;32mI (18091) UART: uart[1] event[0m
[0;32mI (18091) UART: uart rx break[0m
[0;32mI (18101) UART: uart[1] event[0m
[0;32mI (18101) UART: uart rx break[0m
[0;32mI (18101) UART: uart[1] event[0m
[0;32mI (18101) UART: uart rx break[0m
[0;32mI (18111) UART: uart[1] event[0m
[0;32mI (18111) UART: uart rx break[0m
[0;32mI (18111) UART: uart[1] event[0m
[0;32mI (18121) UART: uart rx break[0m
[0;32mI (18121) UART: uart[1] event[0m
[0;32mI (18121) UART: uart rx break[0m
[0;32mI (18121) UART: uart[1] event[0m
[0;32mI (18131) UART: uart rx break[0m
[0;32mI (18131) UART: uart[1] event[0m
[0;32mI (18131) UART: uart rx break[0m
[0;32mI (18131) UART: uart[1] event[0m
[0;32mI (18141) UART: uart rx break[0m
[0;32mI (18141) UART: uart[1] event[0m
[0;32mI (18141) UART: uart rx break[0m
[0;32mI (18151) UART: uart[1] event[0m
[0;32mI (18151) UART: uart rx break[0m
[0;32mI (18151) UART: uart[1] event[0m
[0;32mI (18151) UART: uart rx break[0m
[0;32mI (18161) UART: uart[1] event[0m
[0;32mI (18161) UART: uart rx break[0m
[0;32mI (18161) UART: uart[1] event[0m
[0;32mI (18161) UART: uart rx break[0m
[0;32mI (18171) UART: uart[1] event[0m
[0;32mI (18171) UART: uart rx break[0m
[0;32mI (18171) UART: uart[1] event[0m
[0;32mI (18171) UART: uart rx break[0m
[0;32mI (18181) UART: uart[1] event[0m
[0;32mI (18181) UART: uart rx break[0m
[0;32mI (18181) UART: uart[1] event[0m
[0;32mI (18191) UART: uart rx break[0m
[0;32mI (18191) UART: uart[1] event[0m
[0;32mI (18191) UART: uart rx break[0m
[0;32mI (18191) UART: uart[1] event[0m
[0;32mI (18201) UART: uart rx break[0m
[0;32mI (18201) UART: uart[1] event[0m
[0;32mI (18201) UART: uart rx break[0m
[0;32mI (18201) UART: uart[1] event[0m
[0;32mI (18211) UART: uart rx break[0m
[0;32mI (18211) UART: uart[1] event[0m
[0;32mI (18211) UART: uart rx break[0m
[0;32mI (18221) UART: uart[1] event[0m
[0;32mI (18221) UART: uart rx break[0m
[0;32mI (18221) UART: uart[1] event[0m
[0;32mI (18221) UART: uart rx break[0m
[0;32mI (18231) UART: uart[1] event[0m
[0;32mI (18231) UART: uart rx break[0m
[0;32mI (18231) UART: uart[1] event[0m
[0;32mI (18231) UART: uart rx break[0m
[0;32mI (18241) UART: uart[1] event[0m
[0;32mI (18241) UART: uart rx break[0m
[0;32mI (18241) UART: uart[1] event[0m
[0;32mI (18241) UART: uart rx break[0m
[0;32mI (18251) UART: uart[1] event[0m
[0;32mI (18251) UART: uart rx break[0m
[0;32mI (18251) UART: uart[1] event[0m
[0;32mI (18261) UART: uart rx break[0m
[0;32mI (18261) UART: uart[1] event[0m
[0;32mI (18261) UART: uart rx break[0m
[0;32mI (18261) UART: uart[1] event[0m
[0;32mI (18271) UART: uart rx break[0m
[0;32mI (18271) UART: uart[1] event[0m
[0;32mI (18271) UART: uart rx break[0m
[0;32mI (18271) UART: uart[1] event[0m
[0;32mI (18281) UART: uart rx break[0m
[0;32mI (18281) UART: uart[1] event[0m
[0;32mI (18281) UART: uart rx break[0m
[0;32mI (18291) UART: uart[1] event[0m
[0;32mI (18291) UART: uart rx break[0m
[0;32mI (18291) UART: uart[1] event[0m
[0;32mI (18291) UART: uart rx break[0m
[0;32mI (18301) UART: uart[1] event[0m
[0;32mI (18301) UART: uart rx break[0m
[0;32mI (18301) UART: uart[1] event[0m
[0;32mI (18301) UART: uart rx break[0m
[0;32mI (18311) UART: uart[1] event[0m
[0;32mI (18311) UART: uart rx break[0m
[0;32mI (18311) UART: uart[1] event[0m
[0;32mI (18311) UART: uart rx break[0m
[0;32mI (18321) UART: uart[1] event[0m
[0;32mI (18321) UART: uart rx break[0m
[0;32mI (18321) UART: uart[1] event[0m
[0;32mI (18331) UART: uart rx break[0m
[0;32mI (18331) UART: uart[1] event[0m
[0;32mI (18331) UART: uart rx break[0m
[0;32mI (18331) UART: uart[1] event[0m
[0;32mI (18341) UART: uart rx break[0m
[0;32mI (18341) UART: uart[1] event[0m
[0;32mI (18341) UART: uart rx break[0m
[0;32mI (18341) UART: uart[1] event[0m
[0;32mI (18351) UART: uart rx break[0m
[0;32mI (18351) UART: uart[1] event[0m
[0;32mI (18351) UART: uart rx break[0m
[0;32mI (18361) UART: uart[0] event[0m
[0;32mI (18361) UART_READ: : 2[0m
[0;32mI (18361) UART_READ: : : UART_READ_END[0m
[0;32mI (18361) UART READ: [LEN: ]: 1841[0m
[0;32mI (18371) UART: uart[1] event[0m
[0;32mI (18371) UART: uart rx break[0m
[0;32mI (18371) UART: uart[1] event[0m
[0;32mI (18371) UART: uart rx break[0m
[0;32mI (18381) UART: uart[1] event[0m
[0;32mI (18381) UART: uart rx break[0m
[0;32mI (18381) UART: uart[1] event[0m
[0;32mI (18391) UART: uart rx break[0m
[0;32mI (18391) UART: uart[1] event[0m
[0;32mI (18391) UART: uart rx break[0m
[0;32mI (18391) UART: uart[1] event[0m
[0;32mI (18401) UART: uart rx break[0m
[0;32mI (18401) UART: uart[1] event[0m
[0;32mI (18401) UART: uart rx break[0m
[0;32mI (18401) UART: uart[1] event[0m
[0;32mI (18411) UART: uart rx break[0m
[0;32mI (18411) UART: uart[1] event[0m
[0;32mI (18411) UART: uart rx break[0m
[0;32mI (18421) UART: uart[1] event[0m
[0;32mI (18421) UART: uart rx break[0m
[0;32mI (18421) UART: uart[1] event[0m
[0;32mI (18421) UART: uart rx break[0m
[0;32mI (18431) UART: uart[1] event[0m
[0;32mI (18431) UART: uart rx break[0m
[0;32mI (18431) UART: uart[1] event[0m
[0;32mI (18431) UART: uart rx break[0m
[0;32mI (18441) UART: uart[1] event[0m
[0;32mI (18441) UART: uart rx break[0m
[0;32mI (18441) UART: uart[1] event[0m
[0;32mI (18441) UART: ua