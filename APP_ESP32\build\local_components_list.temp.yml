components:
  - name: "app_trace"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace"
  - name: "app_update"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/app_update"
  - name: "bootloader"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader"
  - name: "bootloader_support"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support"
  - name: "bt"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/bt"
  - name: "cmock"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cmock"
  - name: "console"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/console"
  - name: "cxx"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/cxx"
  - name: "driver"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/driver"
  - name: "efuse"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse"
  - name: "esp-tls"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls"
  - name: "esp_adc"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc"
  - name: "esp_app_format"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format"
  - name: "esp_bootloader_format"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format"
  - name: "esp_coex"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex"
  - name: "esp_common"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common"
  - name: "esp_driver_ana_cmpr"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr"
  - name: "esp_driver_cam"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam"
  - name: "esp_driver_dac"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac"
  - name: "esp_driver_gpio"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio"
  - name: "esp_driver_gptimer"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer"
  - name: "esp_driver_i2c"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c"
  - name: "esp_driver_i2s"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s"
  - name: "esp_driver_isp"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp"
  - name: "esp_driver_jpeg"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg"
  - name: "esp_driver_ledc"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc"
  - name: "esp_driver_mcpwm"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm"
  - name: "esp_driver_parlio"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio"
  - name: "esp_driver_pcnt"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt"
  - name: "esp_driver_ppa"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa"
  - name: "esp_driver_rmt"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt"
  - name: "esp_driver_sdio"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio"
  - name: "esp_driver_sdm"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm"
  - name: "esp_driver_sdmmc"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc"
  - name: "esp_driver_sdspi"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi"
  - name: "esp_driver_spi"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi"
  - name: "esp_driver_touch_sens"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens"
  - name: "esp_driver_tsens"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens"
  - name: "esp_driver_uart"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart"
  - name: "esp_driver_usb_serial_jtag"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag"
  - name: "esp_eth"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth"
  - name: "esp_event"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event"
  - name: "esp_gdbstub"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub"
  - name: "esp_hid"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid"
  - name: "esp_http_client"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client"
  - name: "esp_http_server"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server"
  - name: "esp_https_ota"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota"
  - name: "esp_https_server"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server"
  - name: "esp_hw_support"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support"
  - name: "esp_lcd"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd"
  - name: "esp_local_ctrl"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl"
  - name: "esp_mm"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm"
  - name: "esp_netif"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif"
  - name: "esp_netif_stack"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif_stack"
  - name: "esp_partition"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition"
  - name: "esp_phy"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy"
  - name: "esp_pm"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm"
  - name: "esp_psram"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_psram"
  - name: "esp_ringbuf"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf"
  - name: "esp_rom"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom"
  - name: "esp_security"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security"
  - name: "esp_system"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system"
  - name: "esp_timer"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer"
  - name: "esp_vfs_console"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console"
  - name: "esp_wifi"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi"
  - name: "espcoredump"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump"
  - name: "esptool_py"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py"
  - name: "fatfs"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs"
  - name: "freertos"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos"
  - name: "hal"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/hal"
  - name: "heap"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/heap"
  - name: "http_parser"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/http_parser"
  - name: "idf_test"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/idf_test"
  - name: "ieee802154"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ieee802154"
  - name: "json"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/json"
  - name: "linux"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/linux"
  - name: "log"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/log"
  - name: "lwip"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/lwip"
  - name: "mbedtls"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls"
  - name: "mqtt"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt"
  - name: "newlib"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib"
  - name: "nvs_flash"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash"
  - name: "nvs_sec_provider"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider"
  - name: "openthread"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/openthread"
  - name: "partition_table"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table"
  - name: "perfmon"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon"
  - name: "protobuf-c"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c"
  - name: "protocomm"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm"
  - name: "pthread"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/pthread"
  - name: "riscv"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/riscv"
  - name: "rt"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/rt"
  - name: "sdmmc"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc"
  - name: "soc"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/soc"
  - name: "spi_flash"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash"
  - name: "spiffs"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs"
  - name: "tcp_transport"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport"
  - name: "touch_element"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/touch_element"
  - name: "ulp"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/ulp"
  - name: "unity"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/unity"
  - name: "usb"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/usb"
  - name: "vfs"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/vfs"
  - name: "wear_levelling"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling"
  - name: "wifi_provisioning"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning"
  - name: "wpa_supplicant"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant"
  - name: "xtensa"
    path: "C:/Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa"
  - name: "main"
    path: "D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main"
