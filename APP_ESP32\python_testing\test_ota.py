#!/usr/bin/env python3
"""
Simple OTA Test Script
Tests the firmware update functionality with a small test file
"""

import asyncio
import os
from firmware_updater import FirmwareUpdater

def create_test_firmware(size=2048):
    """Create a small test firmware file"""
    filename = "test_firmware.bin"
    print(f"📝 Creating test firmware: {filename} ({size} bytes)")
    
    # Create test data
    test_data = bytearray()
    
    # Add ESP32-like header
    test_data.extend(b'\xE9')  # ESP32 magic byte
    test_data.extend(b'\x00' * 15)  # Padding
    
    # Fill with test pattern
    for i in range(16, size):
        test_data.append(i % 256)
    
    with open(filename, 'wb') as f:
        f.write(test_data)
    
    print(f"✅ Test firmware created: {filename}")
    return filename

async def test_ota():
    """Test OTA update with small test firmware"""
    print("🧪 Testing OTA Firmware Update")
    print("=" * 40)
    
    # Create test firmware
    test_firmware = create_test_firmware(2048)
    
    try:
        # Test the update
        updater = FirmwareUpdater()
        success = await updater.update_firmware(test_firmware)
        
        if success:
            print("\n🎉 OTA Test PASSED!")
        else:
            print("\n❌ OTA Test FAILED!")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
    
    finally:
        # Clean up
        if os.path.exists(test_firmware):
            os.remove(test_firmware)
            print(f"🗑️  Cleaned up: {test_firmware}")

if __name__ == "__main__":
    try:
        asyncio.run(test_ota())
    except KeyboardInterrupt:
        print("\n❌ Test interrupted")
