/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : timer_handler.h
* @version 		  : 1.0.1
* @brief          : Header for timer_handler.c file
* @details		  : Header for timer_handler.c file
********************************************************************************/

#ifndef TIMER_HANDLER_H
#define TIMER_HANDLER_H

#include "esp_log.h"
#include "esp_timer.h"
#include <stdint.h>

esp_err_t timer_initalization(void);
void timer_callback(void* arg);
void timer_start(uint32_t timer_value);
void timer_stop(void);

#endif /* END OF TIMER_HANDLER_H */