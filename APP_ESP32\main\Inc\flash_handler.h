/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : flash_handler.h
* @version 		  : 1.0.1
* @brief          : Header for flash_handler.c file
* @details		  : Header for flash_handler.c file
********************************************************************************
* @version 1.0.1                                        				Date : 24/07/2025
* Added send_start_file_transfer function
********************************************************************************/
#ifndef FLASH_HANDLER_H
#define FLASH_HANDLER_H

#include "esp_littlefs.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C"
{
#endif

int32_t flash_initalization(void);
uint32_t get_total_flash_size(uint32_t* total, uint32_t* used);
int32_t write_data_to_flash(const uint8_t* data, uint32_t len);
int32_t read_data_from_flash(uint8_t* data, uint32_t len);
int32_t send_data_to_nexus(void);
int32_t format_flash(void);
uint32_t get_file_size(void);
void current_packet_received(uint32_t len);
void send_start_file_transfer(void);
void write_data_to_nexus(void* param);

#ifdef __cplusplus
}
#endif

#endif /* END OF FLASH_HANDLER_H */