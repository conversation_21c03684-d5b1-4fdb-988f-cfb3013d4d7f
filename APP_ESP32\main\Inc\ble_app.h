/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_app.h
* @version 		  : 1.0.1
* @brief          : Header for ble_app.c file
* @details		  : Header for ble_app.c file
********************************************************************************
* @version 1.0.1                                         				Date : 21/07/2025
* Added function declarations for BLE advertising control via UART
* Added ble_advertisement_start(), ble_advertisement_stop(), is_ble_synced()
* Added clear_state_from_nvs() declaration for NVS state management
********************************************************************************/
#ifndef BLE_APP_H
#define BLE_APP_H

#include <stdint.h>

#ifdef __cplusplus
extern "C"
{
#endif

int32_t ble_initialization(void);
/**
 * @brief Saves BLE advertising state to NVS (Non-Volatile Storage)
 * @param state True to enable advertising, false to disable
 */
void set_state_to_nvs(bool state);

/**
 * @brief Loads BLE advertising state from NVS (Non-Volatile Storage)
 * @return True if advertising should be enabled, false otherwise
 */
bool clear_state_from_nvs(void);

/**
 * @brief Gets the stop advertising on disconnect flag state
 * @return True if advertising should stop on disconnect, false otherwise
 */
bool get_stop_adv_on_disconnect_flag(void);

/**
 * @brief Sets the stop advertising on disconnect flag state
 * @param state True to stop advertising on disconnect, false otherwise
 */
void set_stop_adv_on_disconnect_flag(bool state);

/**
 * @brief Gets the current BLE connection handle
 * @return Connection handle, or 0 if not connected
 */
uint16_t get_connection_handle(void);

/**
 * @brief Updates BLE device name and starts advertising with the new name
 * @param new_name Pointer to null-terminated string (max 31 characters)
 * @return 0 on success, negative error code on failure
 */
int update_ble_name_and_start_advertising(const char* new_name);

/**
 * @brief Gets the current BLE device name
 * @return Pointer to current BLE device name string, or NULL on error
 */
const char* get_current_ble_name(void);

/**
 * @brief Starts BLE advertising
 * @return None
 */
void ble_advertisement_start(void);

/**
 * @brief Stops BLE advertising
 * @return None
 */
void ble_advertisement_stop(void);

/**
 * @brief Checks if BLE stack is synchronized
 * @return True if BLE is synced and ready, false otherwise
 */
bool is_ble_synced(void);


#ifdef __cplusplus
}
#endif

#endif /* END OF BLE_APP_H */