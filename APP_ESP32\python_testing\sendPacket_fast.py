import asyncio
from bleak import Bleak<PERSON>lient
import sys
import time

BLE_DEVICE_ADDRESS = "a0:85:e3:f1:8c:c6"  # Replace with your ESP32's MAC
CHAR_UUID = "A2BD0011-AD84-44BE-94BB-B289C6D34F32"
CHUNK_SIZE = 512  # Maximum chunk size
PIPELINE_SIZE = 10  # Number of chunks to send without waiting for ACK
PROGRESS_INTERVAL = 200  # Show progress every N chunks

ack_count = 0
total_chunks_sent = 0

def handle_notification(sender, data):
    global ack_count
    if data == b'\xAA':
        ack_count += 1

async def send_file_fast(file_path):
    global ack_count, total_chunks_sent
    
    # Get file size for progress tracking
    file_size = 0
    with open(file_path, "rb") as f:
        f.seek(0, 2)  # Seek to end
        file_size = f.tell()
        f.seek(0)  # Seek back to beginning

    print(f"Starting FAST transfer of {file_path} ({file_size} bytes)")
    print(f"Chunk size: {CHUNK_SIZE} bytes, Pipeline: {PIPELINE_SIZE} chunks")
    start_time = time.time()
    total_sent = 0
    chunk_count = 0

    async with BleakClient(BLE_DEVICE_ADDRESS) as client:
        await client.start_notify(CHAR_UUID, handle_notification)

        with open(file_path, "rb") as f:
            chunks_in_flight = 0
            
            while chunk := f.read(CHUNK_SIZE):
                chunk_count += 1
                total_chunks_sent += 1
                
                # Send chunk without waiting
                await client.write_gatt_char(CHAR_UUID, chunk)
                total_sent += len(chunk)
                chunks_in_flight += 1
                
                # Show progress less frequently
                if chunk_count % PROGRESS_INTERVAL == 0 or len(chunk) < CHUNK_SIZE:
                    progress = (total_sent / file_size) * 100
                    print(f"Chunk {chunk_count}: Progress: {total_sent}/{file_size} ({progress:.1f}%) - ACKs: {ack_count}")
                
                # Pipeline control: wait if we have too many chunks in flight
                if chunks_in_flight >= PIPELINE_SIZE:
                    # Small delay to let ACKs catch up
                    await asyncio.sleep(0.001)  # 1ms delay
                    chunks_in_flight = max(0, chunks_in_flight - (ack_count - (chunk_count - chunks_in_flight)))

        # Wait for remaining ACKs
        print("Waiting for final ACKs...")
        for i in range(50):  # Wait up to 5 seconds
            await asyncio.sleep(0.1)
            if ack_count >= chunk_count:
                break

        end_time = time.time()
        transfer_time = end_time - start_time
        throughput = file_size / transfer_time if transfer_time > 0 else 0

        print(f"File sent successfully!")
        print(f"Total chunks sent: {chunk_count}")
        print(f"ACKs received: {ack_count}")
        print(f"Transfer completed in {transfer_time:.2f} seconds")
        print(f"Average throughput: {throughput:.0f} bytes/second")
        print(f"Speed improvement: {throughput/4000:.1f}x faster than before")

        await client.stop_notify(CHAR_UUID)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python sendPacket_fast.py <file_path>")
        sys.exit(1)

    asyncio.run(send_file_fast(sys.argv[1]))
