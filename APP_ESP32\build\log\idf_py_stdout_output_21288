ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x8 (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
Octal Flash Mode Enabled
For OPI Flash, Use Default Flash Boot Mode
mode:SLOW_RD, clock div:1
load:0x3fce2810,len:0x15a0
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0;32mI (33) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (33) boot: compile time Jul 24 2025 20:39:56[0m
[0;32mI (33) boot: Multicore bootloader[0m
[0;32mI (33) boot: chip revision: v0.2[0m
[0;32mI (36) boot: efuse block revision: v1.3[0m
[0;32mI (40) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (44) boot.esp32s3: SPI Mode       : SLOW READ[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 32MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (56) boot: Partition Table:[0m
[0;32mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (65) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (72) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (78) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (85) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18648h ( 99912) map[0m
[0;32mI (125) esp_image: segment 1: paddr=00028670 vaddr=3fc9b900 size=04a84h ( 19076) load[0m
[0;32mI (131) esp_image: segment 2: paddr=0002d0fc vaddr=40374000 size=02f1ch ( 12060) load[0m
[0;32mI (134) esp_image: segment 3: paddr=00030020 vaddr=42000020 size=61aa8h (400040) map[0m
[0;32mI (231) esp_image: segment 4: paddr=00091ad0 vaddr=40376f1c size=149c0h ( 84416) load[0m
[0;32mI (254) esp_image: segment 5: paddr=000a6498 vaddr=600fe100 size=0001ch (    28) load[0m
[0;32mI (263) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (264) boot: Disabling RNG early entropy source...[0m
[0;32mI (274) octal_psram: vendor id    : 0x0d (AP)[0m
[0;32mI (274) octal_psram: dev id       : 0x03 (generation 4)[0m
[0;32mI (274) octal_psram: density      : 0x05 (128 Mbit)[0m
[0;32mI (276) octal_psram: good-die     : 0x01 (Pass)[0m
[0;32mI (281) octal_psram: Latency      : 0x01 (Fixed)[0m
[0;32mI (285) octal_psram: VCC          : 0x00 (1.8V)[0m
[0;32mI (289) octal_psram: SRF          : 0x01 (Fast Refresh)[0m
[0;32mI (294) octal_psram: BurstType    : 0x01 (Hybrid Wrap)[0m
[0;32mI (299) octal_psram: BurstLen     : 0x01 (32 Byte)[0m
[0;32mI (304) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)[0m
[0;32mI (309) octal_psram: DriveStrength: 0x00 (1/1)[0m
[0;32mI (314) MSPI Timing: PSRAM timing tuning index: 6[0m
[0;32mI (317) esp_psram: Found 16MB PSRAM device[0m
[0;32mI (321) esp_psram: Speed: 80MHz[0m
[0;32mI (324) cpu_start: Multicore app[0m
[0;32mI (1179) esp_psram: SPI SRAM memory test OK[0m
[0;32mI (1196) cpu_start: Pro cpu start user code[0m
[0;32mI (1196) cpu_start: cpu freq: 240000000 Hz[0m
[0;32mI (1196) app_init: Application information:[0m
[0;32mI (1196) app_init: Project name:     Vantage_nxESP32_Int_Rel_1_2_1_0[0m
[0;32mI (1202) app_init: App version:      Vantage_Bin_nxESP32_1_2_0_0[0m
[0;32mI (1208) app_init: Compile time:     Jul 24 2025 20:38:22[0m
[0;32mI (1213) app_init: ELF file SHA256:  7fe8b3592...[0m
[0;32mI (1217) app_init: ESP-IDF:          v5.4.1[0m
[0;32mI (1221) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (1225) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (1229) efuse_init: Chip rev:         v0.2[0m
[0;32mI (1233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (1240) heap_init: At 3FCAA4E8 len 0003F228 (252 KiB): RAM[0m
[0;32mI (1245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (1250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (1255) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0;32mI (1261) esp_psram: Adding pool of 16121K of PSRAM memory to heap allocator[0m
[0;33mW (1268) spi_flash: Octal flash chip is using but dio mode is selected, will automatically switch to Octal mode[0m
[0;32mI (1277) spi_flash: detected chip: mxic (opi)[0m
[0;32mI (1281) spi_flash: flash io: opi_str[0m
[0;32mI (1285) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (1291) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (1298) coexist: coex firmware version: e727207[0m
[0;32mI (1311) coexist: coexist rom version e7ae62f[0m
[0;32mI (1311) main_task: Started on CPU0[0m
[0;32mI (1321) esp_psram: Reserving pool of 32K of internal memory for DMA/internal allocations[0m
[0;32mI (1321) main_task: Calling app_main()[0m
[0;32mI (1341) BLE_INIT: BT controller compile version [d74042a][0m
[0;32mI (1341) BLE_INIT: Bluetooth MAC: b4:3a:45:f7:39:0a[0m
[0;32mI (1341) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (1371) uart: queue free spaces: 80[0m
[0;32mI (1381) FLASH: : 0[0m
[0;32mI (1381) FLASH: : LittleFS mounted successfully at /storage[0m
[0;32mI (1381) HEAP: Free heap: 16688140[0m
[0;32mI (1381) FILE_HANDLE: : TASK CREATED[0m
[0;32mI (1381) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (1391) NimBLE: Device Address: [0m
[0;32mI (1391) NimBLE: b4:3a:45:f7:39:0a[0m
[0;32mI (1391) NimBLE: [0m

[0;32mI (1391) gpio: GPIO[10]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1401) gpio: GPIO[11]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1411) gpio: GPIO[13]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1421) gpio: GPIO[14]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1431) NimBLE_BLE_PRPH: BLE advertising state loaded from NVS: false[0m
[0;32mI (1431) main_task: Returned from app_main()[0m
[0;32mI (17781) UART: uart[0] event[0m
[0;32mI (17781) UART_READ: : 0[0m
[0;32mI (17781) UART_READ: : UART_READ_START[0m
[0;32mI (17781) UART: Received BLE_START_ADV with name: 'MR CAR 1'[0m
[0;32mI (17781) NimBLE_BLE_PRPH: BLE device name rec: 'MR CAR 1'[0m
[0;32mI (17791) NimBLE_BLE_PRPH: BLE device name updated to: 'MR CAR 1'[0m
[0;32mI (17791) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (17801) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (17801) NimBLE: disc_mode=2[0m
[0;32mI (17801) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (17811) NimBLE: [0m

[0;32mI (17821) NimBLE_BLE_PRPH: BLE advertising started with new name: 'MR CAR 1'[0m
[0;32mI (17831) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: true[0m
[0;32mI (17831) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (17831) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (17841) UART: BLE advertising started successfully[0m
[0;32mI (17841) UART READ: [LEN: ]: 18[0m
BLE CONNECTION ESTABLISHED
[0;32mI (44601) NimBLE: connection established; status=0 [0m
[0;32mI (44601) UART: Sent UART command: 10 bytes[0m
[0;32mI (44601) NimBLE: [0m

[0;32mI (44601) NimBLE: GAP procedure initiated: [0m
[0;32mI (44611) NimBLE: connection parameter update; conn_handle=1 itvl_min=6 itvl_max=8 latency=0 supervision_timeout=400 min_ce_len=0 max_ce_len=0[0m
[0;32mI (44621) NimBLE: [0m

[0;32mI (44661) NimBLE: connection updated; status=547 [0m
[0;32mI (44661) NimBLE: [0m

[0;32mI (44681) UART: uart[0] event[0m
[0;32mI (44681) UART_READ: : 0[0m
[0;32mI (44681) UART_READ: : UART_READ_START[0m
[0;32mI (44681) UART READ: [LEN: ]: 28[0m
[0;32mI (44871) NimBLE: connection updated; status=0 [0m
[0;32mI (44881) NimBLE: [0m

[0;32mI (45131) NimBLE: mtu update event; conn_handle=1 cid=4 mtu=517[0m

[0;32mI (45201) NimBLE: connection updated; status=0 [0m
[0;32mI (45201) NimBLE: [0m

[0;32mI (45321) BLE: Data Length: 10[0m
[0;32mI (45321) BLE_REQUEST_HANDLER: : Received: BLE_VERSION_REQUEST[0m
[0;32mI (45321) NimBLE: GATT procedure initiated: notify; [0m
[0;32mI (45321) NimBLE: att_handle=24[0m

[0;32mI (45321) NimBLE: notify_tx event; conn_handle=1 attr_handle=24 status=0 is_indication=0[0m
[0;32mI (45591) BLE: Data Length: 1[0m
[0;32mI (45591) BLE: Read Response[0m
[0;32mI (53901) NimBLE: subscribe event; conn_handle=1 attr_handle=21 reason=1 prevn=0 curn=1 previ=0 curi=0[0m

[0;32mI (53961) BLE: Data Length: 10[0m
[0;32mI (53961) BLE_REQUEST_HANDLER: : Received: BLE_READ_CONFIG_REQUEST[0m
[0;32mI (53961) write_data: Received: write_data[0m
[0;32mI (53961) NimBLE: GATT procedure initiated: notify; [0m
[0;32mI (53971) NimBLE: att_handle=24[0m

[0;32mI (53961) UART: uart[0] event[0m
[0;32mI (53971) NimBLE: notify_tx event; conn_handle=1 attr_handle=24 status=0 is_indication=0[0m
[0;32mI (53971) UART_READ: : 0[0m
[0;32mI (53981) UART_READ: : UART_READ_START[0m
[0;32mI (53991) UART READ: [LEN: ]: 10[0m
[0;32mI (74451) NimBLE: subscribe event; conn_handle=1 attr_handle=21 reason=2 prevn=1 curn=0 previ=0 curi=0[0m

[0;32mI (74451) NimBLE: disconnect; reason=531 [0m
[0;32mI (74451) NimBLE: [0m

[0;32mI (74461) UART: Sent UART command: 10 bytes[0m
[0;32mI (74461) UART: uart[0] event[0m
[0;32mI (74461) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (74461) UART_READ: : 0[0m
[0;32mI (74471) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (74481) NimBLE: disc_mode=2[0m
[0;32mI (74471) UART_READ: : UART_READ_START[0m
[0;32mI (74481) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (74481) UART READ: [LEN: ]: 20[0m
[0;32mI (74491) NimBLE: [0m

[0;32mI (102501) UART: uart[0] event[0m
[0;32mI (102501) UART_READ: : 0[0m
[0;32mI (102501) UART_READ: : UART_READ_START[0m
[0;32mI (102501) UART: Received BLE_STOP_ADV command[0m
[0;32mI (102501) NimBLE_BLE_PRPH: Stopping BLE advertising...[0m
[0;32mI (102511) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (102511) UART: BLE advertising was active - stopped successfully[0m
[0;32mI (102531) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: false[0m
[0;32mI (102531) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (102531) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (102531) UART: BLE_STOP_ADV command processed successfully[0m
[0;32mI (102541) UART READ: [LEN: ]: 30[0m
[0;32mI (152291) UART: uart[0] event[0m
[0;32mI (152291) UART_READ: : 0[0m
[0;32mI (152291) UART_READ: : UART_READ_START[0m
[0;32mI (152291) UART: Received BLE_STOP_ADV command[0m
[0;32mI (152291) UART: BLE advertising was already stopped - no action needed[0m
[0;32mI (152301) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: false[0m
[0;32mI (152301) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (152311) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (152311) UART: BLE_STOP_ADV command processed successfully[0m
[0;32mI (152311) UART READ: [LEN: ]: 40[0m
[0;32mI (180991) UART: uart[0] event[0m
[0;32mI (180991) UART_READ: : 0[0m
[0;32mI (180991) UART_READ: : UART_READ_START[0m
[0;32mI (180991) UART: Received BLE_START_ADV with name: 'MR CAR 1'[0m
[0;32mI (180991) NimBLE_BLE_PRPH: BLE device name rec: 'MR CAR 1'[0m
[0;32mI (181001) NimBLE_BLE_PRPH: BLE device name updated to: 'MR CAR 1'[0m
[0;32mI (181001) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (181011) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (181011) NimBLE: disc_mode=2[0m
[0;32mI (181011) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (181021) NimBLE: [0m

[0;32mI (181031) NimBLE_BLE_PRPH: BLE advertising started with new name: 'MR CAR 1'[0m
[0;32mI (181041) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: true[0m
[0;32mI (181041) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (181041) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (181051) UART: BLE advertising started successfully[0m
[0;32mI (181051) UART READ: [LEN: ]: 58[0m
BLE CONNECTION ESTABLISHED
[0;32mI (204691) NimBLE: connection established; status=0 [0m
[0;32mI (204691) UART: Sent UART command: 10 bytes[0m
[0;32mI (204691) NimBLE: [0m

[0;32mI (204691) NimBLE: GAP procedure initiated: [0m
[0;32mI (204701) NimBLE: connection parameter update; conn_handle=1 itvl_min=6 itvl_max=8 latency=0 supervision_timeout=400 min_ce_len=0 max_ce_len=0[0m
[0;32mI (204711) NimBLE: [0m

[0;32mI (204751) NimBLE: connection updated; status=547 [0m
[0;32mI (204751) NimBLE: [0m

[0;32mI (204791) UART: uart[0] event[0m
[0;32mI (204791) UART_READ: : 0[0m
[0;32mI (204791) UART_READ: : UART_READ_START[0m
[0;32mI (204791) UART READ: [LEN: ]: 68[0m
[0;32mI (204961) NimBLE: connection updated; status=0 [0m
[0;32mI (204961) NimBLE: [0m

[0;32mI (205281) NimBLE: connection updated; status=0 [0m
[0;32mI (205281) NimBLE: [0m

[0;32mI (210381) UART: uart[0] event[0m
[0;32mI (210381) UART_READ: : 0[0m
[0;32mI (210381) UART_READ: : UART_READ_START[0m
[0;32mI (210381) UART: Received BLE_STOP_ADV command[0m
[0;32mI (210381) UART: BLE advertising was already stopped - no action needed[0m
[0;32mI (210391) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: false[0m
[0;32mI (210391) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (210401) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (210401) UART: BLE_STOP_ADV command processed successfully[0m
[0;32mI (210401) UART READ: [LEN: ]: 78[0m
[0;32mI (228231) NimBLE: disconnect; reason=531 [0m
[0;32mI (228231) NimBLE: [0m

[0;32mI (228231) UART: Sent UART command: 10 bytes[0m
[0;32mI (228231) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (228231) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (228241) NimBLE: disc_mode=2[0m
[0;32mI (228241) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (228251) NimBLE: [0m

[0;32mI (228481) UART: uart[0] event[0m
[0;32mI (228481) UART_READ: : 0[0m
[0;32mI (228481) UART_READ: : UART_READ_START[0m
[0;32mI (228481) UART: Received BLE_STOP_ADV command[0m
[0;32mI (228481) NimBLE_BLE_PRPH: Stopping BLE advertising...[0m
[0;32mI (228481) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (228491) UART: BLE advertising was active - stopped successfully[0m
[0;32mI (228501) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: false[0m
[0;32mI (228501) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (228511) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (228511) UART: BLE_STOP_ADV command processed successfully[0m
[0;32mI (228521) UART READ: [LEN: ]: 88[0m
[0;32mI (254281) UART: uart[0] event[0m
[0;32mI (254281) UART_READ: : 0[0m
[0;32mI (254281) UART_READ: : UART_READ_START[0m
[0;32mI (254281) UART: Received BLE_START_ADV with name: 'MR CAR 1'[0m
[0;32mI (254281) NimBLE_BLE_PRPH: BLE device name rec: 'MR CAR 1'[0m
[0;32mI (254291) NimBLE_BLE_PRPH: BLE device name updated to: 'MR CAR 1'[0m
[0;32mI (254291) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (254301) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (254301) NimBLE: disc_mode=2[0m
[0;32mI (254311) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (254311) NimBLE: [0m

[0;32mI (254321) NimBLE_BLE_PRPH: BLE advertising started with new name: 'MR CAR 1'[0m
[0;32mI (254331) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: true[0m
[0;32mI (254331) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (254331) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (254341) UART: BLE advertising started successfully[0m
[0;32mI (254341) UART READ: [LEN: ]: 106[0m
BLE CONNECTION ESTABLISHED
[0;32mI (261871) NimBLE: connection established; status=0 [0m
[0;32mI (261871) UART: Sent UART command: 10 bytes[0m
[0;32mI (261871) NimBLE: [0m

[0;32mI (261871) NimBLE: GAP procedure initiated: [0m
[0;32mI (261881) NimBLE: connection parameter update; conn_handle=1 itvl_min=6 itvl_max=8 latency=0 supervision_timeout=400 min_ce_len=0 max_ce_len=0[0m
[0;32mI (261891) NimBLE: [0m

[0;32mI (261881) UART: uart[0] event[0m
[0;32mI (261891) UART_READ: : 0[0m
[0;32mI (261901) UART_READ: : UART_READ_START[0m
[0;32mI (261901) UART READ: [LEN: ]: 116[0m
[0;32mI (261931) NimBLE: connection updated; status=547 [0m
[0;32mI (261931) NimBLE: [0m

[0;32mI (262141) NimBLE: connection updated; status=0 [0m
[0;32mI (262141) NimBLE: [0m

[0;32mI (262461) NimBLE: connection updated; status=0 [0m
[0;32mI (262461) NimBLE: [0m

[0;32mI (276741) BLE: Data Length: 10[0m
[0;32mI (276741) BLE_REQUEST_HANDLER: : Received: BLE_READ_CONFIG_REQUEST[0m
[0;32mI (276741) write_data: Received: write_data[0m
[0;32mI (276741) NimBLE: GATT procedure initiated: notify; [0m
[0;32mI (276751) NimBLE: att_handle=24[0m

[0;32mI (276741) UART: uart[0] event[0m
[0;32mI (276751) NimBLE: notify_tx event; conn_handle=1 attr_handle=24 status=0 is_indication=0[0m
[0;32mI (276751) UART_READ: : 0[0m
[0;32mI (276761) UART_READ: : UART_READ_START[0m
[0;32mI (276761) UART READ: [LEN: ]: 10[0m
[0;32mI (295371) NimBLE: disconnect; reason=531 [0m
[0;32mI (295371) NimBLE: [0m

[0;32mI (295371) UART: Sent UART command: 10 bytes[0m
[0;32mI (295371) UART: uart[0] event[0m
[0;32mI (295371) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (295371) UART_READ: : 0[0m
[0;32mI (295381) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (295381) UART_READ: : UART_READ_START[0m
[0;32mI (295381) NimBLE: disc_mode=2[0m
[0;32mI (295391) UART READ: [LEN: ]: 20[0m
[0;32mI (295391) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (295401) NimBLE: [0m

[0;32mI (440491) UART: uart[1] event[0m
[0;32mI (440491) UART: uart rx break[0m
[0;32mI (440491) UART: uart[1] event[0m
[0;32mI (440491) UART: uart rx break[0m
[0;32mI (440491) UART: uart[1] event[0m
[0;32mI (440501) UART: uart rx break[0m
[0;32mI (440501) UART: uart[1] event[0m
[0;32mI (440501) UART: uart rx break[0m
[0;32mI (440501) UART: uart[1] event[0m
[0;32mI (440511) UART: uart rx break[0m
[0;32mI (440511) UART: uart[1] event[0m
[0;32mI (440511) UART: uart rx break[0m
[0;32mI (440521) UART: uart[1] event[0m
[0;32mI (440521) UART: uart rx break[0m
[0;32mI (440521) UART: uart[1] event[0m
[0;32mI (440521) UART: uart rx break[0m
[0;32mI (440531) UART: uart[1] event[0m
[0;32mI (440531) UART: uart rx break[0m
[0;32mI (440531) UART: uart[1] event[0m
[0;32mI (440541) UART: uart rx break[0m
[0;32mI (440541) UART: uart[1] event[0m
[0;32mI (440541) UART: uart rx break[0m
[0;32mI (440541) UART: uart[1] event[0m
[0;32mI (440551) UART: uart rx break[0m
[0;32mI (440551) UART: uart[1] event[0m
[0;32mI (440551) UART: uart rx break[0m
[0;32mI (440551) UART: uart[1] event[0m
[0;32mI (440561) UART: uart rx break[0m
[0;32mI (440561) UART: uart[1] event[0m
[0;32mI (440561) UART: uart rx break[0m
[0;32mI (440571) UART: uart[1] event[0m
[0;32mI (440571) UART: uart rx break[0m
[0;32mI (440571) UART: uart[1] event[0m
[0;32mI (440571) UART: uart rx break[0m
[0;32mI (440581) UART: uart[1] event[0m
[0;32mI (440581) UART: uart rx break[0m
[0;32mI (440581) UART: uart[1] event[0m
[0;32mI (440591) UART: uart rx break[0m
[0;32mI (440591) UART: uart[1] event[0m
[0;32mI (440591) UART: uart rx break[0m
[0;32mI (440591) UART: uart[1] event[0m
[0;32mI (440601) UART: uart rx break[0m
[0;32mI (440601) UART: uart[1] event[0m
[0;32mI (440601) UART: uart rx break[0m
[0;32mI (440601) UART: uart[1] event[0m
[0;32mI (440611) UART: uart rx break[0m
[0;32mI (440611) UART: uart[1] event[0m
[0;32mI (440611) UART: uart rx break[0m
[0;32mI (440621) UART: uart[1] event[0m
[0;32mI (440621) UART: uart rx break[0m
[0;32mI (440621) UART: uart[1] event[0m
[0;32mI (440621) UART: uart rx break[0m
[0;32mI (440631) UART: uart[1] event[0m
[0;32mI (440631) UART: uart rx break[0m
[0;32mI (440631) UART: uart[1] event[0m
[0;32mI (440641) UART: uart rx break[0m
[0;32mI (440641) UART: uart[1] event[0m
[0;32mI (440641) UART: uart rx break[0m
[0;32mI (440641) UART: uart[1] event[0m
[0;32mI (440651) UART: uart rx break[0m
[0;32mI (440651) UART: uart[1] event[0m
[0;32mI (440651) UART: uart rx break[0m
[0;32mI (440651) UART: uart[1] event[0m
[0;32mI (440661) UART: uart rx break[0m
[0;32mI (440661) UART: uart[1] event[0m
[0;32mI (440661) UART: uart rx break[0m
[0;32mI (440671) UART: uart[1] event[0m
[0;32mI (440671) UART: uart rx break[0m
[0;32mI (440671) UART: uart[1] event[0m
[0;32mI (440671) UART: uart rx break[0m
[0;32mI (440681) UART: uart[1] event[0m
[0;32mI (440681) UART: uart rx break[0m
[0;32mI (440681) UART: uart[1] event[0m
[0;32mI (440691) UART: uart rx break[0m
[0;32mI (440691) UART: uart[1] event[0m
[0;32mI (440691) UART: uart rx break[0m
[0;32mI (440691) UART: uart[1] event[0m
[0;32mI (440701) UART: uart rx break[0m
[0;32mI (440701) UART: uart[1] event[0m
[0;32mI (440701) UART: uart rx break[0m
[0;32mI (440701) UART: uart[1] event[0m
[0;32mI (440711) UART: uart rx break[0m
[0;32mI (440711) UART: uart[1] event[0m
[0;32mI (440711) UART: uart rx break[0m
[0;32mI (440721) UART: uart[1] event[0m
[0;32mI (440721) UART: uart rx break[0m
[0;32mI (440721) UART: uart[1] event[0m
[0;32mI (440721) UART: uart rx break[0m
[0;32mI (440731) UART: uart[0] event[0m
[0;32mI (440731) UART_READ: : 0[0m
[0;32mI (440731) UART_READ: : UART_READ_START[0m
[0;32mI (440741) UART_READ: : ERROR[0m
[0;32mI (440741) UART READ: [LEN: ]: 126[0m
[0;32mI (440741) UART: uart[0] event[0m
[0;32mI (440741) UART_READ: : 2[0m
[0;32mI (440751) UART_READ: : : UART_READ_END[0m
[0;32mI (440751) UART READ: [LEN: ]: 127[0m
[0;32mI (440751) UART: uart[1] event[0m
[0;32mI (440761) UART: uart rx break[0m
[0;32mI (440761) UART: uart[1] event[0m
[0;32mI (440761) UART: uart rx break[0m
[0;32mI (440761) UART: uart[1] event[0m
[0;32mI (440771) UART: uart rx break[0m
[0;32mI (440771) UART: uart[1] event[0m
[0;32mI (440771) UART: uart rx break[0m
[0;32mI (440781) UART: uart[1] event[0m
[0;32mI (440781) UART: uart rx break[0m
[0;32mI (440781) UART: uart[1] event[0m
[0;32mI (440781) UART: uart rx break[0m
[0;32mI (440791) UART: uart[1] event[0m
[0;32mI (440791) UART: uart rx break[0m
[0;32mI (440791) UART: uart[1] event[0m
[0;32mI (440801) UART: uart rx break[0m
[0;32mI (440801) UART: uart[1] event[0m
[0;32mI (440801) UART: uart rx break[0m
[0;32mI (440801) UART: uart[1] event[0m
[0;32mI (440811) UART: uart rx break[0m
[0;32mI (440811) UART: uart[1] event[0m
[0;32mI (440811) UART: uart rx break[0m
[0;32mI (440811) UART: uart[1] event[0m
[0;32mI (440821) UART: uart rx break[0m
[0;32mI (440821) UART: uart[1] event[0m
[0;32mI (440821) UART: uart rx break[0m
[0;32mI (440831) UART: uart[1] event[0m
[0;32mI (440831) UART: uart rx break[0m
[0;32mI (440831) UART: uart[1] event[0m
[0;32mI (440831) UART: uart rx break[0m
[0;32mI (440841) UART: uart[1] event[0m
[0;32mI (440841) UART: uart rx break[0m
[0;32mI (440841) UART: uart[1] event[0m
[0;32mI (440851) UART: uart rx break[0m
[0;32mI (440851) UART: uart[1] event[0m
[0;32mI (440851) UART: uart rx break[0m
[0;32mI (440851) UART: uart[1] event[0m
[0;32mI (440861) UART: uart rx break[0m
[0;32mI (440861) UART: uart[1] event[0m
[0;32mI (440861) UART: uart rx break[0m
[0;32mI (440861) UART: uart[1] event[0m
[0;32mI (440871) UART: uart rx break[0m
[0;32mI (440871) UART: uart[1] event[0m
[0;32mI (440871) UART: uart rx break[0m
[0;32mI (440881) UART: uart[1] event[0m
[0;32mI (440881) UART: uart rx break[0m
[0;32mI (440881) UART: uart[1] event[0m
[0;32mI (440881) UART: uart rx break[0m
[0;32mI (440891) UART: uart[1] event[0m
[0;32mI (440891) UART: uart rx break[0m
[0;32mI (440891) UART: uart[1] event[0m
[0;32mI (440901) UART: uart rx break[0m
[0;32mI (440901) UART: uart[1] event[0m
[0;32mI (440901) UART: uart rx break[0m
[0;32mI (440901) UART: uart[0] event[0m
[0;32mI (440911) UART_READ: : 2[0m
[0;32mI (440911) UART_READ: : : UART_READ_END[0m
[0;32mI (440911) UART READ: [LEN: ]: 199[0m
[0;32mI (440921) UART: uart[0] event[0m
[0;32mI (440921) UART_READ: : 2[0m
[0;32mI (440921) UART_READ: : : UART_READ_END[0m
[0;32mI (440921) UART READ: [LEN: ]: 200[0m
[0;32mI (440931) UART: uart[1] event[0m
[0;32mI (440931) UART: uart rx break[0m
[0;32mI (440931) UART: uart[1] event[0m
[0;32mI (440941) UART: uart rx break[0m
[0;32mI (440941) UART: uart[1] event[0m
[0;32mI (440941) UART: uart rx break[0m
[0;32mI (440941) UART: uart[1] event[0m
[0;32mI (440951) UART: uart rx break[0m
[0;32mI (440951) UART: uart[1] event[0m
[0;32mI (440951) UART: uart rx break[0m
[0;32mI (440951) UART: uart[1] event[0m
[0;32mI (440961) UART: uart rx break[0m
[0;32mI (440961) UART: uart[1] event[0m
[0;32mI (440961) UART: uart rx break[0m
[0;32mI (440971) UART: uart[1] event[0m
[0;32mI (440971) UART: uart rx break[0m
[0;32mI (440971) UART: uart[1] event[0m
[0;32mI (440971) UART: uart rx break[0m
[0;32mI (440981) UART: uart[1] event[0m
[0;32mI (440981) UART: uart rx break[0m
[0;32mI (440981) UART: uart[1] event[0m
[0;32mI (440991) UART: uart rx break[0m
[0;32mI (440991) UART: uart[1] event[0m
[0;32mI (440991) UART: uart rx break[0m
[0;32mI (440991) UART: uart[1] event[0m
[0;32mI (441001) UART: uart rx break[0m
[0;32mI (441001) UART: uart[1] event[0m
[0;32mI (441001) UART: uart rx break[0m
[0;32mI (441001) UART: uart[1] event[0m
[0;32mI (441011) UART: uart rx break[0m
[0;32mI (441011) UART: uart[1] event[0m
[0;32mI (441011) UART: uart rx break[0m
[0;32mI (441021) UART: uart[1] event[0m
[0;32mI (441021) UART: uart rx break[0m
[0;32mI (441021) UART: uart[1] event[0m
[0;32mI (441021) UART: uart rx break[0m
[0;32mI (441031) UART: uart[1] event[0m
[0;32mI (441031) UART: uart rx break[0m
[0;32mI (441031) UART: uart[1] event[0m
[0;32mI (441041) UART: uart rx break[0m
[0;32mI (441041) UART: uart[1] event[0m
[0;32mI (441041) UART: uart rx break[0m
[0;32mI (441041) UART: uart[1] event[0m
[0;32mI (441051) UART: uart rx break[0m
[0;32mI (441051) UART: uart[1] event[0m
[0;32mI (441051) UART: uart rx break[0m
[0;32mI (441051) UART: uart[1] event[0m
[0;32mI (441061) UART: uart rx break[0m
[0;32mI (441061) UART: uart[1] event[0m
[0;32mI (441061) UART: uart rx break[0m
[0;32mI (441071) UART: uart[1] event[0m
[0;32mI (441071) UART: uart rx break[0m
[0;32mI (441071) UART: uart[1] event[0m
[0;32mI (441071) UART: uart rx break[0m
[0;32mI (441081) UART: uart[1] event[0m
[0;32mI (441081) UART: uart rx break[0m
[0;32mI (441081) UART: uart[1] event[0m
[0;32mI (441091) UART: uart rx break[0m
[0;32mI (441091) UART: uart[1] event[0m
[0;32mI (441091) UART: uart rx break[0m
[0;32mI (441091) UART: uart[1] event[0m
[0;32mI (441101) UART: uart rx break[0m
[0;32mI (441101) UART: uart[1] event[0m
[0;32mI (441101) UART: uart rx break[0m
[0;32mI (441101) UART: uart[1] event[0m
[0;32mI (441111) UART: uart rx break[0m
[0;32mI (441111) UART: uart[1] event[0m
[0;32mI (441111) UART: uart rx break[0m
[0;32mI (441121) UART: uart[1] event[0m
[0;32mI (441121) UART: uart rx break[0m
[0;32mI (441121) UART: uart[1] event[0m
[0;32mI (441121) UART: uart rx break[0m
[0;32mI (441131) UART: uart[1] event[0m
[0;32mI (441131) UART: uart rx break[0m
[0;32mI (441131) UART: uart[1] event[0m
[0;32mI (441141) UART: uart rx break[0m
[0;32mI (441141) UART: uart[1] event[0m
[0;32mI (441141) UART: uart rx break[0m
[0;32mI (441141) UART: uart[1] event[0m
[0;32mI (441151) UART: uart rx break[0m
[0;32mI (441151) UART: uart[1] event[0m
[0;32mI (441151) UART: uart rx break[0m
[0;32mI (441151) UART: uart[1] event[0m
[0;32mI (441161) UART: uart rx break[0m
[0;32mI (441161) UART: uart[1] event[0m
[0;32mI (441161) UART: uart rx break[0m
[0;32mI (441171) UART: uart[1] event[0m
[0;32mI (441171) UART: uart rx break[0m
[0;32mI (441171) UART: uart[1] event[0m
[0;32mI (441171) UART: uart rx break[0m
[0;32mI (441181) UART: uart[1] event[0m
[0;32mI (441181) UART: uart rx break[0m
[0;32mI (441181) UART: uart[0] event[0m
[0;32mI (441191) UART_READ: : 2[0m
[0;32mI (441191) UART_READ: : : UART_READ_END[0m
[0;32mI (441191) UART READ: [LEN: ]: 219[0m
[0;32mI (441191) UART: uart[0] event[0m
[0;32mI (441201) UART_READ: : 2[0m
[0;32mI (441201) UART_READ: : : UART_READ_END[0m
[0;32mI (441201) UART READ: [LEN: ]: 220[0m
[0;32mI (441211) UART: uart[1] event[0m
[0;32mI (441211) UART: uart rx break[0m
[0;32mI (441211) UART: uart[1] event[0m
[0;32mI (441211) UART: uart rx break[0m
[0;32mI (441221) UART: uart[1] event[0m
[0;32mI (441221) UART: uart rx break[0m
[0;32mI (441221) UART: uart[1] event[0m
[0;32mI (441231) UART: uart rx break[0m
[0;32mI (441231) UART: uart[1] event[0m
[0;32mI (441231) UART: uart rx break[0m
[0;32mI (441231) UART: uart[1] event[0m
[0;32mI (441241) UART: uart rx break[0m
[0;32mI (441241) UART: uart[1] event[0m
[0;32mI (441241) UART: uart rx break[0m
[0;32mI (441251) UART: uart[1] event[0m
[0;32mI (441251) UART: uart rx break[0m
[0;32mI (441251) UART: uart[1] event[0m
[0;32mI (441251) UART: uart rx break[0m
[0;32mI (441261) UART: uart[1] event[0m
[0;32mI (441261) UART: uart rx break[0m
[0;32mI (441261) UART: uart[1] event[0m
[0;32mI (441261) UART: uart rx break[0m
[0;32mI (441271) UART: uart[1] event[0m
[0;32mI (441271) UART: uart rx break[0m
[0;32mI (441271) UART: uart[1] event[0m
[0;32mI (441281) UART: uart rx break[0m
[0;32mI (441281) UART: uart[1] event[0m
[0;32mI (441281) UART: uart rx break[0m
[0;32mI (441281) UART: uart[1] event[0m
[0;32mI (441291) UART: uart rx break[0m
[0;32mI (441291) UART: uart[0] event[0m
[0;32mI (441291) UART_READ: : 2[0m
[0;32mI (441291) UART_READ: : : UART_READ_END[0m
[0;32mI (441301) UART READ: [LEN: ]: 251[0m
[0;32mI (441301) UART: uart[1] event[0m
[0;32mI (441301) UART: uart rx break[0m
[0;32mI (441311) UART: uart[1] event[0m
[0;32mI (441311) UART: uart rx break[0m
[0;32mI (441311) UART: uart[1] event[0m
[0;32mI (441321) UART: uart rx break[0m
[0;32mI (441321) UART: uart[1] event[0m
[0;32mI (441321) UART: uart rx break[0m
[0;32mI (441321) UART: uart[1] event[0m
[0;32mI (441331) UART: uart rx break[0m
[0;32mI (441331) UART: uart[1] event[0m
[0;32mI (441331) UART: uart rx break[0m
[0;32mI (441331) UART: uart[1] event[0m
[0;32mI (441341) UART: uart rx break[0m
[0;32mI (441341) UART: uart[1] event[0m
[0;32mI (441341) UART: uart rx break[0m
[0;32mI (441351) UART: uart[1] event[0m
[0;32mI (441351) UART: uart rx break[0m
[0;32mI (441351) UART: uart[1] event[0m
[0;32mI (441351) UART: uart rx break[0m
[0;32mI (441361) UART: uart[1] event[0m
[0;32mI (441361) UART: uart rx break[0m
[0;32mI (441361) UART: uart[1] event[0m
[0;32mI (441371) UART: uart rx break[0m
[0;32mI (441371) UART: uart[1] event[0m
[0;32mI (441371) UART: uart rx break[0m
[0;32mI (441371) UART: uart[1] event[0m
[0;32mI (441381) UART: uart rx break[0m
[0;32mI (441381) UART: uart[1] event[0m
[0;32mI (441381) UART: uart rx break[0m
[0;32mI (441381) UART: uart[1] event[0m
[0;32mI (441391) UART: uart rx break[0m
[0;32mI (441391) UART: uart[1] event[0m
[0;32mI (441391) UART: uart rx break[0m
[0;32mI (441401) UART: uart[1] event[0m
[0;32mI (441401) UART: uart rx break[0m
[0;32mI (441401) UART: uart[1] event[0m
[0;32mI (441401) UART: uart rx break[0m
[0;32mI (441411) UART: uart[1] event[0m
[0;32mI (441411) UART: uart rx break[0m
[0;32mI (441411) UART: uart[1] event[0m
[0;32mI (441421) UART: uart rx break[0m
[0;32mI (441421) UART: uart[1] event[0m
[0;32mI (441421) UART: uart rx break[0m
[0;32mI (441421) UART: uart[1] event[0m
[0;32mI (441431) UART: uart rx break[0m
[0;32mI (441431) UART: uart[1] event[0m
[0;32mI (441431) UART: uart rx break[0m
[0;32mI (441431) UART: uart[1] event[0m
[0;32mI (441441) UART: uart rx break[0m
[0;32mI (441441) UART: uart[1] event[0m
[0;32mI (441441) UART: uart rx break[0m
[0;32mI (441451) UART: uart[1] event[0m
[0;32mI (441451) UART: uart rx break[0m
[0;32mI (441451) UART: uart[1] event[0m
[0;32mI (441451) UART: uart rx break[0m
[0;32mI (441461) UART: uart[1] event[0m
[0;32mI (441461) UART: uart rx break[0m
[0;32mI (441461) UART: uart[1] event[0m
[0;32mI (441471) UART: uart rx break[0m
[0;32mI (441471) UART: uart[1] event[0m
[0;32mI (441471) UART: uart rx break[0m
[0;32mI (441471) UART: uart[1] event[0m
[0;32mI (441481) UART: uart rx break[0m
[0;32mI (441481) UART: uart[1] event[0m
[0;32mI (441481) UART: uart rx break[0m
[0;32mI (441481) UART: uart[1] event[0m
[0;32mI (441491) UART: uart rx break[0m
[0;32mI (441491) UART: uart[1] event[0m
[0;32mI (441491) UART: uart rx break[0m
[0;32mI (441501) UART: uart[1] event[0m
[0;32mI (441501) UART: uart rx break[0m
[0;32mI (441501) UART: uart[1] event[0m
[0;32mI (441501) UART: uart rx break[0m
[0;32mI (441511) UART: uart[1] event[0m
[0;32mI (441511) UART: uart rx break[0m
[0;32mI (441511) UART: uart[1] event[0m
[0;32mI (441521) UART: uart rx break[0m
[0;32mI (441521) UART: uart[1] event[0m
[0;32mI (441521) UART: uart rx break[0m
[0;32mI (441521) UART: uart[1] event[0m
[0;32mI (441531) UART: uart rx break[0m
[0;32mI (441531) UART: uart[1] event[0m
[0;32mI (441531) UART: uart rx break[0m
[0;32mI (441531) UART: uart[1] event[0m
[0;32mI (441541) UART: uart rx break[0m
[0;32mI (441541) UART: uart[1] event[0m
[0;32mI (441541) UART: uart rx break[0m
[0;32mI (441551) UART: uart[1] event[0m
[0;32mI (441551) UART: uart rx break[0m
[0;32mI (441551) UART: uart[1] event[0m
[0;32mI (441551) UART: uart rx break[0m
[0;32mI (441561) UART: uart[1] event[0m
[0;32mI (441561) UART: uart rx break[0m
[0;32mI (441561) UART: uart[1] event[0m
[0;32mI (441571) UART: uart rx break[0m
[0;32mI (441571) UART: uart[1] event[0m
[0;32mI (441571) UART: uart rx break[0m
[0;32mI (441571) UART: uart[1] event[0m
[0;32mI (441581) UART: uart rx break[0m
[0;32mI (441581) UART: uart[1] event[0m
[0;32mI (441581) UART: uart rx break[0m
[0;32mI (441581) UART: uart[1] event[0m
[0;32mI (441591) UART: uart rx break[0m
[0;32mI (441591) UART: uart[1] event[0m
[0;32mI (441591) UART: uart rx break[0m
[0;32mI (441601) UART: uart[1] event[0m
[0;32mI (441601) UART: uart rx break[0m
[0;32mI (441601) UART: uart[1] event[0m
[0;32mI (441601) UART: uart rx break[0m
[0;32mI (441611) UART: uart[1] event[0m
[0;32mI (441611) UART: uart rx break[0m
[0;32mI (441611) UART: uart[1] event[0m
[0;32mI (441621) UART: uart rx break[0m
[0;32mI (441621) UART: uart[1] event[0m
[0;32mI (441621) UART: uart rx break[0m
[0;32mI (441621) UART: uart[1] event[0m
[0;32mI (441631) UART: uart rx break[0m
[0;32mI (441631) UART: uart[1] event[0m
[0;32mI (441631) UART: uart rx break[0m
[0;32mI (441631) UART: uart[1] event[0m
[0;32mI (441641) UART: uart rx break[0m
[0;32mI (441641) UART: uart[1] event[0m
[0;32mI (441641) UART: uart rx break[0m
[0;32mI (441651) UART: uart[1] event[0m
[0;32mI (441651) UART: uart rx break[0m
[0;32mI (441651) UART: uart[1] event[0m
[0;32mI (441651) UART: uart rx break[0m
[0;32mI (441661) UART: uart[1] event[0m
[0;32mI (441661) UART: uart rx break[0m
[0;32mI (441661) UART: uart[1] event[0m
[0;32mI (441671) UART: uart rx break[0m
[0;32mI (441671) UART: uart[1] event[0m
[0;32mI (441671) UART: uart rx break[0m
[0;32mI (441671) UART: uart[1] event[0m
[0;32mI (441681) UART: uart rx break[0m
[0;32mI (441681) UART: uart[1] event[0m
[0;32mI (441681) UART: uart rx break[0m
[0;32mI (441681) UART: uart[1] event[0m
[0;32mI (441691) UART: uart rx break[0m
[0;32mI (441691) UART: uart[1] event[0m
[0;32mI (441691) UART: uart rx break[0m
[0;32mI (441701) UART: uart[1] event[0m
I (441701) UART: uart rx break
[0;32mI (441701) UART: uart[1] event[0m
[0;32mI (441701) UART: uart rx break[0m
[0;32mI (441711) UART: uart[1] event[0m
[0;32mI (441711) UART: uart rx break[0m
[0;32mI (441711) UART: uart[1] event[0m
[0;32mI (441721) UART: uart rx break[0m
[0;32mI (441721) UART: uart[1] event[0m
[0;32mI (441721) UART: uart rx break[0m
[0;32mI (441721) UART: uart[1] event[0m
[0;32mI (441731) UART: uart rx break[0m
[0;32mI (441731) UART: uart[1] event[0m
[0;32mI (441731) UART: uart rx break[0m
[0;32mI (441731) UART: uart[1] event[0m
[0;32mI (441741) UART: uart rx break[0m
[0;32mI (441741) UART: uart[1] event[0m
[0;32mI (441741) UART: uart rx break[0m
[0;32mI (441751) UART: uart[1] event[0m
[0;32mI (441751) UART: uart rx break[0m
[0;32mI (441751) UART: uart[1] event[0m
[0;32mI (441751) UART: uart rx break[0m
[0;32mI (441761) UART: uart[1] event[0m
[0;32mI (441761) UART: uart rx break[0m
[0;32mI (441761) UART: uart[1] event[0m
[0;32mI (441771) UART: uart rx break[0m
[0;32mI (441771) UART: uart[1] event[0m
[0;32mI (441771) UART: uart rx break[0m
[0;32mI (444621) UART: uart[1] event[0m
[0;32mI (444621) UART: uart rx break[0m
[0;32mI (444641) UART: uart[1] event[0m
[0;32mI (444641) UART: uart rx break[0m
[0;32mI (444641) UART: uart[1] event[0m
[0;32mI (444641) UART: uart rx break[0m
[0;32mI (444661) UART: uart[1] event[0m
[0;32mI (444661) UART: uart rx break[0m
[0;32mI (444661) UART: uart[1] event[0m
[0;32mI (444661) UART: uart rx break[0m
[0;32mI (444881) UART: uart[1] event[0m
[0;32mI (444881) UART: uart rx break[0m
[0;32mI (444901) UART: uart[1] event[0m
[0;32mI (444901) UART: uart rx break[0m
[0;32mI (444901) UART: uart[1] event[0m
[0;32mI (444901) UART: uart rx break[0m
[0;32mI (444901) UART: uart[1] event[0m
[0;32mI (444911) UART: uart rx break[0m
[0;32mI (444911) UART: uart[1] event[0m
[0;32mI (444911) UART: uart rx break[0m
[0;32mI (444911) UART: uart[1] event[0m
[0;32mI (444921) UART: uart rx break[0m
[0;32mI (444921) UART: uart[1] event[0m
[0;32mI (444921) UART: uart rx break[0m
[0;32mI (444921) UART: uart[1] event[0m
[0;32mI (444931) UART: uart rx break[0m
[0;32mI (444931) UART: uart[1] event[0m
[0;32mI (444931) UART: uart rx break[0m
[0;32mI (444941) UART: uart[1] event[0m
[0;32mI (444941) UART: uart rx break[0m
[0;32mI (444941) UART: uart[1] event[0m
[0;32mI (444941) UART: uart rx break[0m
[0;32mI (444951) UART: uart[1] event[0m
[0;32mI (444951) UART: uart rx break[0m
[0;32mI (444951) UART: uart[1] event[0m
[0;32mI (444961) UART: uart rx break[0m
[0;32mI (444961) UART: uart[1] event[0m
[0;32mI (444961) UART: uart rx break[0m
[0;32mI (444961) UART: uart[1] event[0m
[0;32mI (444971) UART: uart rx break[0m
[0;32mI (444971) UART: uart[1] event[0m
[0;32mI (444971) UART: uart rx break[0m
[0;32mI (444971) UART: uart[1] event[0m
[0;32mI (444981) UART: uart rx break[0m
[0;32mI (444981) UART: uart[1] event[0m
[0;32mI (444981) UART: uart rx break[0m
[0;32mI (444991) UART: uart[1] event[0m
[0;32mI (444991) UART: uart rx break[0m
[0;32mI (444991) UART: uart[1] event[0m
[0;32mI (444991) UART: uart rx break[0m
[0;32mI (445001) UART: uart[0] event[0m
[0;32mI (445001) UART_READ: : 2[0m
[0;32mI (445001) UART_READ: : : UART_READ_END[0m
[0;32mI (445011) UART READ: [LEN: ]: 371[0m
[0;32mI (445011) UART: uart[1] event[0m
[0;32mI (445011) UART: uart rx break[0m
[0;32mI (445011) UART: uart[1] event[0m
[0;32mI (445021) UART: uart rx break[0m
[0;32mI (445021) UART: uart[1] event[0m
[0;32mI (445021) UART: uart rx break[0m
[0;32mI (445031) UART: uart[1] event[0m
[0;32mI (445031) UART: uart rx break[0m
[0;32mI (445031) UART: uart[1] event[0m
[0;32mI (445031) UART: uart rx break[0m
[0;32mI (445041) UART: uart[1] event[0m
[0;32mI (445041) UART: uart rx break[0m
[0;32mI (445041) UART: uart[1] event[0m
[0;32mI (445051) UART: uart rx break[0m
[0;32mI (445051) UART: uart[1] event[0m
[0;32mI (445051) UART: uart rx break[0m
[0;32mI (445051) UART: uart[1] event[0m
[0;32mI (445061) UART: uart rx break[0m
[0;32mI (445061) UART: uart[1] event[0m
[0;32mI (445061) UART: uart rx break[0m
[0;32mI (445061) UART: uart[1] event[0m
[0;32mI (445071) UART: uart rx break[0m
[0;32mI (445071) UART: uart[1] event[0m
[0;32mI (445071) UART: uart rx break[0m
[0;32mI (445081) UART: uart[1] event[0m
[0;32mI (445081) UART: uart rx break[0m
[0;32mI (445081) UART: uart[1] event[0m
[0;32mI (445081) UART: uart rx break[0m
[0;32mI (445091) UART: uart[1] event[0m
[0;32mI (445091) UART: uart rx break[0m
[0;32mI (445091) UART: uart[1] event[0m
[0;32mI (445101) UART: uart rx break[0m
[0;32mI (445101) UART: uart[1] event[0m
[0;32mI (445101) UART: uart rx break[0m
[0;32mI (445101) UART: uart[1] event[0m
[0;32mI (445111) UART: uart rx break[0m
[0;32mI (445111) UART: uart[1] event[0m
[0;32mI (445111) UART: uart rx break[0m
[0;32mI (445111) UART: uart[1] event[0m
[0;32mI (445121) UART: uart rx break[0m
[0;32mI (445121) UART: uart[1] event[0m
[0;32mI (445121) UART: uart rx break[0m
[0;32mI (445131) UART: uart[1] event[0m
[0;32mI (445131) UART: uart rx break[0m
[0;32mI (445131) UART: uart[1] event[0m
[0;32mI (445131) UART: uart rx break[0m
[0;32mI (445141) UART: uart[1] event[0m
[0;32mI (445141) UART: uart rx break[0m
[0;32mI (445141) UART: uart[1] event[0m
[0;32mI (445151) UART: uart rx break[0m
[0;32mI (445151) UART: uart[1] event[0m
[0;32mI (445151) UART: uart rx break[0m
[0;32mI (445151) UART: uart[1] event[0m
[0;32mI (445161) UART: uart rx break[0m
[0;32mI (445161) UART: uart[1] event[0m
[0;32mI (445161) UART: uart rx break[0m
[0;32mI (445161) UART: uart[1] event[0m
[0;32mI (445171) UART: uart rx break[0m
[0;32mI (445171) UART: uart[1] event[0m
[0;32mI (445171) UART: uart rx break[0m
[0;32mI (445181) UART: uart[1] event[0m
[0;32mI (445181) UART: uart rx break[0m
[0;32mI (445181) UART: uart[1] event[0m
[0;32mI (445181) UART: uart rx break[0m
[0;32mI (445191) UART: uart[1] event[0m
[0;32mI (445191) UART: uart rx break[0m
[0;32mI (445191) UART: uart[1] event[0m
[0;32mI (445201) UART: uart rx break[0m
[0;32mI (445201) UART: uart[1] event[0m
[0;32mI (445201) UART: uart rx break[0m
[0;32mI (445201) UART: uart[1] event[0m
[0;32mI (445211) UART: uart rx break[0m
[0;32mI (445211) UART: uart[1] event[0m
[0;32mI (445211) UART: uart rx break[0m
[0;32mI (445211) UART: uart[1] event[0m
[0;32mI (445221) UART: uart rx break[0m
[0;32mI (445221) UART: uart[1] event[0m
[0;32mI (445221) UART: uart rx break[0m
[0;32mI (445231) UART: uart[1] event[0m
[0;32mI (445231) UART: uart rx break[0m
[0;32mI (445231) UART: uart[1] event[0m
[0;32mI (445231) UART: uart rx break[0m
[0;32mI (445241) UART: uart[1] event[0m
[0;32mI (445241) UART: uart rx break[0m
[0;32mI (445241) UART: uart[1] event[0m
[0;32mI (445251) UART: uart rx break[0m
[0;32mI (445251) UART: uart[1] event[0m
[0;32mI (445251) UART: uart rx break[0m
[0;32mI (445251) UART: uart[1] event[0m
[0;32mI (445261) UART: uart rx break[0m
[0;32mI (445261) UART: uart[1] event[0m
[0;32mI (445261) UART: uart rx break[0m
[0;32mI (445261) UART: uart[1] event[0m
[0;32mI (445271) UART: uart rx break[0m
[0;32mI (445271) UART: uart[1] event[0m
[0;32mI (445271) UART: uart rx break[0m
[0;32mI (445281) UART: uart[1] event[0m
[0;32mI (445281) UART: uart rx break[0m
[0;32mI (445281) UART: uart[1] event[0m
[0;32mI (445281) UART: uart rx break[0m
[0;32mI (445291) UART: uart[1] event[0m
[0;32mI (445291) UART: uart rx break[0m
[0;32mI (445291) UART: uart[1] event[0m
[0;32mI (445301) UART: uart rx break[0m
[0;32mI (445301) UART: uart[1] event[0m
[0;32mI (445301) UART: uart rx break[0m
[0;32mI (445301) UART: uart[1] event[0m
[0;32mI (445311) UART: uart rx break[0m
[0;32mI (445311) UART: uart[1] event[0m
[0;32mI (445311) UART: uart rx break[0m
[0;32mI (445311) UART: uart[1] event[0m
[0;32mI (445321) UART: uart rx break[0m
[0;32mI (445321) UART: uart[1] event[0m
[0;32mI (445321) UART: uart rx break[0m
[0;32mI (445331) UART: uart[1] event[0m
[0;32mI (445331) UART: uart rx break[0m
[0;32mI (445331) UART: uart[1] event[0m
[0;32mI (445331) UART: uart rx break[0m
[0;32mI (445341) UART: uart[1] event[0m
[0;32mI (445341) UART: uart rx break[0m
[0;32mI (445341) UART: uart[1] event[0m
[0;32mI (445351) UART: uart rx break[0m
[0;32mI (445351) UART: uart[1] event[0m
[0;32mI (445351) UART: uart rx break[0m
[0;32mI (445351) UART: uart[1] event[0m
[0;32mI (445361) UART: uart rx break[0m
[0;32mI (445361) UART: uart[1] event[0m
[0;32mI (445361) UART: uart rx break[0m
[0;32mI (445361) UART: uart[1] event[0m
[0;32mI (445371) UART: uart rx break[0m
[0;32mI (445371) UART: uart[1] event[0m
[0;32mI (445371) UART: uart rx break[0m
[0;32mI (445381) UART: uart[1] event[0m
[0;32mI (445381) UART: uart rx break[0m
[0;32mI (445381) UART: uart[1] event[0m
[0;32mI (445381) UART: uart rx break[0m
[0;32mI (445391) UART: uart[1] event[0m
[0;32mI (445391) UART: uart rx break[0m
[0;32mI (445391) UART: uart[0] event[0m
[0;32mI (445401) UART_READ: : 2[0m
[0;32mI (445401) UART_READ: : : UART_READ_END[0m
[0;32mI (445401) UART READ: [LEN: ]: 491[0m
[0;32mI (445401) UART: uart[1] event[0m
[0;32mI (445411) UART: uart rx break[0m
[0;32mI (445411) UART: uart[1] event[0m
[0;32mI (445411) UART: uart rx break[0m
[0;32mI (445421) UART: uart[1] event[0m
[0;32mI (445421) UART: uart rx break[0m
[0;32mI (445421) UART: uart[1] event[0m
[0;32mI (445421) UART: uart rx break[0m
[0;32mI (445431) UART: uart[1] event[0m
[0;32mI (445431) UART: uart rx break[0m
[0;32mI (445431) UART: uart[1] event[0m
[0;32mI (445431) UART: uart rx break[0m
[0;32mI (445441) UART: uart[1] event[0m
[0;32mI (445441) UART: uart rx break[0m
[0;32mI (445441) UART: uart[1] event[0m
[0;32mI (445451) UART: uart rx break[0m
[0;32mI (445451) UART: uart[1] event[0m
[0;32mI (445451) UART: uart rx break[0m
[0;32mI (445451) UART: uart[1] event[0m
[0;32mI (445461) UART: uart rx break[0m
[0;32mI (445461) UART: uart[1] event[0m
[0;32mI (445461) UART: uart rx break[0m
[0;32mI (445471) UART: uart[1] event[0m
[0;32mI (445471) UART: uart rx break[0m
[0;32mI (445471) UART: uart[1] event[0m
[0;32mI (445471) UART: uart rx break[0m
[0;32mI (445481) UART: uart[1] event[0m
[0;32mI (445481) UART: uart rx break[0m
[0;32mI (445481) UART: uart[1] event[0m
[0;32mI (445481) UART: uart rx break[0m
[0;32mI (445491) UART: uart[1] event[0m
[0;32mI (445491) UART: uart rx break[0m
[0;32mI (445491) UART: uart[1] event[0m
[0;32mI (445501) UART: uart rx break[0m
[0;32mI (445501) UART: uart[1] event[0m
[0;32mI (445501) UART: uart rx break[0m
[0;32mI (445501) UART: uart[1] event[0m
[0;32mI (445511) UART: uart rx break[0m
[0;32mI (445511) UART: uart[1] event[0m
[0;32mI (445511) UART: uart rx break[0m
[0;32mI (445521) UART: uart[1] event[0m
[0;32mI (445521) UART: uart rx break[0m
[0;32mI (445521) UART: uart[1] event[0m
[0;32mI (445521) UART: uart rx break[0m
[0;32mI (445531) UART: uart[1] event[0m
[0;32mI (445531) UART: uart rx break[0m
[0;32mI (445531) UART: uart[1] event[0m
[0;32mI (445531) UART: uart rx break[0m
[0;32mI (445541) UART: uart[1] event[0m
[0;32mI (445541) UART: uart rx break[0m
[0;32mI (445541) UART: uart[1] event[0m
[0;32mI (445551) UART: uart rx break[0m
[0;32mI (445551) UART: uart[1] event[0m
[0;32mI (445551) UART: uart rx break[0m
[0;32mI (445551) UART: uart[1] event[0m
[0;32mI (445561) UART: uart rx break[0m
[0;32mI (445561) UART: uart[1] event[0m
[0;32mI (445561) UART: uart rx break[0m
[0;32mI (445571) UART: uart[1] event[0m
[0;32mI (445571) UART: uart rx break[0m
[0;32mI (445571) UART: uart[1] event[0m
[0;32mI (445571) UART: uart rx break[0m
[0;32mI (445581) UART: uart[1] event[0m
[0;32mI (445581) UART: uart rx break[0m
[0;32mI (445581) UART: uart[1] event[0m
[0;32mI (445581) UART: uart rx break[0m
[0;32mI (445591) UART: uart[1] event[0m
[0;32mI (445591) UART: uart rx break[0m
[0;32mI (445591) UART: uart[1] event[0m
[0;32mI (445601) UART: uart rx break[0m
[0;32mI (445601) UART: uart[1] event[0m
[0;32mI (445601) UART: uart rx break[0m
[0;32mI (445601) UART: uart[1] event[0m
[0;32mI (445611) UART: uart rx break[0m
[0;32mI (445611) UART: uart[1] event[0m
[0;32mI (445611) UART: uart rx break[0m
[0;32mI (445621) UART: uart[1] event[0m
[0;32mI (445621) UART: uart rx break[0m
[0;32mI (445621) UART: uart[1] event[0m
[0;32mI (445621) UART: uart rx break[0m
[0;32mI (445631) UART: uart[1] event[0m
[0;32mI (445631) UART: uart rx break[0m
[0;32mI (445631) UART: uart[1] event[0m
[0;32mI (445631) UART: uart rx break[0m
[0;32mI (445641) UART: uart[1] event[0m
[0;32mI (445641) UART: uart rx break[0m
[0;32mI (445641) UART: uart[1] event[0m
[0;32mI (445651) UART: uart rx break[0m
[0;32mI (445651) UART: uart[1] event[0m
[0;32mI (445651) UART: uart rx break[0m
[0;32mI (445651) UART: uart[1] event[0m
[0;32mI (445661) UART: uart rx break[0m
[0;32mI (445661) UART: uart[1] event[0m
[0;32mI (445661) UART: uart rx break[0m
[0;32mI (445671) UART: uart[1] event[0m
[0;32mI (445671) UART: uart rx break[0m
[0;32mI (445671) UART: uart[1] event[0m
[0;32mI (445671) UART: uart rx break[0m
[0;32mI (445681) UART: uart[1] event[0m
[0;32mI (445681) UART: uart rx break[0m
[0;32mI (445681) UART: uart[1] event[0m
[0;32mI (445681) UART: uart rx break[0m
[0;32mI (445691) UART: uart[1] event[0m
[0;32mI (445691) UART: uart rx break[0m
[0;32mI (445691) UART: uart[1] event[0m
[0;32mI (445701) UART: uart rx break[0m
[0;32mI (445701) UART: uart[1] event[0m
[0;32mI (445701) UART: uart rx break[0m
[0;32mI (445701) UART: uart[1] event[0m
[0;32mI (445711) UART: uart rx break[0m
[0;32mI (445711) UART: uart[1] event[0m
[0;32mI (445711) UART: uart rx break[0m
[0;32mI (445721) UART: uart[1] event[0m
[0;32mI (445721) UART: uart rx break[0m
[0;32mI (445721) UART: uart[1] event[0m
[0;32mI (445721) UART: uart rx break[0m
[0;32mI (445731) UART: uart[1] event[0m
[0;32mI (445731) UART: uart rx break[0m
[0;32mI (445731) UART: uart[1] event[0m
[0;32mI (445731) UART: uart rx break[0m
[0;32mI (445741) UART: uart[1] event[0m
[0;32mI (445741) UART: uart rx break[0m
[0;32mI (445741) UART: uart[1] event[0m
[0;32mI (445751) UART: uart rx break[0m
[0;32mI (445751) UART: uart[1] event[0m
[0;32mI (445751) UART: uart rx break[0m
[0;32mI (445751) UART: uart[1] event[0m
[0;32mI (445761) UART: uart rx break[0m
[0;32mI (445761) UART: uart[1] event[0m
[0;32mI (445761) UART: uart rx break[0m
[0;32mI (445771) UART: uart[1] event[0m
[0;32mI (445771) UART: uart rx break[0m
[0;32mI (445771) UART: uart[1] event[0m
[0;32mI (445771) UART: uart rx break[0m
[0;32mI (445781) UART: uart[1] event[0m
[0;32mI (445781) UART: uart rx break[0m
[0;32mI (445781) UART: uart[1] event[0m
[0;32mI (445781) UART: uart rx break[0m
[0;32mI (445791) UART: uart[1] event[0m
[0;32mI (445791) UART: uart rx break[0m
[0;32mI (445791) UART: uart[1] event[0m
[0;32mI (445801) UART: uart rx break[0m
[0;32mI (445801) UART: uart[1] event[0m
[0;32mI (445801) UART: uart rx break[0m
[0;32mI (445801) UART: uart[1] event[0m
[0;32mI (445811) UART: uart rx break[0m
[0;32mI (445811) UART: uart[1] event[0m
[0;32mI (445811) UART: uart rx break[0m
[0;32mI (445821) UART: uart[1] event[0m
[0;32mI (445821) UART: uart rx break[0m
[0;32mI (445821) UART: uart[1] event[0m
[0;32mI (445821) UART: uart rx break[0m
[0;32mI (445831) UART: uart[1] event[0m
[0;32mI (445831) UART: uart rx break[0m
[0;32mI (445831) UART: uart[1] event[0m
[0;32mI (445831) UART: uart rx break[0m
[0;32mI (445841) UART: uart[1] event[0m
[0;32mI (445841) UART: uart rx break[0m
[0;32mI (445841) UART: uart[1] event[0m
[0;32mI (445851) UART: uart rx break[0m
[0;32mI (445851) UART: uart[1] event[0m
[0;32mI (445851) UART: uart rx break[0m
[0;32mI (445851) UART: uart[1] event[0m
[0;32mI (445861) UART: uart rx break[0m
[0;32mI (445861) UART: uart[1] event[0m
[0;32mI (445861) UART: uart rx break[0m
[0;32mI (445871) UART: uart[1] event[0m
[0;32mI (445871) UART: uart rx break[0m
[0;32mI (445871) UART: uart[1] event[0m
[0;32mI (445871) UART: uart rx break[0m
[0;32mI (445881) UART: uart[1] event[0m
[0;32mI (445881) UART: uart rx break[0m
[0;32mI (445881) UART: uart[0] event[0m
[0;32mI (445881) UART_READ: : 2[0m
[0;32mI (445891) UART_READ: : : UART_READ_END[0m
[0;32mI (445891) UART READ: [LEN: ]: 611[0m
[0;32mI (445891) UART: uart[1] event[0m
[0;32mI (445901) UART: uart rx break[0m
[0;32mI (445901) UART: uart[1] event[0m
[0;32mI (445901) UART: uart rx break[0m
[0;32mI (445901) UART: uart[1] event[0m
[0;32mI (445911) UART: uart rx break[0m
[0;32mI (445911) UART: uart[1] event[0m
[0;32mI (445911) UART: uart rx break[0m
[0;32mI (445921) UART: uart[1] event[0m
[0;32mI (445921) UART: uart rx break[0m
[0;32mI (445921) UART: uart[1] event[0m
[0;32mI (445921) UART: uart rx break[0m
[0;32mI (445931) UART: uart[1] event[0m
[0;32mI (445931) UART: uart rx break[0m
[0;32mI (445931) UART: uart[1] event[0m
[0;32mI (445941) UART: uart rx break[0m
[0;32mI (445941) UART: uart[1] event[0m
[0;32mI (445941) UART: uart rx break[0m
[0;32mI (445941) UART: uart[1] event[0m
[0;32mI (445951) UART: uart rx break[0m
[0;32mI (445951) UART: uart[0] event[0m
[0;32mI (445951) UART_READ: : 2[0m
[0;32mI (445951) UART_READ: : : UART_READ_END[0m
[0;32mI (445961) UART READ: [LEN: ]: 731[0m
[0;32mI (445961) UART: uart[1] event[0m
[0;32mI (445961) UART: uart rx break[0m
[0;32mI (445971) UART: uart[1] event[0m
[0;32mI (445971) UART: uart rx break[0m
[0;32mI (445971) UART: uart[1] event[0m
[0;32mI (445971) UART: uart rx break[0m
[0;32mI (445981) UART: uart[1] event[0m
[0;32mI (445981) UART: uart rx break[0m
[0;32mI (445981) UART: uart[1] event[0m
[0;32mI (445991) UART: uart rx break[0m
[0;32mI (445991) UART: uart[1] event[0m
[0;32mI (445991) UART: uart rx break[0m
[0;32mI (445991) UART: uart[1] event[0m
[0;32mI (446001) UART: uart rx break[0m
[0;32mI (446001) UART: uart[1] event[0m
[0;32mI (446001) UART: uart rx break[0m
[0;32mI (446011) UART: uart[1] event[0m
[0;32mI (446011) UART: uart rx break[0m
[0;32mI (446011) UART: uart[1] event[0m
[0;32mI (446011) UART: uart rx break[0m
[0;32mI (446021) UART: uart[1] event[0m
[0;32mI (446021) UART: uart rx break[0m
[0;32mI (446021) UART: uart[1] event[0m
[0;32mI (446021) UART: uart rx break[0m
[0;32mI (446031) UART: uart[1] event[0m
[0;32mI (446031) UART: uart rx break[0m
[0;32mI (446031) UART: uart[1] event[0m
[0;32mI (446041) UART: uart rx break[0m
[0;32mI (446041) UART: uart[1] event[0m
[0;32mI (446041) UART: uart rx break[0m
[0;32mI (446041) UART: uart[1] event[0m
[0;32mI (446051) UART: uart rx break[0m
[0;32mI (446051) UART: uart[1] event[0m
[0;32mI (446051) UART: uart rx break[0m
[0;32mI (446061) UART: uart[1] event[0m
[0;32mI (446061) UART: uart rx break[0m
[0;32mI (446061) UART: uart[1] event[0m
[0;32mI (446061) UART: uart rx break[0m
[0;32mI (446071) UART: uart[1] event[0m
[0;32mI (446071) UART: uart rx break[0m
[0;32mI (446071) UART: uart[1] event[0m
[0;32mI (446071) UART: uart rx break[0m
[0;32mI (446081) UART: uart[1] event[0m
[0;32mI (446081) UART: uart rx break[0m
[0;32mI (446081) UART: uart[1] event[0m
[0;32mI (446091) UART: uart rx break[0m
[0;32mI (446091) UART: uart[1] event[0m
[0;32mI (446091) UART: uart rx break[0m
[0;32mI (446091) UART: uart[1] event[0m
[0;32mI (446101) UART: uart rx break[0m
[0;32mI (446101) UART: uart[1] event[0m
[0;32mI (446101) UART: uart rx break[0m
[0;32mI (446111) UART: uart[1] event[0m
[0;32mI (446111) UART: uart rx break[0m
[0;32mI (446111) UART: uart[1] event[0m
[0;32mI (446111) UART: uart rx break[0m
[0;32mI (446121) UART: uart[1] event[0m
[0;32mI (446121) UART: uart rx break[0m
[0;32mI (446121) UART: uart[1] event[0m
[0;32mI (446121) UART: uart rx break[0m
[0;32mI (446131) UART: uart[1] event[0m
[0;32mI (446131) UART: uart rx break[0m
[0;32mI (446131) UART: uart[1] event[0m
[0;32mI (446141) UART: uart rx break[0m
[0;32mI (446141) UART: uart[1] event[0m
[0;32mI (446141) UART: uart rx break[0m
[0;32mI (446141) UART: uart[1] event[0m
[0;32mI (446151) UART: uart rx break[0m
[0;32mI (446151) UART: uart[1] event[0m
[0;32mI (446151) UART: uart rx break[0m
[0;32mI (446161) UART: uart[1] event[0m
[0;32mI (446161) UART: uart rx break[0m
[0;32mI (446161) UART: uart[1] event[0m
[0;32mI (446161) UART: uart rx break[0m
[0;32mI (446171) UART: uart[1] event[0m
[0;32mI (446171) UART: uart rx break[0m
[0;32mI (446171) UART: uart[1] event[0m
[0;32mI (446171) UART: uart rx break[0m
[0;32mI (446181) UART: uart[1] event[0m
[0;32mI (446181) UART: uart rx break[0m
[0;32mI (446181) UART: uart[1] event[0m
[0;32mI (446191) UART: uart rx break[0m
[0;32mI (446191) UART: uart[1] event[0m
[0;32mI (446191) UART: uart rx break[0m
[0;32mI (446191) UART: uart[1] event[0m
[0;32mI (446201) UART: uart rx break[0m
[0;32mI (446201) UART: uart[1] event[0m
[0;32mI (446201) UART: uart rx break[0m
[0;32mI (446211) UART: uart[1] event[0m
[0;32mI (446211) UART: uart rx break[0m
[0;32mI (446211) UART: uart[1] event[0m
[0;32mI (446211) UART: uart rx break[0m
[0;32mI (446221) UART: uart[1] event[0m
[0;32mI (446221) UART: uart rx break[0m
[0;32mI (446221) UART: uart[1] event[0m
[0;32mI (446221) UART: uart rx break[0m
[0;32mI (446231) UART: uart[1] event[0m
[0;32mI (446231) UART: uart rx break[0m
[0;32mI (446231) UART: uart[1] event[0m
[0;32mI (446241) UART: uart rx break[0m
[0;32mI (446241) UART: uart[1] event[0m
[0;32mI (446241) UART: uart rx break[0m
[0;32mI (446241) UART: uart[1] event[0m
[0;32mI (446251) UART: uart rx break[0m
[0;32mI (446251) UART: uart[1] event[0m
[0;32mI (446251) UART: uart rx break[0m
[0;32mI (446261) UART: uart[1] event[0m
[0;32mI (446261) UART: uart rx break[0m
[0;32mI (446261) UART: uart[1] event[0m
[0;32mI (446261) UART: uart rx break[0m
[0;32mI (446271) UART: uart[1] event[0m
[0;32mI (446271) UART: uart rx break[0m
[0;32mI (446271) UART: uart[1] event[0m
[0;32mI (446271) UART: uart rx break[0m
[0;32mI (446281) UART: uart[1] event[0m
[0;32mI (446281) UART: uart rx break[0m
[0;32mI (446281) UART: uart[1] event[0m
[0;32mI (446291) UART: uart rx break[0m
[0;32mI (446291) UART: uart[1] event[0m
[0;32mI (446291) UART: uart rx break[0m
[0;32mI (446291) UART: uart[1] event[0m
[0;32mI (446301) UART: uart rx break[0m
[0;32mI (446301) UART: uart[0] event[0m
[0;32mI (446301) UART_READ: : 2[0m
[0;32mI (446301) UART_READ: : : UART_READ_END[0m
[0;32mI (446311) UART READ: [LEN: ]: 851[0m
[0;32mI (446311) UART: uart[1] event[0m
[0;32mI (446311) UART: uart rx break[0m
[0;32mI (446321) UART: uart[1] event[0m
[0;32mI (446321) UART: uart rx break[0m
[0;32mI (446321) UART: uart[1] event[0m
[0;32mI (446331) UART: uart rx break[0m
[0;32mI (446331) UART: uart[1] event[0m
[0;32mI (446331) UART: uart rx break[0m
[0;32mI (446331) UART: uart[1] event[0m
[0;32mI (446341) UART: uart rx break[0m
[0;32mI (446341) UART: uart[1] event[0m
[0;32mI (446341) UART: uart rx break[0m
[0;32mI (446341) UART: uart[1] event[0m
[0;32mI (446351) UART: uart rx break[0m
[0;32mI (446351) UART: uart[1] event[0m
[0;32mI (446351) UART: uart rx break[0m
[0;32mI (446361) UART: uart[1] event[0m
[0;32mI (446361) UART: uart rx break[0m
[0;32mI (446361) UART: uart[1] event[0m
[0;32mI (446361) UART: uart rx break[0m
[0;32mI (446371) UART: uart[1] event[0m
[0;32mI (446371) UART: uart rx break[0m
[0;32mI (446371) UART: uart[1] event[0m
[0;32mI (446381) UART: uart rx break[0m
[0;32mI (446381) UART: uart[1] event[0m
[0;32mI (446381) UART: uart rx break[0m
[0;32mI (446381) UART: uart[1] event[0m
[0;32mI (446391) UART: uart rx break[0m
[0;32mI (446391) UART: uart[1] event[0m
[0;32mI (446391) UART: uart rx break[0m
[0;32mI (446391) UART: uart[1] event[0m
[0;32mI (446401) UART: uart rx break[0m
[0;32mI (446401) UART: uart[1] event[0m
[0;32mI (446401) UART: uart rx break[0m
[0;32mI (446411) UART: uart[1] event[0m
[0;32mI (446411) UART: uart rx break[0m
[0;32mI (446411) UART: uart[1] event[0m
[0;32mI (446411) UART: uart rx break[0m
[0;32mI (446421) UART: uart[1] event[0m
[0;32mI (446421) UART: uart rx break[0m
[0;32mI (446421) UART: uart[1] event[0m
[0;32mI (446431) UART: uart rx break[0m
[0;32mI (446431) UART: uart[1] event[0m
[0;32mI (446431) UART: uart rx break[0m
[0;32mI (446431) UART: uart[1] event[0m
[0;32mI (446441) UART: uart rx break[0m
[0;32mI (446441) UART: uart[1] event[0m
[0;32mI (446441) UART: uart rx break[0m
[0;32mI (446441) UART: uart[1] event[0m
[0;32mI (446451) UART: uart rx break[0m
[0;32mI (446451) UART: uart[1] event[0m
[0;32mI (446451) UART: uart rx break[0m
[0;32mI (446461) UART: uart[1] event[0m
[0;32mI (446461) UART: uart rx break[0m
[0;32mI (446461) UART: uart[1] event[0m
[0;32mI (446461) UART: uart rx break[0m
[0;32mI (446471) UART: uart[1] event[0m
[0;32mI (446471) UART: uart rx break[0m
[0;32mI (446471) UART: uart[1] event[0m
[0;32mI (446481) UART: uart rx break[0m
[0;32mI (446481) UART: uart[1] event[0m
[0;32mI (446481) UART: uart rx break[0m
[0;32mI (446481) UART: uart[1] event[0m
[0;32mI (446491) UART: uart rx break[0m
[0;32mI (446491) UART: uart[1] event[0m
[0;32mI (446491) UART: uart rx break[0m
[0;32mI (446491) UART: uart[1] event[0m
[0;32mI (446501) UART: uart rx break[0m
[0;32mI (446501) UART: uart[1] event[0m
[0;32mI (446501) UART: uart rx break[0m
[0;32mI (446511) UART: uart[1] event[0m
[0;32mI (446511) UART: uart rx break[0m
[0;32mI (446511) UART: uart[1] event[0m
[0;32mI (446511) UART: uart rx break[0m
[0;32mI (446521) UART: uart[1] event[0m
[0;32mI (446521) UART: uart rx break[0m
[0;32mI (446521) UART: uart[1] event[0m
[0;32mI (446531) UART: uart rx break[0m
[0;32mI (446531) UART: uart[1] event[0m
[0;32mI (446531) UART: uart rx break[0m
[0;32mI (446531) UART: uart[1] event[0m
[0;32mI (446541) UART: uart rx break[0m
[0;32mI (446541) UART: uart[1] event[0m
[0;32mI (446541) UART: uart rx break[0m
[0;32mI (446541) UART: uart[1] event[0m
[0;32mI (446551) UART: uart rx break[0m
[0;32mI (446551) UART: uart[1] event[0m
[0;32mI (446551) UART: uart rx break[0m
[0;32mI (446561) UART: uart[1] event[0m
[0;32mI (446561) UART: uart rx break[0m
[0;32mI (446561) UART: uart[1] event[0m
[0;32mI (446561) UART: uart rx break[0m
[0;32mI (446571) UART: uart[1] event[0m
[0;32mI (446571) UART: uart rx break[0m
[0;32mI (446571) UART: uart[1] event[0m
[0;32mI (446581) UART: uart rx break[0m
[0;32mI (446581) UART: uart[1] event[0m
[0;32mI (446581) UART: uart rx break[0m
[0;32mI (446581) UART: uart[1] event[0m
[0;32mI (446591) UART: uart rx break[0m
[0;32mI (446591) UART: uart[1] event[0m
[0;32mI (446591) UART: uart rx break[0m
[0;32mI (446591) UART: uart[1] event[0m
[0;32mI (446601) UART: uart rx break[0m
[0;32mI (446601) UART: uart[1] event[0m
[0;32mI (446601) UART: uart rx break[0m
[0;32mI (446611) UART: uart[1] event[0m
[0;32mI (446611) UART: uart rx break[0m
[0;32mI (446611) UART: uart[1] event[0m
[0;32mI (446611) UART: uart rx break[0m
[0;32mI (446621) UART: uart[1] event[0m
[0;32mI (446621) UART: uart rx break[0m
[0;32mI (446621) UART: uart[1] event[0m
[0;32mI (446631) UART: uart rx break[0m
[0;32mI (446631) UART: uart[1] event[0m
[0;32mI (446631) UART: uart rx break[0m
[0;32mI (446631) UART: uart[1] event[0m
[0;32mI (446641) UART: uart rx break[0m
[0;32mI (446641) UART: uart[1] event[0m
[0;32mI (446641) UART: uart rx break[0m
[0;32mI (446641) UART: uart[1] event[0m
[0;32mI (446651) UART: uart rx break[0m
[0;32mI (446651) UART: uart[1] event[0m
[0;32mI (446651) UART: uart rx break[0m
[0;32mI (446661) UART: uart[1] event[0m
[0;32mI (446661) UART: uart rx break[0m
[0;32mI (446661) UART: uart[1] event[0m
[0;32mI (446661) UART: uart rx break[0m
[0;32mI (446671) UART: uart[1] event[0m
[0;32mI (446671) UART: uart rx break[0m
[0;32mI (446671) UART: uart[1] event[0m
[0;32mI (446681) UART: uart rx break[0m
[0;32mI (446681) UART: uart[1] event[0m
[0;32mI (446681) UART: uart rx break[0m
[0;32mI (446681) UART: uart[1] event[0m
[0;32mI (446691) UART: uart rx break[0m
[0;32mI (446691) UART: uart[1] event[0m
[0;32mI (446691) UART: uart rx break[0m
[0;32mI (446691) UART: uart[1] event[0m
[0;32mI (446701) UART: uart rx break[0m
[0;32mI (446701) UART: uart[1] event[0m
[0;32mI (446701) UART: uart rx break[0m
[0;32mI (446711) UART: uart[1] event[0m
[0;32mI (446711) UART: uart rx break[0m
[0;32mI (446711) UART: uart[1] event[0m
[0;32mI (446711) UART: uart rx break[0m
[0;32mI (446721) UART: uart[1] event[0m
[0;32mI (446721) UART: uart rx break[0m
[0;32mI (446721) UART: uart[1] event[0m
[0;32mI (446731) UART: uart rx break[0m
[0;32mI (446731) UART: uart[1] event[0m
[0;32mI (446731) UART: uart rx break[0m
[0;32mI (446731) UART: uart[1] event[0m
[0;32mI (446741) UART: uart rx break[0m
[0;32mI (446741) UART: uart[1] event[0m
[0;32mI (446741) UART: uart rx break[0m
[0;32mI (446741) UART: uart[1] event[0m
[0;32mI (446751) UART: uart rx break[0m
[0;32mI (446751) UART: uart[1] event[0m
[0;32mI (446751) UART: uart rx break[0m
[0;32mI (446761) UART: uart[1] event[0m
[0;32mI (446761) UART: uart rx break[0m
[0;32mI (446761) UART: uart[1] event[0m
[0;32mI (446761) UART: uart rx break[0m
[0;32mI (446771) UART: uart[1] event[0m
[0;32mI (446771) UART: uart rx break[0m
[0;32mI (446771) UART: uart[1] event[0m
[0;32mI (446781) UART: uart rx break[0m
[0;32mI (446781) UART: uart[1] event[0m
[0;32mI (446781) UART: uart rx break[0m
[0;32mI (446781) UART: uart[1] event[0m
[0;32mI (446791) UART: uart rx break[0m
[0;32mI (446791) UART: uart[1] event[0m
[0;32mI (446791) UART: uart rx break[0m
[0;32mI (446791) UART: uart[1] event[0m
[0;32mI (446801) UART: uart rx break[0m
[0;32mI (446801) UART: uart[1] event[0m
[0;32mI (446801) UART: uart rx break[0m
[0;32mI (446811) UART: uart[1] event[0m
[0;32mI (446811) UART: uart rx break[0m
[0;32mI (446811) UART: uart[1] event[0m
[0;32mI (446811) UART: uart rx break[0m
[0;32mI (446821) UART: uart[1] event[0m
[0;32mI (446821) UART: uart rx break[0m
[0;32mI (446821) UART: uart[1] event[0m
[0;32mI (446831) UART: uart rx break[0m
[0;32mI (446831) UART: uart[1] event[0m
[0;32mI (446831) UART: uart rx break[0m
[0;32mI (446831) UART: uart[1] event[0m
[0;32mI (446841) UART: uart rx break[0m
[0;32mI (446841) UART: uart[1] event[0m
[0;32mI (446841) UART: uart rx break[0m
[0;32mI (446841) UART: uart[1] event[0m
[0;32mI (446851) UART: uart rx break[0m
[0;32mI (446851) UART: uart[1] event[0m
[0;32mI (446851) UART: uart rx break[0m
[0;32mI (446861) UART: uart[1] event[0m
[0;32mI (446861) UART: uart rx break[0m
[0;32mI (446861) UART: uart[1] event[0m
[0;32mI (446861) UART: uart rx break[0m
[0;32mI (446871) UART: uart[1] event[0m
[0;32mI (446871) UART: uart rx break[0m
[0;32mI (446871) UART: uart[1] event[0m
[0;32mI (446881) UART: uart rx break[0m
[0;32mI (446881) UART: uart[1] event[0m
[0;32mI (446881) UART: uart rx break[0m
[0;32mI (446881) UART: uart[1] event[0m
[0;32mI (446891) UART: uart rx break[0m
[0;32mI (446891) UART: uart[1] event[0m
[0;32mI (446891) UART: uart rx break[0m
[0;32mI (446891) UART: uart[1] event[0m
[0;32mI (446901) UART: uart rx break[0m
[0;32mI (446901) UART: uart[1] event[0m
[0;32mI (446901) UART: uart rx break[0m
[0;32mI (446911) UART: uart[1] event[0m
[0;32mI (446911) UART: uart rx break[0m
[0;32mI (446911) UART: uart[1] event[0m
[0;32mI (446911) UART: uart rx break[0m
[0;32mI (446921) UART: uart[1] event[0m
[0;32mI (446921) UART: uart rx break[0m
[0;32mI (446921) UART: uart[1] event[0m
[0;32mI (446931) UART: uart rx break[0m
[0;32mI (446931) UART: uart[1] event[0m
[0;32mI (446931) UART: uart rx break[0m
[0;32mI (446931) UART: uart[1] event[0m
[0;32mI (446941) UART: uart rx break[0m
[0;32mI (446941) UART: uart[1] event[0m
[0;32mI (446941) UART: uart rx break[0m
[0;32mI (446941) UART: uart[1] event[0m
[0;32mI (446951) UART: uart rx break[0m
[0;32mI (446951) UART: uart[1] event[0m
[0;32mI (446951) UART: uart rx break[0m
[0;32mI (446961) UART: uart[1] event[0m
[0;32mI (446961) UART: uart rx break[0m
[0;32mI (446961) UART: uart[1] event[0m
[0;32mI (446961) UART: uart rx break[0m
[0;32mI (446971) UART: uart[1] event[0m
[0;32mI (446971) UART: uart rx break[0m
[0;32mI (446971) UART: uart[1] event[0m
[0;32mI (446981) UART: uart rx break[0m
[0;32mI (446981) UART: uart[1] event[0m
[0;32mI (446981) UART: uart rx break[0m
[0;32mI (446981) UART: uart[1] event[0m
[0;32mI (446991) UART: uart rx break[0m
[0;32mI (446991) UART: uart[1] event[0m
[0;32mI (446991) UART: uart rx break[0m
[0;32mI (446991) UART: uart[1] event[0m
[0;32mI (447001) UART: uart rx break[0m
[0;32mI (447001) UART: uart[1] event[0m
[0;32mI (447001) UART: uart rx break[0m
[0;32mI (447011) UART: uart[1] event[0m
[0;32mI (447011) UART: uart rx break[0m
[0;32mI (447011) UART: uart[1] event[0m
[0;32mI (447011) UART: uart rx break[0m
[0;32mI (447021) UART: uart[1] event[0m
[0;32mI (447021) UART: uart rx break[0m
[0;32mI (447021) UART: uart[1] event[0m
[0;32mI (447031) UART: uart rx break[0m
[0;32mI (447031) UART: uart[1] event[0m
[0;32mI (447031) UART: uart rx break[0m
[0;32mI (447031) UART: uart[1] event[0m
[0;32mI (447041) UART: uart rx break[0m
[0;32mI (447041) UART: uart[1] event[0m
[0;32mI (447041) UART: uart rx break[0m
[0;32mI (447041) UART: uart[1] event[0m
[0;32mI (447051) UART: uart rx break[0m
[0;32mI (447051) UART: uart[1] event[0m
[0;32mI (447051) UART: uart rx break[0m
[0;32mI (447061) UART: uart[1] event[0m
[0;32mI (447061) UART: uart rx break[0m
[0;32mI (447061) UART: uart[1] event[0m
[0;32mI (447061) UART: uart rx break[0m
[0;32mI (447071) UART: uart[1] event[0m
[0;32mI (447071) UART: uart rx break[0m
[0;32mI (447071) UART: uart[1] event[0m
[0;32mI (447081) UART: uart rx break[0m
[0;32mI (447081) UART: uart[1] event[0m
[0;32mI (447081) UART: uart rx break[0m
[0;32mI (447081) UART: uart[1] event[0m
[0;32mI (447091) UART: uart rx break[0m
[0;32mI (447091) UART: uart[1] event[0m
[0;32mI (447091) UART: uart rx break[0m
[0;32mI (447091) UART: uart[1] event[0m
[0;32mI (447101) UART: uart rx break[0m
[0;32mI (447101) UART: uart[1] event[0m
[0;32mI (447101) UART: uart rx break[0m
[0;32mI (447111) UART: uart[1] event[0m
[0;32mI (447111) UART: uart rx break[0m
[0;32mI (447111) UART: uart[1] event[0m
[0;32mI (447111) UART: uart rx break[0m
[0;32mI (447121) UART: uart[1] event[0m
[0;32mI (447121) UART: uart rx break[0m
[0;32mI (447121) UART: uart[1] event[0m
[0;32mI (447131) UART: uart rx break[0m
[0;32mI (447131) UART: uart[1] event[0m
[0;32mI (447131) UART: uart rx break[0m
[0;32mI (447131) UART: uart[1] event[0m
[0;32mI (447141) UART: uart rx break[0m
[0;32mI (447141) UART: uart[1] event[0m
[0;32mI (447141) UART: uart rx break[0m
[0;32mI (447141) UART: uart[1] event[0m
[0;32mI (447151) UART: uart rx break[0m
[0;32mI (447151) UART: uart[1] event[0m
[0;32mI (447151) UART: uart rx break[0m
[0;32mI (447161) UART: uart[1] event[0m
[0;32mI (447161) UART: uart rx break[0m
[0;32mI (447161) UART: uart[1] event[0m
[0;32mI (447161) UART: uart rx break[0m
[0;32mI (447171) UART: uart[1] event[0m
[0;32mI (447171) UART: uart rx break[0m
[0;32mI (447171) UART: uart[1] event[0m
[0;32mI (447181) UART: uart rx break[0m
[0;32mI (447181) UART: uart[1] event[0m
[0;32mI (447181) UART: uart rx break[0m
[0;32mI (447181) UART: uart[1] event[0m
[0;32mI (447191) UART: uart rx break[0m
[0;32mI (447191) UART: uart[1] event[0m
[0;32mI (447191) UART: uart rx break[0m
[0;32mI (447191) UART: uart[1] event[0m
[0;32mI (447201) UART: uart rx break[0m
[0;32mI (447201) UART: uart[1] event[0m
[0;32mI (447201) UART: uart rx break[0m
[0;32mI (447211) UART: uart[1] event[0m
[0;32mI (447211) UART: uart rx break[0m
[0;32mI (447211) UART: uart[1] event[0m
[0;32mI (447211) UART: uart rx break[0m
[0;32mI (447221) UART: uart[1] event[0m
[0;32mI (447221) UART: uart rx break[0m
[0;32mI (447221) UART: uart[1] event[0m
[0;32mI (447231) UART: uart rx break[0m
[0;32mI (447231) UART: uart[1] event[0m
[0;32mI (447231) UART: uart rx break[0m
[0;32mI (447231) UART: uart[1] event[0m
[0;32mI (447241) UART: uart rx break[0m
[0;32mI (447241) UART: uart[1] event[0m
[0;32mI (447241) UART: uart rx break[0m
[0;32mI (447241) UART: uart[1] event[0m
[0;32mI (447251) UART: uart rx break[0m
[0;32mI (447251) UART: uart[1] event[0m
[0;32mI (447251) UART: uart rx break[0m
[0;32mI (447261) UART: uart[1] event[0m
[0;32mI (447261) UART: uart rx break[0m
[0;32mI (447261) UART: uart[1] event[0m
[0;32mI (447261) UART: uart rx break[0m
[0;32mI (447271) UART: uart[1] event[0m
[0;32mI (447271) UART: uart rx break[0m
[0;32mI (447271) UART: uart[1] event[0m
[0;32mI (447281) UART: uart rx break[0m
[0;32mI (450331) UART: uart[1] event[0m
[0;32mI (450331) UART: uart rx break[0m
[0;32mI (450331) UART: uart[1] event[0m
[0;32mI (450331) UART: uart rx break[0m
[0;32mI (450331) UART: uart[1] event[0m
[0;32mI (450331) UART: uart rx break[0m
[0;32mI (450341) UART: uart[1] event[0m
[0;32mI (450341) UART: uart rx break[0m
[0;32mI (450341) UART: uart[1] event[0m
[0;32mI (450341) UART: uart rx break[0m
[0;32mI (450351) UART: uart[1] event[0m
[0;32mI (450351) UART: uart rx break[0m
[0;32mI (450351) UART: uart[1] event[0m
[0;32mI (450351) UART: uart rx break[0m
[0;32mI (450361) UART: uart[1] event[0m
[0;32mI (450361) UART: uart rx break[0m
[0;32mI (450361) UART: uart[1] event[0m
[0;32mI (450371) UART: uart rx break[0m
[0;32mI (450371) UART: uart[1] event[0m
[0;32mI (450371) UART: uart rx break[0m
[0;32mI (450371) UART: uart[1] event[0m
[0;32mI (450381) UART: uart rx break[0m
[0;32mI (450381) UART: uart[1] event[0m
[0;32mI (450381) UART: uart rx break[0m
[0;32mI (450391) UART: uart[1] event[0m
[0;32mI (450391) UART: uart rx break[0m
[0;32mI (450391) UART: uart[1] event[0m
[0;32mI (450391) UART: uart rx break[0m
[0;32mI (450401) UART: uart[1] event[0m
[0;32mI (450401) UART: uart rx break[0m
[0;32mI (450401) UART: uart[1] event[0m
[0;32mI (450401) UART: uart rx break[0m
[0;32mI (450411) UART: uart[1] event[0m
[0;32mI (450411) UART: uart rx break[0m
[0;32mI (450411) UART: uart[1] event[0m
[0;32mI (450421) UART: uart rx break[0m
[0;32mI (450421) UART: uart[1] event[0m
[0;32mI (450421) UART: uart rx break[0m
[0;32mI (450421) UART: uart[1] event[0m
[0;32mI (450431) UART: uart rx break[0m
[0;32mI (450431) UART: uart[1] event[0m
[0;32mI (450431) UART: uart rx break[0m
[0;32mI (450441) UART: uart[1] event[0m
[0;32mI (450441) UART: uart rx break[0m
[0;32mI (450441) UART: uart[1] event[0m
[0;32mI (450441) UART: uart rx break[0m
[0;32mI (450451) UART: uart[1] event[0m
[0;32mI (450451) UART: uart rx break[0m
[0;32mI (450451) UART: uart[1] event[0m
[0;32mI (450451) UART: uart rx break[0m
[0;32mI (450461) UART: uart[1] event[0m
[0;32mI (450461) UART: uart rx break[0m
[0;32mI (450461) UART: uart[1] event[0m
[0;32mI (450471) UART: uart rx break[0m
[0;32mI (450471) UART: uart[1] event[0m
[0;32mI (450471) UART: uart rx break[0m
[0;32mI (450471) UART: uart[1] event[0m
[0;32mI (450481) UART: uart rx break[0m
[0;32mI (450481) UART: uart[1] event[0m
[0;32mI (450481) UART: uart rx break[0m
[0;32mI (450491) UART: uart[1] event[0m
[0;32mI (450491) UART: uart rx break[0m
[0;32mI (450491) UART: uart[1] event[0m
[0;32mI (450491) UART: uart rx break[0m
[0;32mI (450501) UART: uart[1] event[0m
[0;32mI (450501) UART: uart rx break[0m
[0;32mI (450501) UART: uart[1] event[0m
[0;32mI (450501) UART: uart rx break[0m
[0;32mI (450511) UART: uart[1] event[0m
[0;32mI (450511) UART: uart rx break[0m
[0;32mI (450511) UART: uart[1] event[0m
[0;32mI (450521) UART: uart rx break[0m
[0;32mI (450521) UART: uart[1] event[0m
[0;32mI (450521) UART: uart rx break[0m
[0;32mI (450521) UART: uart[1] event[0m
[0;32mI (450531) UART: uart rx break[0m
[0;32mI (450531) UART: uart[1] event[0m
[0;32mI (450531) UART: uart rx break[0m
[0;32mI (450541) UART: uart[1] event[0m
[0;32mI (450541) UART: uart rx break[0m
[0;32mI (450541) UART: uart[1] event[0m
[0;32mI (450541) UART: uart rx break[0m
[0;32mI (450551) UART: uart[1] event[0m
[0;32mI (450551) UART: uart rx break[0m
[0;32mI (450551) UART: uart[1] event[0m
[0;32mI (450551) UART: uart rx break[0m
[0;32mI (450561) UART: uart[1] event[0m
[0;32mI (450561) UART: uart rx break[0m
[0;32mI (450561) UART: uart[1] event[0m
[0;32mI (450571) UART: uart rx break[0m
[0;32mI (450571) UART: uart[1] event[0m
[0;32mI (450571) UART: uart rx break[0m
[0;32mI (450571) UART: uart[1] event[0m
[0;32mI (450581) UART: uart rx break[0m
[0;32mI (450581) UART: uart[1] event[0m
[0;32mI (450581) UART: uart rx break[0m
[0;32mI (450591) UART: uart[1] event[0m
[0;32mI (450591) UART: uart rx break[0m
[0;32mI (450591) UART: uart[1] event[0m
[0;32mI (450591) UART: uart rx break[0m
[0;32mI (450601) UART: uart[1] event[0m
[0;32mI (450601) UART: uart rx break[0m
[0;32mI (450601) UART: uart[1] event[0m
[0;32mI (450601) UART: uart rx break[0m
[0;32mI (450611) UART: uart[0] event[0m
[0;32mI (450611) UART_READ: : 2[0m
[0;32mI (450611) UART_READ: : : UART_READ_END[0m
[0;32mI (450621) UART READ: [LEN: ]: 947[0m
[0;32mI (450621) UART: uart[1] event[0m
[0;32mI (450621) UART: uart rx break[0m
[0;32mI (450621) UART: uart[1] event[0m
[0;32mI (450631) UART: uart rx break[0m
[0;32mI (450631) UART: uart[1] event[0m
[0;32mI (450631) UART: uart rx break[0m
[0;32mI (450641) UART: uart[1] event[0m
[0;32mI (450641) UART: uart rx break[0m
[0;32mI (450641) UART: uart[1] event[0m
[0;32mI (450641) UART: uart rx break[0m
[0;32mI (450651) UART: uart[1] event[0m
[0;32mI (450651) UART: uart rx break[0m
[0;32mI (450651) UART: uart[1] event[0m
[0;32mI (450661) UART: uart rx break[0m
[0;32mI (450661) UART: uart[1] event[0m
[0;32mI (450661) UART: uart rx break[0m
[0;32mI (450661) UART: uart[1] event[0m
[0;32mI (450671) UART: uart rx break[0m
[0;32mI (450671) UART: uart[1] event[0m
[0;32mI (450671) UART: uart rx break[0m
[0;32mI (450671) UART: uart[1] event[0m
[0;32mI (450681) UART: uart rx break[0m
[0;32mI (450681) UART: uart[1] event[0m
[0;32mI (450681) UART: uart rx break[0m
[0;32mI (450691) UART: uart[1] event[0m
[0;32mI (450691) UART: uart rx break[0m
[0;32mI (450691) UART: uart[1] event[0m
[0;32mI (450691) UART: uart rx break[0m
[0;32mI (450701) UART: uart[1] event[0m
[0;32mI (450701) UART: uart rx break[0m
[0;32mI (450701) UART: uart[1] event[0m
[0;32mI (450711) UART: uart rx break[0m
[0;32mI (450711) UART: uart[1] event[0m
[0;32mI (450711) UART: uart rx break[0m
[0;32mI (450711) UART: uart[1] event[0m
[0;32mI (450721) UART: uart rx break[0m
[0;32mI (450721) UART: uart[1] event[0m
[0;32mI (450721) UART: uart rx break[0m
[0;32mI (450721) UART: uart[1] event[0m
[0;32mI (450731) UART: uart rx break[0m
[0;32mI (450731) UART: uart[1] event[0m
[0;32mI (450731) UART: uart rx break[0m
[0;32mI (450741) UART: uart[1] event[0m
[0;32mI (450741) UART: uart rx break[0m
[0;32mI (450741) UART: uart[0] event[0m
[0;32mI (450741) UART_READ: : 2[0m
[0;32mI (450751) UART_READ: : : UART_READ_END[0m
[0;32mI (450751) UART READ: [LEN: ]: 989[0m
[0;32mI (450751) UART: uart[1] event[0m
[0;32mI (450761) UART: uart rx break[0m
[0;32mI (450761) UART: uart[1] event[0m
[0;32mI (450761) UART: uart rx break[0m
[0;32mI (450761) UART: uart[1] event[0m
[0;32mI (450771) UART: uart rx break[0m
[0;32mI (450771) UART: uart[1] event[0m
[0;32mI (450771) UART: uart rx break[0m
[0;32mI (450781) UART: uart[1] event[0m
[0;32mI (450781) UART: uart rx break[0m
[0;32mI (450781) UART: uart[1] event[0m
[0;32mI (450781) UART: uart rx break[0m
[0;32mI (450791) UART: uart[1] event[0m
[0;32mI (450791) UART: uart rx break[0m
[0;32mI (450791) UART: uart[1] event[0m
[0;32mI (450791) UART: uart rx break[0m
[0;32mI (450801) UART: uart[1] event[0m
[0;32mI (450801) UART: uart rx break[0m
[0;32mI (450801) UART: uart[1] event[0m
[0;32mI (450811) UART: uart rx break[0m
[0;32mI (450811) UART: uart[1] event[0m
[0;32mI (450811) UART: uart rx break[0m
[0;32mI (450811) UART: uart[1] event[0m
[0;32mI (450821) UART: uart rx break[0m
[0;32mI (450821) UART: uart[1] event[0m
[0;32mI (450821) UART: uart rx break[0m
[0;32mI (450831) UART: uart[1] event[0m
[0;32mI (450831) UART: uart rx break[0m
[0;32mI (450831) UART: uart[1] event[0m
[0;32mI (450831) UART: uart rx break[0m
[0;32mI (450841) UART: uart[1] event[0m
[0;32mI (450841) UART: uart rx break[0m
[0;32mI (450841) UART: uart[1] event[0m
[0;32mI (450841) UART: uart rx break[0m
[0;32mI (450851) UART: uart[1] event[0m
[0;32mI (450851) UART: uart rx break[0m
[0;32mI (450851) UART: uart[1] event[0m
[0;32mI (450861) UART: uart rx break[0m
[0;32mI (450861) UART: uart[1] event[0m
[0;32mI (450861) UART: uart rx break[0m
[0;32mI (450861) UART: uart[1] event[0m
[0;32mI (450871) UART: uart rx break[0m
[0;32mI (450871) UART: uart[0] event[0m
[0;32mI (450871) UART_READ: : 2[0m
[0;32mI (450871) UART_READ: : : UART_READ_END[0m
[0;32mI (450881) UART READ: [LEN: ]: 1012[0m
[0;32mI (450881) UART: uart[1] event[0m
[0;32mI (450881) UART: uart rx break[0m
[0;32mI (450891) UART: uart[1] event[0m
[0;32mI (450891) UART: uart rx break[0m
[0;32mI (450891) UART: uart[1] event[0m
[0;32mI (450901) UART: uart rx break[0m
[0;32mI (450901) UART: uart[1] event[0m
[0;32mI (450901) UART: uart rx break[0m
[0;32mI (450901) UART: uart[1] event[0m
[0;32mI (450911) UART: uart rx break[0m
[0;32mI (450911) UART: uart[1] event[0m
[0;32mI (450911) UART: uart rx break[0m
[0;32mI (450911) UART: uart[1] event[0m
[0;32mI (450921) UART: uart rx break[0m
[0;32mI (450921) UART: uart[1] event[0m
[0;32mI (450921) UART: uart rx break[0m
[0;32mI (450931) UART: uart[1] event[0m
[0;32mI (450931) UART: uart rx break[0m
[0;32mI (450931) UART: uart[1] event[0m
[0;32mI (450931) UART: uart rx break[0m
[0;32mI (450941) UART: uart[1] event[0m
[0;32mI (450941) UART: uart rx break[0m
[0;32mI (450941) UART: uart[1] event[0m
[0;32mI (450951) UART: uart rx break[0m
[0;32mI (450951) UART: uart[1] event[0m
[0;32mI (450951) UART: uart rx break[0m
[0;32mI (450951) UART: uart[1] event[0m
[0;32mI (450961) UART: uart rx break[0m
[0;32mI (450961) UART: uart[1] event[0m
[0;32mI (450961) UART: uart rx break[0m
[0;32mI (450961) UART: uart[1] event[0m
[0;32mI (450971) UART: uart rx break[0m
[0;32mI (450971) UART: uart[1] event[0m
[0;32mI (450971) UART: uart rx break[0m
[0;32mI (450981) UART: uart[1] event[0m
[0;32mI (450981) UART: uart rx break[0m
[0;32mI (450981) UART: uart[1] event[0m
[0;32mI (450981) UART: uart rx break[0m
[0;32mI (450991) UART: uart[1] event[0m
[0;32mI (450991) UART: uart rx break[0m
[0;32mI (450991) UART: uart[1] event[0m
[0;32mI (451001) UART: uart rx break[0m
[0;32mI (451001) UART: uart[1] event[0m
[0;32mI (451001) UART: uart rx break[0m
[0;32mI (451001) UART: uart[1] event[0m
[0;32mI (451011) UART: uart rx break[0m
[0;32mI (451011) UART: uart[1] event[0m
[0;32mI (451011) UART: uart rx break[0m
[0;32mI (451011) UART: uart[1] event[0m
[0;32mI (451021) UART: uart rx break[0m
[0;32mI (451021) UART: uart[1] event[0m
[0;32mI (451021) UART: uart rx break[0m
[0;32mI (451031) UART: uart[1] event[0m
[0;32mI (451031) UART: uart rx break[0m
[0;32mI (451031) UART: uart[1] event[0m
[0;32mI (451031) UART: uart rx break[0m
[0;32mI (451041) UART: uart[1] event[0m
[0;32mI (451041) UART: uart rx break[0m
[0;32mI (451041) UART: uart[1] event[0m
[0;32mI (451051) UART: uart rx break[0m
[0;32mI (451051) UART: uart[1] event[0m
[0;32mI (451051) UART: uart rx break[0m
[0;32mI (451051) UART: uart[1] event[0m
[0;32mI (451061) UART: uart rx break[0m
[0;32mI (451061) UART: uart[1] event[0m
[0;32mI (451061) UART: uart rx break[0m
[0;32mI (451061) UART: uart[1] event[0m
[0;32mI (451071) UART: uart rx break[0m
[0;32mI (451071) UART: uart[1] event[0m
[0;32mI (451071) UART: uart rx break[0m
[0;32mI (451081) UART: uart[1] event[0m
[0;32mI (451081) UART: uart rx break[0m
[0;32mI (451081) UART: uart[1] event[0m
[0;32mI (451081) UART: uart rx break[0m
[0;32mI (451091) UART: uart[1] event[0m
[0;32mI (451091) UART: uart rx break[0m
[0;32mI (451091) UART: uart[1] event[0m
[0;32mI (451101) UART: uart rx break[0m
[0;32mI (451101) UART: uart[1] event[0m
[0;32mI (451101) UART: uart rx break[0m
[0;32mI (451101) UART: uart[1] event[0m
[0;32mI (451111) UART: uart rx break[0m
[0;32mI (451111) UART: uart[1] event[0m
[0;32mI (451111) UART: uart rx break[0m
[0;32mI (451111) UART: uart[1] event[0m
[0;32mI (451121) UART: uart rx break[0m
[0;32mI (451121) UART: uart[1] event[0m
[0;32mI (451121) UART: uart rx break[0m
[0;32mI (451131) UART: uart[1] event[0m
[0;32mI (451131) UART: uart rx break[0m
[0;32mI (451131) UART: uart[1] event[0m
[0;32mI (451131) UART: uart rx break[0m
[0;32mI (451141) UART: uart[1] event[0m
[0;32mI (451141) UART: uart rx break[0m
[0;32mI (451141) UART: uart[1] event[0m
[0;32mI (451151) UART: uart rx break[0m
[0;32mI (451151) UART: uart[1] event[0m
[0;32mI (451151) UART: uart rx break[0m
[0;32mI (451151) UART: uart[1] event[0m
[0;32mI (451161) UART: uart rx break[0m
[0;32mI (451161) UART: uart[1] event[0m
[0;32mI (451161) UART: uart rx break[0m
[0;32mI (451161) UART: uart[1] event[0m
[0;32mI (451171) UART: uart rx break[0m
[0;32mI (451171) UART: uart[1] event[0m
[0;32mI (451171) UART: uart rx break[0m
[0;32mI (451181) UART: uart[1] event[0m
[0;32mI (451181) UART: uart rx break[0m
[0;32mI (451181) UART: uart[1] event[0m
[0;32mI (451181) UART: uart rx break[0m
[0;32mI (451191) UART: uart[1] event[0m
[0;32mI (451191) UART: uart rx break[0m
[0;32mI (451191) UART: uart[1] event[0m
[0;32mI (451201) UART: uart rx break[0m
[0;32mI (451201) UART: uart[1] event[0m
[0;32mI (451201) UART: uart rx break[0m
[0;32mI (451201) UART: uart[1] event[0m
[0;32mI (451211) UART: uart rx break[0m
[0;32mI (451211) UART: uart[1] event[0m
[0;32mI (451211) UART: uart rx break[0m
[0;32mI (451211) UART: uart[1] event[0m
[0;32mI (451221) UART: uart rx break[0m
[0;32mI (451221) UART: uart[1] event[0m
[0;32mI (451221) UART: uart rx break[0m
[0;32mI (451231) UART: uart[1] event[0m
[0;32mI (451231) UART: uart rx break[0m
[0;32mI (451231) UART: uart[1] event[0m
[0;32mI (451231) UART: uart rx break[0m
[0;32mI (451241) UART: uart[1] event[0m
[0;32mI (451241) UART: uart rx break[0m
[0;32mI (451241) UART: uart[1] event[0m
[0;32mI (451251) UART: uart rx break[0m
[0;32mI (451251) UART: uart[1] event[0m
[0;32mI (451251) UART: uart rx break[0m
[0;32mI (451761) UART: uart[1] event[0m
[0;32mI (451761) UART: uart rx break[0m
[0;32mI (451761) UART: uart[1] event[0m
[0;32mI (451761) UART: uart rx break[0m
[0;32mI (451761) UART: uart[1] event[0m
[0;32mI (451761) UART: uart rx break[0m
[0;32mI (451761) UART: uart[1] event[0m
[0;32mI (451771) UART: uart rx break[0m
[0;32mI (451771) UART: uart[1] event[0m
[0;32mI (451771) UART: uart rx break[0m
[0;32mI (451771) UART: uart[0] event[0m
[0;32mI (451781) UART_READ: : 2[0m
[0;32mI (451781) UART_READ: : : UART_READ_END[0m
[0;32mI (451781) UART READ: [LEN: ]: 1121[0m
[0;32mI (451791) UART: uart[1] event[0m
[0;32mI (451791) UART: uart rx break[0m
[0;32mI (451791) UART: uart[1] event[0m
[0;32mI (451801) UART: uart rx break[0m
[0;32mI (451801) UART: uart[1] event[0m
[0;32mI (451801) UART: uart rx break[0m
[0;32mI (451801) UART: uart[1] event[0m
[0;32mI (451811) UART: uart rESP-ROM:esp32s3-20210327[0m
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
Octal Flash Mode Enabled
For OPI Flash, Use Default Flash Boot Mode
mode:SLOW_RD, clock div:1
load:0x3fce2810,len:0x15a0
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0;32mI (33) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (33) boot: compile time Jul 24 2025 20:39:56[0m
[0;32mI (33) boot: Multicore bootloader[0m
[0;32mI (34) boot: chip revision: v0.2[0m
[0;32mI (36) boot: efuse block revision: v1.3[0m
[0;32mI (40) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (44) boot.esp32s3: SPI Mode       : SLOW READ[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 32MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (56) boot: Partition Table:[0m
[0;32mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (65) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (72) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (78) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (85) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18648h ( 99912) map[0m
[0;32mI (126) esp_image: segment 1: paddr=00028670 vaddr=3fc9b900 size=04a84h ( 19076) load[0m
[0;32mI (131) esp_image: segment 2: paddr=0002d0fc vaddr=40374000 size=02f1ch ( 12060) load[0m
[0;32mI (134) esp_image: segment 3: paddr=00030020 vaddr=42000020 size=61aa8h (400040) map[0m
[0;32mI (231) esp_image: segment 4: paddr=00091ad0 vaddr=40376f1c size=149c0h ( 84416) load[0m
[0;32mI (254) esp_image: segment 5: paddr=000a6498 vaddr=600fe100 size=0001ch (    28) load[0m
[0;32mI (263) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (264) boot: Disabling RNG early entropy source...[0m
[0;32mI (274) octal_psram: vendor id    : 0x0d (AP)[0m
[0;32mI (274) octal_psram: dev id       : 0x03 (generation 4)[0m
[0;32mI (274) octal_psram: density      : 0x05 (128 Mbit)[0m
[0;32mI (276) octal_psram: good-die     : 0x01 (Pass)[0m
[0;32mI (281) octal_psram: Latency      : 0x01 (Fixed)[0m
[0;32mI (285) octal_psram: VCC          : 0x00 (1.8V)[0m
[0;32mI (289) octal_psram: SRF          : 0x01 (Fast Refresh)[0m
[0;32mI (294) octal_psram: BurstType    : 0x01 (Hybrid Wrap)[0m
[0;32mI (299) octal_psram: BurstLen     : 0x01 (32 Byte)[0m
[0;32mI (304) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)[0m
[0;32mI (309) octal_psram: DriveStrength: 0x00 (1/1)[0m
[0;32mI (314) MSPI Timing: PSRAM timing tuning index: 6[0m
[0;32mI (317) esp_psram: Found 16MB PSRAM device[0m
[0;32mI (321) esp_psram: Speed: 80MHz[0m
[0;32mI (324) cpu_start: Multicore app[0m
[0;32mI (1179) esp_psram: SPI SRAM memory test OK[0m
[0;32mI (1196) cpu_start: Pro cpu start user code[0m
[0;32mI (1196) cpu_start: cpu freq: 240000000 Hz[0m
[0;32mI (1196) app_init: Application information:[0m
[0;32mI (1196) app_init: Project name:     Vantage_nxESP32_Int_Rel_1_2_1_0[0m
[0;32mI (1202) app_init: App version:      Vantage_Bin_nxESP32_1_2_0_0[0m
[0;32mI (1208) app_init: Compile time:     Jul 24 2025 20:38:22[0m
[0;32mI (1213) app_init: ELF file SHA256:  7fe8b3592...[0m
[0;32mI (1217) app_init: ESP-IDF:          v5.4.1[0m
[0;32mI (1221) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (1225) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (1229) efuse_init: Chip rev:         v0.2[0m
[0;32mI (1233) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (1240) heap_init: At 3FCAA4E8 len 0003F228 (252 KiB): RAM[0m
[0;32mI (1245) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (1250) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (1255) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0;32mI (1261) esp_psram: Adding pool of 16121K of PSRAM memory to heap allocator[0m
[0;33mW (1268) spi_flash: Octal flash chip is using but dio mode is selected, will automatically switch to Octal mode[0m
[0;32mI (1277) spi_flash: detected chip: mxic (opi)[0m
[0;32mI (1281) spi_flash: flash io: opi_str[0m
[0;32mI (1285) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (1291) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (1298) coexist: coex firmware version: e727207[0m
[0;32mI (1311) coexist: coexist rom version e7ae62f[0m
[0;32mI (1311) main_task: Started on CPU0[0m
[0;32mI (1321) esp_psram: Reserving pool of 32K of internal memory for DMA/internal allocations[0m
[0;32mI (1321) main_task: Calling app_main()[0m
[0;32mI (1341) BLE_INIT: BT controller compile version [d74042a][0m
[0;32mI (1341) BLE_INIT: Bluetooth MAC: b4:3a:45:f7:39:0a[0m
[0;32mI (1341) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (1371) uart: queue free spaces: 80[0m
[0;32mI (1381) FLASH: : 0[0m
[0;32mI (1381) FLASH: : LittleFS mounted successfully at /storage[0m
[0;32mI (1381) HEAP: Free heap: 16688140[0m
[0;32mI (1381) FILE_HANDLE: : TASK CREATED[0m
[0;32mI (1381) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (1381) UART: uart[1] event[0m
[0;32mI (1391) NimBLE: Device Address: [0m
[0;32mI (1391) UART: uart rx break[0m
[0;32mI (1391) NimBLE: b4:3a:45:f7:39:0a[0m
[0;32mI (1401) NimBLE: [0m

[0;32mI (1391) UART: uart[1] event[0m
[0;32mI (1391) gpio: GPIO[10]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (1401) UART: uart rx break[0m
[0;32mI (1411) UART: uart[1] event[0m
[0;32mI (1411) UART: uart rx break[0m
[0;32mI (1421) UART: uart[1] event[0m
[0;32mI (1421) UART: uart rx break[0m
[0;32mI (1421) UART: uart[1] event[0m
[0;32mI (1421) UART: uart rx break[0m
[0;32mI (1431) UART: uart[1] event[0m
[0;32mI (1431) UART: uart rx break[0m
[0;32mI (1431) UART: uart[1] event[0m
[0;32mI (1441) UART: uart rx break[0m
[0;32mI (1441) UART: uart[1] event[0m
[0;32mI (1441) UART: uart rx break[0m
[0;32mI (1441) UART: uart[1] event[0m
[0;32mI (1451) UART: uart rx break[0m
[0;32mI (1451) UART: uart[1] event[0m
[0;32mI (1451) UART: uart rx break[0m
[0;32mI (1451) UART: uart[1] event[0m
[0;32mI (1461) UART: uart rx break[0m
[0;32mI (1461) UART: uart[1] event[0m
[0;32mI (1461) UART: uart rx break[0m
[0;32mI (1461) UART: uart[1] event[0m
[0;32mI (1471) UART: uart rx break[0m
[0;32mI (1471) UART: uart[0] event[0m
[0;32mI (1471) UART_READ: : 0[0m
[0;32mI (1471) UART_READ: : UART_READ_START[0m
[0;32mI (1481) UART_READ: : ERROR[0m
[0;32mI (1481) UART READ: [LEN: ]: 28[0m
[0;32mI (1481) UART: uart[1] event[0m
[0;32mI (1491) UART: uart rx break[0m
[0;32mI (1491) UART: uart[1] event[0m
[0;32mI (1491) UART: uart rx break[0m
[0;32mI (1491) UART: uart[1] event[0m
[0;32mI (1501) UART: uart rx break[0m
[0;32mI (1501) UART: uart[1] event[0m
[0;32mI (1501) UART: uart rx break[0m
[0;32mI (1501) UART: uart[1] event[0m
[0;32mI (1511) UART: uart rx break[0m
[0;32mI (1511) UART: uart[1] event[0m
[0;32mI (1511) UART: uart rx break[0m
[0;32mI (1511) UART: uart[1] event[0m
[0;32mI (1521) UART: uart rx break[0m
[0;32mI (1521) UART: uart[1] event[0m
[0;32mI (1521) UART: uart rx break[0m
[0;32mI (1521) UART: uart[0] event[0m
[0;32mI (1531) UART_READ: : 2[0m
[0;32mI (1531) UART_READ: : : UART_READ_END[0m
[0;32mI (1531) UART READ: [LEN: ]: 53[0m
[0;32mI (1541) UART: uart[1] event[0m
[0;32mI (1541) UART: uart rx break[0m
[0;32mI (1541) UART: uart[1] event[0m
[0;32mI (1541) UART: uart rx break[0m
[0;32mI (1551) UART: uart[1] event[0m
[0;32mI (1551) UART: uart rx break[0m
[0;32mI (1551) UART: uart[1] event[0m
[0;32mI (1551) UART: uart rx break[0m
[0;32mI (1561) UART: uart[1] event[0m
[0;32mI (1561) UART: uart rx break[0m
[0;32mI (1561) UART: uart[1] event[0m
[0;32mI (1561) UART: uart rx break[0m
[0;32mI (1571) UART: uart[1] event[0m
[0;32mI (1571) UART: uart rx break[0m
[0;32mI (1571) UART: uart[0] event[0m
[0;32mI (1571) UART_READ: : 2[0m
[0;32mI (1581) UART_READ: : : UART_READ_END[0m
[0;32mI (1581) UART READ: [LEN: ]: 74[0m
[0;32mI (1581) UART: uart[1] event[0m
[0;32mI (1591) UART: uart rx break[0m
[0;32mI (1591) UART: uart[1] event[0m
[0;32mI (1591) UART: uart rx break[0m
[0;32mI (1591) UART: uart[1] event[0m
[0;32mI (1601) UART: uart rx break[0m
[0;32mI (1601) UART: uart[1] event[0m
[0;32mI (1601) UART: uart rx break[0m
[0;32mI (1601) UART: uart[1] event[0m
[0;32mI (1611) UART: uart rx break[0m
[0;32mI (1611) UART: uart[1] event[0m
[0;32mI (1611) UART: uart rx break[0m
[0;32mI (1611) UART: uart[1] event[0m
[0;32mI (1621) UART: uart rx break[0m
[0;32mI (1621) UART: uart[1] event[0m
[0;32mI (1621) UART: uart rx break[0m
[0;32mI (1621) UART: uart[0] event[0m
[0;32mI (1631) UART_READ: : 2[0m
[0;32mI (1631) UART_READ: : : UART_READ_END[0m
[0;32mI (1631) UART READ: [LEN: ]: 94[0m
[0;32mI (1641) UART: uart[1] event[0m
[0;32mI (1641) UART: uart rx break[0m
[0;32mI (1641) UART: uart[1] event[0m
[0;32mI (1641) UART: uart rx break[0m
[0;32mI (1651) UART: uart[1] event[0m
[0;32mI (1651) UART: uart rx break[0m
[0;32mI (1651) UART: uart[1] event[0m
[0;32mI (1651) UART: uart rx break[0m
[0;32mI (1661) UART: uart[1] event[0m
[0;32mI (1661) UART: uart rx break[0m
[0;32mI (1661) UART: uart[0] event[0m
[0;32mI (1661) UART_READ: : 2[0m
[0;32mI (1671) UART_READ: : : UART_READ_END[0m
[0;32mI (1671) UART READ: [LEN: ]: 108[0m
[0;32mI (1671) UART: uart[0] event[0m
[0;32mI (1681) UART_READ: : 2[0m
[0;32mI (1681) UART_READ: : : UART_READ_END[0m
[0;32mI (1681) UART READ: [LEN: ]: 109[0m
[0;32mI (1681) UART: uart[1] event[0m
[0;32mI (1691) UART: uart rx break[0m
[0;32mI (1691) UART: uart[1] event[0m
[0;32mI (1691) UART: uart rx break[0m
[0;32mI (1691) UART: uart[1] event[0m
[0;32mI (1701) UART: uart rx break[0m
[0;32mI (1701) UART: uart[1] event[0m
[0;32mI (1701) UART: uart rx break[0m
[0;32mI (1701) UART: uart[0] event[0m
[0;32mI (1711) UART_READ: : 2[0m
[0;32mI (1711) UART_READ: : : UART_READ_END[0m
[0;32mI (1711) UART READ: [LEN: ]: 126[0m
[0;32mI (1721) UART: uart[1] event[0m
[0;32mI (1721) UART: uart rx break[0m
[0;32mI (1721) UART: uart[1] event[0m
[0;32mI (1721) UART: uart rx break[0m
[0;32mI (1731) UART: uart[1] event[0m
[0;32mI (1731) UART: uart rx break[0m
[0;32mI (1731) UART: uart[1] event[0m
[0;32mI (1731) UART: uart rx break[0m
[0;32mI (1741) UART: uart[1] event[0m
[0;32mI (1741) UART: uart rx break[0m
[0;32mI (1741) UART: uart[1] event[0m
[0;32mI (1741) UART: uart rx break[0m
[0;32mI (1751) UART: uart[1] event[0m
[0;32mI (1751) UART: uart rx break[0m
[0;32mI (1751) UART: uart[0] event[0m
[0;32mI (1751) UART_READ: : 2[0m
[0;32mI (1761) UART_READ: : : UART_READ_END[0m
[0;32mI (1761) UART READ: [LEN: ]: 141[0m
[0;32mI (1761) UART: uart[1] event[0m
[0;32mI (1771) UART: uart rx break[0m
[0;32mI (1771) UART: uart[1] event[0m
[0;32mI (1771) UART: uart rx break[0m
[0;32mI (1771) UART: uart[1] event[0m
[0;32mI (1781) UART: uart rx break[0m
[0;32mI (1781) UART: uart[1] event[0m
[0;32mI (1781) UART: uart rx break[0m
[0;32mI (1781) UART: uart[1] event[0m
[0;32mI (1791) UART: uart rx break[0m
[0;32mI (1791) UART: uart[1] event[0m
[0;32mI (1791) UART: uart rx break[0m
[0;32mI (1791) UART: uart[1] event[0m
[0;32mI (1801) UART: uart rx break[0m
[0;32mI (1801) UART: uart[0] event[0m
[0;32mI (1801) UART_READ: : 2[0m
[0;32mI (1801) UART_READ: : : UART_READ_END[0m
[0;32mI (1811) UART READ: [LEN: ]: 157[0m
[0;32mI (1811) UART: uart[1] event[0m
[0;32mI (1811) UART: uart rx break[0m
[0;32mI (1821) UART: uart[1] event[0m
[0;32mI (1821) UART: uart rx break[0m
[0;32mI (1821) UART: uart[1] event[0m
[0;32mI (1821) UART: uart rx break[0m
[0;32mI (1831) UART: uart[1] event[0m
[0;32mI (1831) UART: uart rx break[0m
[0;32mI (1831) UART: uart[1] event[0m
[0;32mI (1831) UART: uart rx break[0m
[0;32mI (1841) UART: uart[0] event[0m
[0;32mI (1841) UART_READ: : 2[0m
[0;32mI (1841) UART_READ: : : UART_READ_END[0m
[0;32mI (1841) UART READ: [LEN: ]: 172[0m
[0;32mI (1851) UART: uart[1] event[0m
[0;32mI (1851) UART: uart rx break[0m
[0;32mI (1851) UART: uart[1] event[0m
[0;32mI (1861) UART: uart rx break[0m
[0;32mI (1861) UART: uart[1] event[0m
[0;32mI (1861) UART: uart rx break[0m
[0;32mI (1861) UART: uart[0] event[0m
[0;32mI (1871) UART_READ: : 2[0m
[0;32mI (1871) UART_READ: : : UART_READ_END[0m
[0;32mI (1871) UART READ: [LEN: ]: 186[0m
[0;32mI (1871) UART: uart[1] event[0m
[0;32mI (1881) UART: uart rx break[0m
[0;32mI (1881) UART: uart[1] event[0m
[0;32mI (1881) UART: uart rx break[0m
[0;32mI (1881) UART: uart[1] event[0m
[0;32mI (1891) UART: uart rx break[0m
[0;32mI (1891) UART: uart[1] event[0m
[0;32mI (1891) UART: uart rx break[0m
[0;32mI (1901) UART: uart[1] event[0m
[0;32mI (1901) UART: uart rx break[0m
[0;32mI (1901) UART: uart[0] event[0m
[0;32mI (1901) UART_READ: : 2[0m
[0;32mI (1911) UART_READ: : : UART_READ_END[0m
[0;32mI (1911) UART READ: [LEN: ]: 198[0m
[0;32mI (1911) UART: uart[1] event[0m
[0;32mI (1911) UART: uart rx break[0m
[0;32mI (1921) UART: uart[1] event[0m
[0;32mI (1921) UART: uart rx break[0m
[0;32mI (1921) UART: uart[1] event[0m
[0;32mI (1921) UART: uart rx break[0m
[0;32mI (1931) UART: uart[1] event[0m
[0;32mI (1931) UART: uart rx break[0m
[0;32mI (1931) UART: uart[1] event[0m
[0;32mI (1941) UART: uart rx break[0m
[0;32mI (1941) UART: uart[1] event[0m
[0;32mI (1941) UART: uart rx break[0m
[0;32mI (1941) UART: uart[1] event[0m
[0;32mI (1951) UART: uart rx break[0m
[0;32mI (1951) UART: uart[0] event[0m
[0;32mI (1951) UART_READ: : 2[0m
[0;32mI (1951) UART_READ: : : UART_READ_END[0m
[0;32mI (1961) UART READ: [LEN: ]: 215[0m
[0;32mI (1961) UART: uart[1] event[0m
[0;32mI (1961) UART: uart rx break[0m
[0;32mI (1961) UART: uart[1] event[0m
[0;32mI (1971) UART: uart rx break[0m
[0;32mI (1971) UART: uart[1] event[0m
[0;32mI (1971) UART: uart rx break[0m
[0;32mI (1971) UART: uart[1] event[0m
[0;32mI (1981) UART: uart rx break[0m
[0;32mI (1981) UART: uart[1] event[0m
[0;32mI (1981) UART: uart rx break[0m
[0;32mI (1991) UART: uart[0] event[0m
[0;32mI (1991) UART_READ: : 2[0m
[0;32mI (1991) UART_READ: : : UART_READ_END[0m
[0;32mI (1991) UART READ: [LEN: ]: 228[0m
[0;32mI (2001) UART: uart[1] event[0m
[0;32mI (2001) UART: uart rx break[0m
[0;32mI (2001) UART: uart[1] event[0m
[0;32mI (2001) UART: uart rx break[0m
[0;32mI (2011) UART: uart[1] event[0m
[0;32mI (2011) UART: uart rx break[0m
[0;32mI (2011) UART: uart[1] event[0m
[0;32mI (2011) UART: uart rx break[0m
[0;32mI (2021) UART: uart[1] event[0m
[0;32mI (2021) UART: uart rx break[0m
[0;32mI (2021) UART: uart[1] event[0m
[0;32mI (2031) UART: uart rx break[0m
[0;32mI (2031) UART: uart[1] event[0m
[0;32mI (2031) UART: uart rx break[0m
[0;32mI (2031) UART: uart[1] event[0m
[0;32mI (2041) UART: uart rx break[0m
[0;32mI (2041) UART: uart[1] event[0m
[0;32mI (2041) UART: uart rx break[0m
[0;32mI (2041) UART: uart[0] event[0m
[0;32mI (2051) UART_READ: : 2[0m
[0;32mI (2051) UART_READ: : : UART_READ_END[0m
[0;32mI (2051) UART READ: [LEN: ]: 252[0m
[0;32mI (2051) UART: uart[1] event[0m
[0;32mI (2061) UART: uart rx break[0m
[0;32mI (2061) UART: uart[1] event[0m
[0;32mI (2061) UART: uart rx break[0m
[0;32mI (2061) UART: uart[1] event[0m
[0;32mI (2071) UART: uart rx break[0m
[0;32mI (2071) UART: uart[1] event[0m
[0;32mI (2071) UART: uart rx break[0m
[0;32mI (2081) UART: uart[1] event[0m
[0;32mI (2081) UART: uart rx break[0m
[0;32mI (2081) UART: uart[1] event[0m
[0;32mI (2081) UART: uart rx break[0m
[0;32mI (2091) UART: uart[1] event[0m
[0;32mI (2091) UART: uart rx break[0m
[0;32mI (2091) UART: uart[1] event[0m
[0;32mI (2091) UART: uart rx break[0m
[0;32mI (2101) UART: uart[1] event[0m
[0;32mI (2101) UART: uart rx break[0m
[0;32mI (2101) UART: uart[1] event[0m
[0;32mI (2101) UART: uart rx break[0m
[0;32mI (2111) UART: uart[0] event[0m
[0;32mI (2111) UART_READ: : 2[0m
[0;32mI (2111) UART_READ: : : UART_READ_END[0m
[0;32mI (2111) UART READ: [LEN: ]: 271[0m
[0;32mI (2121) UART: uart[1] event[0m
[0;32mI (2121) UART: uart rx break[0m
[0;32mI (2121) UART: uart[1] event[0m
[0;32mI (2131) UART: uart rx break[0m
[0;32mI (2131) UART: uart[1] event[0m
[0;32mI (2131) UART: uart rx break[0m
[0;32mI (2131) UART: uart[1] event[0m
[0;32mI (2141) UART: uart rx break[0m
[0;32mI (2141) UART: uart[1] event[0m
[0;32mI (2141) UART: uart rx break[0m
[0;32mI (2141) UART: uart[1] event[0m
[0;32mI (2151) UART: uart rx break[0m
[0;32mI (2151) UART: uart[1] event[0m
[0;32mI (2151) UART: uart rx break[0m
[0;32mI (2151) UART: uart[1] event[0m
[0;32mI (2161) UART: uart rx break[0m
[0;32mI (2161) UART: uart[1] event[0m
[0;32mI (2161) UART: uart rx break[0m
[0;32mI (2161) UART: uart[0] event[0m
[0;32mI (2171) UART_READ: : 2[0m
[0;32mI (2171) UART_READ: : : UART_READ_END[0m
[0;32mI (2171) UART READ: [LEN: ]: 290[0m
[0;32mI (2181) UART: uart[1] event[0m
[0;32mI (2181) UART: uart rx break[0m
[0;32mI (2181) UART: uart[1] event[0m
[0;32mI (2181) UART: uart rx break[0m
[0;32mI (2191) UART: uart[1] event[0m
[0;32mI (2191) UART: uart rx break[0m
[0;32mI (2191) UART: uart[1] event[0m
[0;32mI (2191) UART: uart rx break[0m
[0;32mI (2201) UART: uart[1] event[0m
[0;32mI (2201) UART: uart rx break[0m
[0;32mI (2201) UART: uart[0] event[0m
[0;32mI (2201) UART_READ: : 2[0m
[0;32mI (2211) UART_READ: : : UART_READ_END[0m
[0;32mI (2211) UART READ: [LEN: ]: 303[0m
[0;32mI (2211) UART: uart[1] event[0m
[0;32mI (2221) UART: uart rx break[0m
[0;32mI (2221) UART: uart[1] event[0m
[0;32mI (2221) UART: uart rx break[0m
[0;32mI (2221) UART: uart[1] event[0m
[0;32mI (2231) UART: uart rx break[0m
[0;32mI (2231) UART: uart[0] event[0m
[0;32mI (2231) UART_READ: : 2[0m
[0;32mI (2231) UART_READ: : : UART_READ_END[0m
[0;32mI (2241) UART READ: [LEN: ]: 318[0m
[0;32mI (2241) UART: uart[1] event[0m
[0;32mI (2241) UART: uart rx break[0m
[0;32mI (2241) UART: uart[1] event[0m
[0;32mI (2251) UART: uart rx break[0m
[0;32mI (2251) UART: uart[0] event[0m
[0;32mI (2251) UART_READ: : 2[0m
[0;32mI (2251) UART_READ: : : UART_READ_END[0m
[0;32mI (2261) UART READ: [LEN: ]: 333[0m
[0;32mI (2261) UART: uart[1] event[0m
[0;32mI (2261) UART: uart rx break[0m
[0;32mI (2271) UART: uart[1] event[0m
[0;32mI (2271) UART: uart rx break[0m
[0;32mI (2271) UART: uart[1] event[0m
[0;32mI (2271) UART: uart rx break[0m
[0;32mI (2281) UART: uart[0] event[0m
[0;32mI (2281) UART_READ: : 2[0m
[0;32mI (2281) UART_READ: : : UART_READ_END[0m
[0;32mI (2281) UART READ: [LEN: ]: 354[0m
[0;32mI (2291) UART: uart[1] event[0m
[0;32mI (2291) UART: uart rx break[0m
[0;32mI (2291) UART: uart[1] event[0m
[0;32mI (2301) UART: uart rx break[0m
[0;32mI (2301) UART: uart[1] event[0m
[0;32mI (2301) UART: uart rx break[0m
[0;32mI (2301) UART: uart[1] event[0m
[0;32mI (2311) UART: uart rx break[0m
[0;32mI (2311) UART: uart[1] event[0m
[0;32mI (2311) UART: uart rx break[0m
[0;32mI (2311) UART: uart[1] event[0m
[0;32mI (2321) UART: uart rx break[0m
[0;32mI (2321) UART: uart[1] event[0m
[0;32mI (2321) UART: uart rx break[0m
[0;32mI (2321) UART: uart[1] event[0m
[0;32mI (2331) UART: uart rx break[0m
[0;32mI (2331) UART: uart[1] event[0m
[0;32mI (2331) UART: uart rx break[0m
[0;32mI (2331) UART: uart[1] event[0m
[0;32mI (2341) UART: uart rx break[0m
[0;32mI (2341) UART: uart[1] event[0m
[0;32mI (2341) UART: uart rx break[0m
[0;32mI (2341) UART: uart[1] event[0m
[0;32mI (2351) UART: uart rx break[0m
[0;32mI (2351) UART: uart[1] event[0m
[0;32mI (2351) UART: uart rx break[0m
[0;32mI (2361) UART: uart[1] event[0m
[0;32mI (2361) UART: uart rx break[0m
[0;32mI (2361) UART: uart[1] event[0m
[0;32mI (2361) UART: uart rx break[0m
[0;32mI (2371) UART: uart[1] event[0m
[0;32mI (2371) UART: uart rx break[0m
[0;32mI (2371) UART: uart[1] event[0m
[0;32mI (2371) UART: uart rx break[0m
[0;32mI (2381) UART: uart[1] event[0m
[0;32mI (2381) UART: uart rx break[0m
[0;32mI (2381) UART: uart[1] event[0m
[0;32mI (2381) UART: uart rx break[0m
[0;32mI (2391) UART: uart[1] event[0m
[0;32mI (2391) UART: uart rx break[0m
[0;32mI (2391) UART: uart[1] event[0m
[0;32mI (2391) UART: uart rx break[0m
[0;32mI (2401) UART: uart[1] event[0m
[0;32mI (2401) UART: uart rx break[0m
[0;32mI (2401) UART: uart[0] event[0m
[0;32mI (2401) UART_READ: : 2[0m
[0;32mI (2411) UART_READ: : : UART_READ_END[0m
[0;32mI (2411) UART READ: [LEN: ]: 355[0m
[0;32mI (2411) UART: uart[1] event[0m
[0;32mI (2421) UART: uart rx break[0m
[0;32mI (2421) UART: uart[1] event[0m
[0;32mI (2421) UART: uart rx break[0m
[0;32mI (2421) UART: uart[1] event[0m
[0;32mI (2431) UART: uart rx break[0m
[0;32mI (2431) UART: uart[1] event[0m
[0;32mI (2431) UART: uart rx break[0m
[0;32mI (2431) UART: uart[1] event[0m
[0;32mI (2441) UART: uart rx break[0m
[0;32mI (2441) UART: uart[1] event[0m
[0;32mI (2441) UART: uart rx break[0m
[0;32mI (2441) UART: uart[1] event[0m
[0;32mI (2451) UART: uart rx break[0m
[0;32mI (2451) UART: uart[1] event[0m
[0;32mI (2451) UART: uart rx break[0m
[0;32mI (2461) UART: uart[1] event[0m
[0;32mI (2461) UART: uart rx break[0m
[0;32mI (2461) UART: uart[1] event[0m
[0;32mI (2461) UART: uart rx break[0m
[0;32mI (2471) UART: uart[1] event[0m
[0;32mI (2471) UART: uart rx break[0m
[0;32mI (2471) UART: uart[1] event[0m
[0;32mI (2471) UART: uart rx break[0m
[0;32mI (2481) UART: uart[1] event[0m
[0;32mI (2481) UART: uart rx break[0m
[0;32mI (2481) UART: uart[1] event[0m
[0;32mI (2481) UART: uart rx break[0m
[0;32mI (2491) UART: uart[1] event[0m
[0;32mI (2491) UART: uart rx break[0m
[0;32mI (2491) UART: uart[1] event[0m
[0;32mI (2491) UART: uart rx break[0m
[0;32mI (2501) UART: uart[1] event[0m
[0;32mI (2501) UART: uart rx break[0m
[0;32mI (2501) UART: uart[1] event[0m
[0;32mI (2501) UART: uart rx break[0m
[0;32mI (2511) UART: uart[1] event[0m
[0;32mI (2511) UART: uart rx break[0m
[0;32mI (2511) UART: uart[1] event[0m
[0;32mI (2511) UART: uart rx break[0m
[0;32mI (2521) UART: uart[1] event[0m
[0;32mI (2521) UART: uart rx break[0m
[0;32mI (2521) UART: uart[1] event[0m
[0;32mI (2531) UART: uart rx break[0m
[0;32mI (2531) UART: uart[1] event[0m
[0;32mI (2531) UART: uart rx break[0m
[0;32mI (2531) UART: uart[1] event[0m
[0;32mI (2541) UART: uart rx break[0m
[0;32mI (2541) UART: uart[1] event[0m
[0;32mI (2541) UART: uart rx break[0m
[0;32mI (2541) UART: uart[1] event[0m
[0;32mI (2551) UART: uart rx break[0m
[0;32mI (2551) UART: uart[1] event[0m
[0;32mI (2551) UART: uart rx break[0m
[0;32mI (2551) UART: uart[1] event[0m
[0;32mI (2561) UART: uart rx break[0m
[0;32mI (2561) UART: uart[1] event[0m
[0;32mI (2561) UART: uart rx break[0m
[0;32mI (2561) UART: uart[1] event[0m
[0;32mI (2571) UART: uart rx break[0m
[0;32mI (2571) UART: uart[1] event[0m
[0;32mI (2571) UART: uart rx break[0m
[0;32mI (2571) UART: uart[1] event[0m
[0;32mI (2581) UART: uart rx break[0m
[0;32mI (2581) UART: uart[0] event[0m
[0;32mI (2581) UART_READ: : 2[0m
[0;32mI (2581) UART_READ: : : UART_READ_END[0m
[0;32mI (2591) UART READ: [LEN: ]: 380[0m
[0;32mI (2591) UART: uart[1] event[0m
[0;32mI (2591) UART: uart rx break[0m
[0;32mI (2601) UART: uart[1] event[0m
[0;32mI (2601) UART: uart rx break[0m
[0;32mI (2601) UART: uart[1] event[0m
[0;32mI (2601) UART: uart rx break[0m
[0;32mI (2611) UART: uart[1] event[0m
[0;32mI (2611) UART: uart rx break[0m
[0;32mI (2611) UART: uart[1] event[0m
[0;32mI (2611) UART: uart rx break[0m
[0;32mI (2621) UART: uart[1] event[0m
[0;32mI (2621) UART: uart rx break[0m
[0;32mI (2621) UART: uart[1] event[0m
[0;32mI (2621) UART: uart rx break[0m
[0;32mI (2631) UART: uart[1] event[0m
[0;32mI (2631) UART: uart rx break[0m
[0;32mI (2631) UART: uart[1] event[0m
[0;32mI (2641) UART: uart rx break[0m
[0;32mI (2641) UART: uart[1] event[0m
[0;32mI (2641) UART: uart rx break[0m
[0;32mI (2641) UART: uart[1] event[0m
[0;32mI (2651) UART: uart rx break[0m
[0;32mI (2651) UART: uart[1] event[0m
[0;32mI (2651) UART: uart rx break[0m
[0;32mI (2651) UART: uart[1] event[0m
[0;32mI (2661) UART: uart rx break[0m
[0;32mI (2661) UART: uart[1] event[0m
[0;32mI (2661) UART: uart rx break[0m
[0;32mI (2661) UART: uart[1] event[0m
[0;32mI (2671) UART: uart rx break[0m
[0;32mI (2671) UART: uart[1] event[0m
[0;32mI (2671) UART: uart rx break[0m
[0;32mI (2671) UART: uart[1] event[0m
[0;32mI (2681) UART: uart rx break[0m
[0;32mI (2681) UART: uart[1] event[0m
[0;32mI (2681) UART: uart rx break[0m
[0;32mI (2681) UART: uart[1] event[0m
[0;32mI (2691) UART: uart rx break[0m
[0;32mI (2691) UART: uart[1] event[0m
[0;32mI (2691) UART: uart rx break[0m
[0;32mI (2701) UART: uart[1] event[0m
[0;32mI (2701) UART: uart rx break[0m
[0;32mI (2701) UART: uart[1] event[0m
[0;32mI (2701) UART: uart rx break[0m
[0;32mI (2711) UART: uart[1] event[0m
[0;32mI (2711) UART: uart rx break[0m
[0;32mI (2711) UART: uart[1] event[0m
[0;32mI (2711) UART: uart rx break[0m
[0;32mI (2721) UART: uart[1] event[0m
[0;32mI (2721) UART: uart rx break[0m
[0;32mI (2721) UART: uart[0] event[0m
[0;32mI (2721) UART_READ: : 2[0m
[0;32mI (2731) UART_READ: : : UART_READ_END[0m
[0;32mI (2731) UART READ: [LEN: ]: 413[0m
[0;32mI (2731) UART: uart[1] event[0m
[0;32mI (2741) UART: uart rx break[0m
[0;32mI (2741) UART: uart[1] event[0m
[0;32mI (2741) UART: uart rx break[0m
[0;32mI (2741) UART: uart[1] event[0m
[0;32mI (2751) UART: uart rx break[0m
[0;32mI (2751) UART: uart[1] event[0m
[0;32mI (2751) UART: uart rx break[0m
[0;32mI (2751) UART: uart[1] event[0m
[0;32mI (2761) UART: uart rx break[0m
[0;32mI (2761) UART: uart[1] event[0m
[0;32mI (2761) UART: uart rx break[0m
[0;32mI (2761) UART: uart[1] event[0m
[0;32mI (2771) UART: uart rx break[0m
[0;32mI (2771) UART: uart[1] event[0m
[0;32mI (2771) UART: uart rx break[0m
[0;32mI (2771) UART: uart[1] event[0m
[0;32mI (2781) UART: uart rx break[0m
[0;32mI (2781) UART: uart[1] event[0m
[0;32mI (2781) UART: uart rx break[0m
[0;32mI (2781) UART: uart[1] event[0m
[0;32mI (2791) UART: uart rx break[0m
[0;32mI (2791) UART: uart[0] event[0m
[0;32mI (2791) UART_READ: : 2[0m
[0;32mI (2791) UART_READ: : : UART_READ_END[0m
[0;32mI (2801) UART READ: [LEN: ]: 414[0m
[0;32mI (2801) UART: uart[1] event[0m
[0;32mI (2801) UART: uart rx break[0m
[0;32mI (2811) UART: uart[1] event[0m
[0;32mI (2811) UART: uart rx break[0m
[0;32mI (2811) UART: uart[1] event[0m
[0;32mI (2811) UART: uart rx break[0m
[0;32mI (2821) UART: uart[1] event[0m
[0;32mI (2821) UART: uart rx break[0m
[0;32mI (2821) UART: uart[1] event[0m
[0;32mI (2821) UART: uart rx break[0m
[0;32mI (2831) UART: uart[1] event[0m
[0;32mI (2831) UART: uart rx break[0m
[0;32mI (2831) UART: uart[1] event[0m
[0;32mI (2831) UART: uart rx break[0m
[0;32mI (2841) UART: uart[1] event[0m
[0;32mI (2841) UART: uart rx break[0m
[0;32mI (2841) UART: uart[1] event[0m
[0;32mI (2851) UART: uart rx break[0m
[0;32mI (2851) UART: uart[1] event[0m
[0;32mI (2851) UART: uart rx break[0m
[0;32mI (2851) UART: uart[1] event[0m
[0;32mI (2861) UART: uart rx break[0m
[0;32mI (2861) UART: uart[1] event[0m
[0;32mI (2861) UART: uart rx break[0m
[0;32mI (2861) UART: uart[1] event[0m
[0;32mI (2871) UART: uart rx break[0m
[0;32mI (2871) UART: uart[0] event[0m
[0;32mI (2871) UART_READ: : 2[0m
[0;32mI (2871) UART_READ: : : UART_READ_END[0m
[0;32mI (2881) UART READ: [LEN: ]: 415[0m
[0;32mI (2881) UART: uart[1] event[0m
[0;32mI (2881) UART: uart rx break[0m
[0;32mI (2881) UART: uart[1] event[0m
[0;32mI (2891) UART: uart rx break[0m
[0;32mI (2891) UART: uart[1] event[0m
[0;32mI (2891) UART: uart rx break[0m
[0;32mI (2901) UART: uart[1] event[0m
[0;32mI (2901) UART: uart rx break[0m
[0;32mI (2901) UART: uart[1] event[0m
[0;32mI (2901) UART: uart rx break[0m
[0;32mI (2911) UART: uart[1] event[0m
[0;32mI (2911) UART: uart rx break[0m
[0;32mI (2911) UART: uart[1] event[0m
[0;32mI (2911) UART: uart rx break[0m
[0;32mI (2921) UART: uart[1] event[0m
[0;32mI (2921) UART: uart rx break[0m
[0;32mI (2921) UART: uart[1] event[0m
[0;32mI (2921) UART: uart rx break[0m
[0;32mI (2931) UART: uart[1] event[0m
[0;32mI (2931) UART: uart rx break[0m
[0;32mI (2931) UART: uart[1] event[0m
[0;32mI (2931) UART: uart rx break[0m
[0;32mI (2941) UART: uart[1] event[0m
[0;32mI (2941) UART: uart rx break[0m
[0;32mI (2941) UART: uart[1] event[0m
[0;32mI (2941) UART: uart rx break[0m
[0;32mI (2951) UART: uart[1] event[0m
[0;32mI (2951) UART: uart rx break[0m
[0;32mI (2951) gpio: GPIO[11]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (2951) UART: uart[1] event[0m
[0;32mI (2961) UART: uart rx break[0m
[0;32mI (2971) UART: uart[1] event[0m
[0;32mI (2971) UART: uart rx break[0m
[0;32mI (2971) UART: uart[1] event[0m
[0;32mI (2971) UART: uart rx break[0m
[0;32mI (2981) UART: uart[1] event[0m
[0;32mI (2981) UART: uart rx break[0m
[0;32mI (2981) UART: uart[1] event[0m
[0;32mI (2981) UART: uart rx break[0m
[0;32mI (2991) UART: uart[1] event[0m
[0;32mI (2991) UART: uart rx break[0m
[0;32mI (2991) UART: uart[1] event[0m
[0;32mI (2991) UART: uart rx break[0m
[0;32mI (3001) UART: uart[1] event[0m
[0;32mI (3001) UART: uart rx break[0m
[0;32mI (3001) UART: uart[1] event[0m
[0;32mI (3011) UART: uart rx break[0m
[0;32mI (3011) UART: uart[1] event[0m
[0;32mI (3011) UART: uart rx break[0m
[0;32mI (3011) UART: uart[1] event[0m
[0;32mI (3021) UART: uart rx break[0m
[0;32mI (3021) UART: uart[1] event[0m
[0;32mI (3021) UART: uart rx break[0m
[0;32mI (3021) UART: uart[1] event[0m
[0;32mI (3031) UART: uart rx break[0m
[0;32mI (3031) UART: uart[1] event[0m
[0;32mI (3031) UART: uart rx break[0m
[0;32mI (3031) UART: uart[1] event[0m
[0;32mI (3041) UART: uart rx break[0m
[0;32mI (3041) UART: uart[1] event[0m
[0;32mI (3041) UART: uart rx break[0m
[0;32mI (3041) UART: uart[1] event[0m
[0;32mI (3051) UART: uart rx break[0m
[0;32mI (3051) UART: uart[1] event[0m
[0;32mI (3051) UART: uart rx break[0m
[0;32mI (3051) UART: uart[1] event[0m
[0;32mI (3061) UART: uart rx break[0m
[0;32mI (3061) UART: uart[1] event[0m
[0;32mI (3061) UART: uart rx break[0m
[0;32mI (3071) UART: uart[1] event[0m
[0;32mI (3071) UART: uart rx break[0m
[0;32mI (3071) UART: uart[1] event[0m
[0;32mI (3071) UART: uart rx break[0m
[0;32mI (3081) UART: uart[1] event[0m
[0;32mI (3081) UART: uart rx break[0m
[0;32mI (3081) UART: uart[1] event[0m
[0;32mI (3081) UART: uart rx break[0m
[0;32mI (3091) UART: uart[1] event[0m
[0;32mI (3091) UART: uart rx break[0m
[0;32mI (3091) UART: uart[1] event[0m
[0;32mI (3091) UART: uart rx break[0m
[0;32mI (3101) UART: uart[1] event[0m
[0;32mI (3101) UART: uart rx break[0m
[0;32mI (3101) UART: uart[1] event[0m
[0;32mI (3101) UART: uart rx break[0m
[0;32mI (3111) UART: uart[1] event[0m
[0;32mI (3111) UART: uart rx break[0m
[0;32mI (3111) UART: uart[1] event[0m
[0;32mI (3111) UART: uart rx break[0m
[0;32mI (3121) UART: uart[1] event[0m
[0;32mI (3121) UART: uart rx break[0m
[0;32mI (3121) UART: uart[1] event[0m
[0;32mI (3131) UART: uart rx break[0m
[0;32mI (3131) UART: uart[1] event[0m
[0;32mI (3131) UART: uart rx break[0m
[0;32mI (3131) UART: uart[1] event[0m
[0;32mI (3141) UART: uart rx break[0m
[0;32mI (3141) UART: uart[1] event[0m
[0;32mI (3141) UART: uart rx break[0m
[0;32mI (3141) UART: uart[1] event[0m
[0;32mI (3151) UART: uart rx break[0m
[0;32mI (3151) UART: uart[1] event[0m
[0;32mI (3151) UART: uart rx break[0m
[0;32mI (3151) UART: uart[1] event[0m
[0;32mI (3161) UART: uart rx break[0m
[0;32mI (3161) UART: uart[1] event[0m
[0;32mI (3161) UART: uart rx break[0m
[0;32mI (3161) UART: uart[1] event[0m
[0;32mI (3171) UART: uart rx break[0m
[0;32mI (3171) UART: uart[1] event[0m
[0;32mI (3171) UART: uart rx break[0m
[0;32mI (3171) UART: uart[1] event[0m
[0;32mI (3181) UART: uart rx break[0m
[0;32mI (3181) UART: uart[1] event[0m
[0;32mI (3181) UART: uart rx break[0m
[0;32mI (3181) UART: uart[1] event[0m
[0;32mI (3191) UART: uart rx break[0m
[0;32mI (3191) UART: uart[1] event[0m
[0;32mI (3191) UART: uart rx break[0m
[0;32mI (3201) UART: uart[1] event[0m
[0;32mI (3201) UART: uart rx break[0m
[0;32mI (3201) UART: uart[1] event[0m
[0;32mI (3201) UART: uart rx break[0m
[0;32mI (3211) UART: uart[1] event[0m
[0;32mI (3211) UART: uart rx break[0m
[0;32mI (3211) UART: uart[1] event[0m
[0;32mI (3211) UART: uart rx break[0m
[0;32mI (3221) UART: uart[1] event[0m
[0;32mI (3221) UART: uart rx break[0m
[0;32mI (3221) UART: uart[1] event[0m
[0;32mI (3221) UART: uart rx break[0m
[0;32mI (3231) UART: uart[1] event[0m
[0;32mI (3231) UART: uart rx break[0m
[0;32mI (3231) UART: uart[1] event[0m
[0;32mI (3231) UART: uart rx break[0m
[0;32mI (3241) UART: uart[1] event[0m
[0;32mI (3241) UART: uart rx break[0m
[0;32mI (3241) UART: uart[1] event[0m
[0;32mI (3241) UART: uart rx break[0m
[0;32mI (3251) UART: uart[1] event[0m
[0;32mI (3251) UART: uart rx break[0m
[0;32mI (3251) UART: uart[1] event[0m
[0;32mI (3261) UART: uart rx break[0m
[0;32mI (3261) UART: uart[1] event[0m
[0;32mI (3261) UART: uart rx break[0m
[0;32mI (3261) UART: uart[1] event[0m
[0;32mI (3271) UART: uart rx break[0m
[0;32mI (3271) UART: uart[1] event[0m
[0;32mI (3271) UART: uart rx break[0m
[0;32mI (3271) UART: uart[1] event[0m
[0;32mI (3281) UART: uart rx break[0m
[0;32mI (3281) UART: uart[1] event[0m
[0;32mI (3281) UART: uart rx break[0m
[0;32mI (3281) UART: uart[1] event[0m
[0;32mI (3291) UART: uart rx break[0m
[0;32mI (3291) UART: uart[1] event[0m
[0;32mI (3291) UART: uart rx break[0m
[0;32mI (3291) UART: uart[1] event[0m
[0;32mI (3301) UART: uart rx break[0m
[0;32mI (3301) UART: uart[1] event[0m
[0;32mI (3301) UART: uart rx break[0m
[0;32mI (3301) UART: uart[1] event[0m
[0;32mI (3311) UART: uart rx break[0m
[0;32mI (3311) UART: uart[1] event[0m
[0;32mI (3311) UART: uart rx break[0m
[0;32mI (3321) UART: uart[1] event[0m
[0;32mI (3321) UART: uart rx break[0m
[0;32mI (3321) UART: uart[1] event[0m
[0;32mI (3321) UART: uart rx break[0m
[0;32mI (3331) UART: uart[1] event[0m
[0;32mI (3331) UART: uart rx break[0m
[0;32mI (3331) UART: uart[1] event[0m
[0;32mI (3331) UART: uart rx break[0m
[0;32mI (3341) UART: uart[1] event[0m
[0;32mI (3341) UART: uart rx break[0m
[0;32mI (3341) UART: uart[1] event[0m
[0;32mI (3341) UART: uart rx break[0m
[0;32mI (3351) UART: uart[0] event[0m
[0;32mI (3351) UART_READ: : 2[0m
[0;32mI (3351) UART_READ: : : UART_READ_END[0m
[0;32mI (3351) UART READ: [LEN: ]: 535[0m
[0;32mI (3361) UART: uart[1] event[0m
[0;32mI (3361) UART: uart rx break[0m
[0;32mI (3361) UART: uart[1] event[0m
[0;32mI (3371) UART: uart rx break[0m
[0;32mI (3371) UART: uart[1] event[0m
[0;32mI (3371) UART: uart rx break[0m
[0;32mI (3371) UART: uart[1] event[0m
[0;32mI (3381) UART: uart rx break[0m
[0;32mI (3381) UART: uart[1] event[0m
[0;32mI (3381) UART: uart rx break[0m
[0;32mI (3381) UART: uart[1] event[0m
[0;32mI (3391) UART: uart rx break[0m
[0;32mI (3391) UART: uart[1] event[0m
[0;32mI (3391) UART: uart rx break[0m
[0;32mI (3391) UART: uart[1] event[0m
[0;32mI (3401) UART: uart rx break[0m
[0;32mI (3401) UART: uart[1] event[0m
[0;32mI (3401) UART: uart rx break[0m
[0;32mI (3401) UART: uart[1] event[0m
[0;32mI (3411) UART: uart rx break[0m
[0;32mI (3411) UART: uart[1] event[0m
[0;32mI (3411) UART: uart rx break[0m
[0;32mI (3411) UART: uart[1] event[0m
[0;32mI (3421) UART: uart rx break[0m
[0;32mI (3421) UART: uart[1] event[0m
[0;32mI (3421) UART: uart rx break[0m
[0;32mI (3431) UART: uart[1] event[0m
[0;32mI (3431) UART: uart rx break[0m
[0;32mI (3431) UART: uart[1] event[0m
[0;32mI (3431) UART: uart rx break[0m
[0;32mI (3441) UART: uart[1] event[0m
[0;32mI (3441) UART: uart rx break[0m
[0;32mI (3441) UART: uart[1] event[0m
[0;32mI (3441) UART: uart rx break[0m
[0;32mI (3451) UART: uart[1] event[0m
[0;32mI (3451) UART: uart rx break[0m
[0;32mI (3451) UART: uart[1] event[0m
[0;32mI (3451) UART: uart rx break[0m
[0;32mI (3461) UART: uart[1] event[0m
[0;32mI (3461) UART: uart rx break[0m
[0;32mI (3461) UART: uart[1] event[0m
[0;32mI (3461) UART: uart rx break[0m
[0;32mI (3471) UART: uart[1] event[0m
[0;32mI (3471) UART: uart rx break[0m
[0;32mI (3471) UART: uart[1] event[0m
[0;32mI (3471) UART: uart rx break[0m
[0;32mI (3481) UART: uart[1] event[0m
[0;32mI (3481) UART: uart rx break[0m
[0;32mI (3481) UART: uart[1] event[0m
[0;32mI (3491) UART: uart rx break[0m
[0;32mI (3491) UART: uart[1] event[0m
[0;32mI (3491) UART: uart rx break[0m
[0;32mI (3491) UART: uart[1] event[0m
[0;32mI (3501) UART: uart rx break[0m
[0;32mI (3501) UART: uart[1] event[0m
[0;32mI (3501) UART: uart rx break[0m
[0;32mI (3501) UART: uart[1] event[0m
[0;32mI (3511) UART: uart rx break[0m
[0;32mI (3511) UART: uart[1] event[0m
[0;32mI (3511) UART: uart rx break[0m
[0;32mI (3511) UART: uart[1] event[0m
[0;32mI (3521) UART: uart rx break[0m
[0;32mI (3521) UART: uart[1] event[0m
[0;32mI (3521) UART: uart rx break[0m
[0;32mI (3521) UART: uart[1] event[0m
[0;32mI (3531) UART: uart rx break[0m
[0;32mI (3531) UART: uart[1] event[0m
[0;32mI (3531) UART: uart rx break[0m
[0;32mI (3531) UART: uart[1] event[0m
[0;32mI (3541) UART: uart rx break[0m
[0;32mI (3541) UART: uart[1] event[0m
[0;32mI (3541) UART: uart rx break[0m
[0;32mI (3541) UART: uart[1] event[0m
[0;32mI (3551) UART: uart rx break[0m
[0;32mI (3551) UART: uart[1] event[0m
[0;32mI (3551) UART: uart rx break[0m
[0;32mI (3561) UART: uart[1] event[0m
[0;32mI (3561) UART: uart rx break[0m
[0;32mI (3561) UART: uart[1] event[0m
[0;32mI (3561) UART: uart rx break[0m
[0;32mI (3571) UART: uart[1] event[0m
[0;32mI (3571) UART: uart rx break[0m
[0;32mI (3571) UART: uart[1] event[0m
[0;32mI (3571) UART: uart rx break[0m
[0;32mI (3581) UART: uart[0] event[0m
[0;32mI (3581) UART_READ: : 2[0m
[0;32mI (3581) UART_READ: : : UART_READ_END[0m
[0;32mI (3581) UART READ: [LEN: ]: 550[0m
[0;32mI (3591) UART: uart[1] event[0m
[0;32mI (3591) UART: uart rx break[0m
[0;32mI (3591) UART: uart[1] event[0m
[0;32mI (3601) UART: uart rx break[0m
[0;32mI (3601) UART: uart[1] event[0m
[0;32mI (3601) UART: uart rx break[0m
[0;32mI (3601) UART: uart[1] event[0m
[0;32mI (3611) UART: uart rx break[0m
[0;32mI (3611) UART: uart[1] event[0m
[0;32mI (3611) UART: uart rx break[0m
[0;32mI (3611) UART: uart[1] event[0m
[0;32mI (3621) UART: uart rx break[0m
[0;32mI (3621) UART: uart[1] event[0m
[0;32mI (3621) UART: uart rx break[0m
[0;32mI (3621) UART: uart[1] event[0m
[0;32mI (3631) UART: uart rx break[0m
[0;32mI (3631) UART: uart[1] event[0m
[0;32mI (3631) UART: uart rx break[0m
[0;32mI (3631) UART: uart[1] event[0m
[0;32mI (3641) UART: uart rx break[0m
[0;32mI (3641) UART: uart[1] event[0m
[0;32mI (3641) UART: uart rx break[0m
[0;32mI (3641) UART: uart[1] event[0m
[0;32mI (3651) UART: uart rx break[0m
[0;32mI (3651) UART: uart[1] event[0m
[0;32mI (3651) UART: uart rx break[0m
[0;32mI (3661) UART: uart[1] event[0m
[0;32mI (3661) UART: uart rx break[0m
[0;32mI (3661) UART: uart[1] event[0m
[0;32mI (3661) UART: uart rx break[0m
[0;32mI (3671) UART: uart[1] event[0m
[0;32mI (3671) UART: uart rx break[0m
[0;32mI (3671) UART: uart[1] event[0m
[0;32mI (3671) UART: uart rx break[0m
[0;32mI (3681) UART: uart[0] event[0m
[0;32mI (3681) UART_READ: : 2[0m
[0;32mI (3681) UART_READ: : : UART_READ_END[0m
[0;32mI (3681) UART READ: [LEN: ]: 577[0m
[0;32mI (3691) UART: uart[1] event[0m
[0;32mI (3691) UART: uart rx break[0m
[0;32mI (3691) UART: uart[1] event[0m
[0;32mI (3691) UART: uart rx break[0m
[0;32mI (3701) UART: uart[1] event[0m
[0;32mI (3701) UART: uart rx break[0m
[0;32mI (3701) UART: uart[0] event[0m
[0;32mI (3711) UART_READ: : 2[0m
[0;32mI (3711) UART_READ: : : UART_READ_END[0m
[0;32mI (3711) UART READ: [LEN: ]: 605[0m
[0;32mI (3711) UART: uart[1] event[0m
[0;32mI (3721) UART: uart rx break[0m
[0;32mI (3721) UART: uart[1] event[0m
[0;32mI (3721) UART: uart rx break[0m
[0;32mI (3721) UART: uart[1] event[0m
[0;32mI (3731) UART: uart rx break[0m
[0;32mI (3731) UART: uart[1] event[0m
[0;32mI (3731) UART: uart rx break[0m
[0;32mI (3731) UART: uart[1] event[0m
[0;32mI (3741) UART: uart rx break[0m
[0;32mI (3741) UART: uart[1] event[0m
[0;32mI (3741) UART: uart rx break[0m
[0;32mI (3741) UART: uart[1] event[0m
[0;32mI (3751) UART: uart rx break[0m
[0;32mI (3751) UART: uart[1] event[0m
[0;32mI (3751) UART: uart rx break[0m
[0;32mI (3761) UART: uart[1] event[0m
[0;32mI (3761) UART: uart rx break[0m
[0;32mI (3761) UART: uart[1] event[0m
[0;32mI (3761) UART: uart rx break[0m
[0;32mI (3771) UART: uart[1] event[0m
[0;32mI (3771) UART: uart rx break[0m
[0;32mI (3771) UART: uart[0] event[0m
[0;32mI (3771) UART_READ: : 2[0m
[0;32mI (3781) UART_READ: : : UART_READ_END[0m
[0;32mI (3781) UART READ: [LEN: ]: 621[0m
[0;32mI (3781) UART: uart[0] event[0m
[0;32mI (3781) UART_READ: : 2[0m
[0;32mI (3791) UART_READ: : : UART_READ_END[0m
[0;32mI (3791) UART READ: [LEN: ]: 622[0m
[0;32mI (3791) UART: uart[0] event[0m
[0;32mI (3801) UART_READ: : 2[0m
[0;32mI (3801) UART_READ: : : UART_READ_END[0m
[0;32mI (3801) UART READ: [LEN: ]: 632[0m
[0;32mI (3801) UART: uart[1] event[0m
[0;32mI (3811) UART: uart rx break[0m
[0;32mI (3811) UART: uart[1] event[0m
[0;32mI (3811) UART: uart rx break[0m
[0;32mI (3811) UART: uart[1] event[0m
[0;32mI (3821) UART: uart rx break[0m
[0;32mI (3821) UART: uart[1] event[0m
[0;32mI (3821) UART: uart rx break[0m
[0;32mI (3831) UART: uart[0] event[0m
[0;32mI (3831) UART_READ: : 2[0m
[0;32mI (3831) UART_READ: : : UART_READ_END[0m
[0;32mI (3831) UART READ: [LEN: ]: 640[0m
[0;32mI (3841) gpio: GPIO[13]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (3841) gpio: GPIO[14]| InputEn: 0| OutputEn: 0| OpenDrain: 0| Pullup: 1| Pulldown: 0| Intr:0 [0m
[0;32mI (3851) NimBLE_BLE_PRPH: BLE advertising state loaded from NVS: true[0m
[0;32mI (3861) MAIN: Resuming BLE advertising from NVS...[0m
[0;32mI (3861) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (3871) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (3871) NimBLE: disc_mode=2[0m
[0;32mI (3881) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (3891) NimBLE: [0m

[0;32mI (3891) main_task: Returned from app_main()[0m
[0;32mI (3931) UART: uart[0] event[0m
[0;32mI (3931) UART_READ: : 2[0m
[0;32mI (3931) UART_READ: : : UART_READ_END[0m
[0;32mI (3931) UART READ: [LEN: ]: 650[0m
[0;32mI (39291) UART: uart[0] event[0m
[0;32mI (39291) UART_READ: : 2[0m
[0;32mI (39291) UART_READ: : : UART_READ_END[0m
[0;32mI (39291) UART READ: [LEN: ]: 651[0m
[0;32mI (39301) UART: uart[1] event[0m
[0;32mI (39301) UART: uart rx break[0m
[0;32mI (39301) UART: uart[1] event[0m
[0;32mI (39301) UART: uart rx break[0m
[0;32mI (39311) UART: uart[1] event[0m
[0;32mI (39311) UART: uart rx break[0m
[0;32mI (39311) UART: uart[1] event[0m
[0;32mI (39311) UART: uart rx break[0m
[0;32mI (39311) UART: uart[1] event[0m
[0;32mI (39321) UART: uart rx break[0m
[0;32mI (39321) UART: uart[0] event[0m
[0;32mI (39321) UART_READ: : 2[0m
[0;32mI (39321) UART_READ: : : UART_READ_END[0m
[0;32mI (39331) UART READ: [LEN: ]: 662[0m
[0;32mI (39331) UART: uart[1] event[0m
[0;32mI (39331) UART: uart rx break[0m
[0;32mI (39341) UART: uart[1] event[0m
[0;32mI (39341) UART: uart rx break[0m
[0;32mI (39341) UART: uart[1] event[0m
[0;32mI (39341) UART: uart rx break[0m
[0;32mI (39351) UART: uart[1] event[0m
[0;32mI (39351) UART: uart rx break[0m
[0;32mI (39351) UART: uart[1] event[0m
[0;32mI (39361) UART: uart rx break[0m
[0;32mI (39361) UART: uart[1] event[0m
[0;32mI (39361) UART: uart rx break[0m
[0;32mI (39361) UART: uart[1] event[0m
[0;32mI (39371) UART: uart rx break[0m
[0;32mI (39371) UART: uart[1] event[0m
[0;32mI (39371) UART: uart rx break[0m
[0;32mI (39371) UART: uart[1] event[0m
[0;32mI (39381) UART: uart rx break[0m
[0;32mI (39381) UART: uart[0] event[0m
[0;32mI (39381) UART_READ: : 2[0m
[0;32mI (39381) UART_READ: : : UART_READ_END[0m
[0;32mI (39391) UART READ: [LEN: ]: 678[0m
[0;32mI (39391) UART: uart[1] event[0m
[0;32mI (39391) UART: uart rx break[0m
[0;32mI (39401) UART: uart[1] event[0m
[0;32mI (39401) UART: uart rx break[0m
[0;32mI (39401) UART: uart[1] event[0m
[0;32mI (39401) UART: uart rx break[0m
[0;32mI (39411) UART: uart[1] event[0m
[0;32mI (39411) UART: uart rx break[0m
[0;32mI (39411) UART: uart[1] event[0m
[0;32mI (39421) UART: uart rx break[0m
[0;32mI (39421) UART: uart[1] event[0m
[0;32mI (39421) UART: uart rx break[0m
[0;32mI (39421) UART: uart[1] event[0m
[0;32mI (39431) UART: uart rx break[0m
[0;32mI (39431) UART: uart[0] event[0m
[0;32mI (39431) UART_READ: : 2[0m
[0;32mI (39431) UART_READ: : : UART_READ_END[0m
[0;32mI (39441) UART READ: [LEN: ]: 691[0m
[0;32mI (39441) UART: uart[1] event[0m
[0;32mI (39441) UART: uart rx break[0m
[0;32mI (39451) UART: uart[1] event[0m
[0;32mI (39451) UART: uart rx break[0m
[0;32mI (39451) UART: uart[1] event[0m
[0;32mI (39451) UART: uart rx break[0m
[0;32mI (39461) UART: uart[1] event[0m
[0;32mI (39461) UART: uart rx break[0m
[0;32mI (39461) UART: uart[1] event[0m
[0;32mI (39461) UART: uart rx break[0m
[0;32mI (39471) UART: uart[1] event[0m
[0;32mI (39471) UART: uart rx break[0m
[0;32mI (39471) UART: uart[1] event[0m
[0;32mI (39481) UART: uart rx break[0m
[0;32mI (39481) UART: uart[1] event[0m
[0;32mI (39481) UART: uart rx break[0m
[0;32mI (39481) UART: uart[1] event[0m
[0;32mI (39491) UART: uart rx break[0m
[0;32mI (39491) UART: uart[0] event[0m
[0;32mI (39491) UART_READ: : 2[0m
[0;32mI (39491) UART_READ: : : UART_READ_END[0m
[0;32mI (39501) UART READ: [LEN: ]: 707[0m
[0;32mI (39501) UART: uart[1] event[0m
[0;32mI (39501) UART: uart rx break[0m
[0;32mI (39511) UART: uart[1] event[0m
[0;32mI (39511) UART: uart rx break[0m
[0;32mI (39511) UART: uart[1] event[0m
[0;32mI (39511) UART: uart rx break[0m
[0;32mI (39521) UART: uart[0] event[0m
[0;32mI (39521) UART_READ: : 2[0m
[0;32mI (39521) UART_READ: : : UART_READ_END[0m
[0;32mI (39521) UART READ: [LEN: ]: 719[0m
[0;32mI (39531) UART: uart[1] event[0m
[0;32mI (39531) UART: uart rx break[0m
[0;32mI (39531) UART: uart[1] event[0m
[0;32mI (39541) UART: uart rx break[0m
[0;32mI (39541) UART: uart[1] event[0m
[0;32mI (39541) UART: uart rx break[0m
[0;32mI (39541) UART: uart[1] event[0m
[0;32mI (39551) UART: uart rx break[0m
[0;32mI (39551) UART: uart[0] event[0m
[0;32mI (39551) UART_READ: : 2[0m
[0;32mI (39551) UART_READ: : : UART_READ_END[0m
[0;32mI (39561) UART READ: [LEN: ]: 727[0m
[0;32mI (39561) UART: uart[1] event[0m
[0;32mI (39561) UART: uart rx break[0m
[0;32mI (39571) UART: uart[1] event[0m
[0;32mI (39571) UART: uart rx break[0m
[0;32mI (39571) UART: uart[1] event[0m
[0;32mI (39571) UART: uart rx break[0m
[0;32mI (39581) UART: uart[1] event[0m
[0;32mI (39581) UART: uart rx break[0m
[0;32mI (39581) UART: uart[1] event[0m
[0;32mI (39581) UART: uart rx break[0m
[0;32mI (39591) UART: uart[0] event[0m
[0;32mI (39591) UART_READ: : 2[0m
[0;32mI (39591) UART_READ: : : UART_READ_END[0m
[0;32mI (39601) UART READ: [LEN: ]: 736[0m
[0;32mI (39601) UART: uart[1] event[0m
[0;32mI (39601) UART: uart rx break[0m
[0;32mI (39601) UART: uart[1] event[0m
[0;32mI (39611) UART: uart rx break[0m
[0;32mI (39611) UART: uart[1] event[0m
[0;32mI (39611) UART: uart rx break[0m
[0;32mI (39621) UART: uart[1] event[0m
[0;32mI (39621) UART: uart rx break[0m
[0;32mI (39621) UART: uart[1] event[0m
[0;32mI (39621) UART: uart rx break[0m
[0;32mI (39631) UART: uart[0] event[0m
[0;32mI (39631) UART_READ: : 2[0m
[0;32mI (39631) UART_READ: : : UART_READ_END[0m
[0;32mI (39631) UART READ: [LEN: ]: 749[0m
[0;32mI (39641) UART: uart[1] event[0m
[0;32mI (39641) UART: uart rx break[0m
[0;32mI (39641) UART: uart[1] event[0m
[0;32mI (39651) UART: uart rx break[0m
[0;32mI (39651) UART: uart[1] event[0m
[0;32mI (39651) UART: uart rx break[0m
[0;32mI (39651) UART: uart[1] event[0m
[0;32mI (39661) UART: uart rx break[0m
[0;32mI (39661) UART: uart[0] event[0m
[0;32mI (39661) UART_READ: : 2[0m
[0;32mI (39661) UART_READ: : : UART_READ_END[0m
[0;32mI (39671) UART READ: [LEN: ]: 761[0m
[0;32mI (39671) UART: uart[1] event[0m
[0;32mI (39671) UART: uart rx break[0m
[0;32mI (39681) UART: uart[1] event[0m
[0;32mI (39681) UART: uart rx break[0m
[0;32mI (39681) UART: uart[1] event[0m
[0;32mI (39681) UART: uart rx break[0m
[0;32mI (39691) UART: uart[1] event[0m
[0;32mI (39691) UART: uart rx break[0m
[0;32mI (39691) UART: uart[1] event[0m
[0;32mI (39691) UART: uart rx break[0m
[0;32mI (39701) UART: uart[1] event[0m
[0;32mI (39701) UART: uart rx break[0m
[0;32mI (39701) UART: uart[1] event[0m
[0;32mI (39711) UART: uart rx break[0m
[0;32mI (39711) UART: uart[0] event[0m
[0;32mI (39711) UART_READ: : 2[0m
[0;32mI (39711) UART_READ: : : UART_READ_END[0m
[0;32mI (39721) UART READ: [LEN: ]: 776[0m
[0;32mI (39721) UART: uart[1] event[0m
[0;32mI (39721) UART: uart rx break[0m
[0;32mI (39721) UART: uart[1] event[0m
[0;32mI (39731) UART: uart rx break[0m
[0;32mI (39731) UART: uart[1] event[0m
[0;32mI (39731) UART: uart rx break[0m
[0;32mI (39741) UART: uart[1] event[0m
[0;32mI (39741) UART: uart rx break[0m
[0;32mI (39741) UART: uart[1] event[0m
[0;32mI (39741) UART: uart rx break[0m
[0;32mI (39751) UART: uart[1] event[0m
[0;32mI (39751) UART: uart rx break[0m
[0;32mI (39751) UART: uart[0] event[0m
[0;32mI (39751) UART_READ: : 2[0m
[0;32mI (39761) UART_READ: : : UART_READ_END[0m
[0;32mI (39761) UART READ: [LEN: ]: 796[0m
[0;32mI (39761) UART: uart[1] event[0m
[0;32mI (39771) UART: uart rx break[0m
[0;32mI (39771) UART: uart[1] event[0m
[0;32mI (39771) UART: uart rx break[0m
[0;32mI (39771) UART: uart[1] event[0m
[0;32mI (39781) UART: uart rx break[0m
[0;32mI (39781) UART: uart[1] event[0m
[0;32mI (39781) UART: uart rx break[0m
[0;32mI (39781) UART: uart[1] event[0m
[0;32mI (39791) UART: uart rx break[0m
[0;32mI (39791) UART: uart[1] event[0m
[0;32mI (39791) UART: uart rx break[0m
[0;32mI (39801) UART: uart[1] event[0m
[0;32mI (39801) UART: uart rx break[0m
[0;32mI (39801) UART: uart[1] event[0m
[0;32mI (39801) UART: uart rx break[0m
[0;32mI (39811) UART: uart[1] event[0m
[0;32mI (39811) UART: uart rx break[0m
[0;32mI (39811) UART: uart[1] event[0m
[0;32mI (39811) UART: uart rx break[0m
[0;32mI (39821) UART: uart[0] event[0m
[0;32mI (39821) UART_READ: : 2[0m
[0;32mI (39821) UART_READ: : : UART_READ_END[0m
[0;32mI (39831) UART READ: [LEN: ]: 814[0m
[0;32mI (39831) UART: uart[1] event[0m
[0;32mI (39831) UART: uart rx break[0m
[0;32mI (39831) UART: uart[1] event[0m
[0;32mI (39841) UART: uart rx break[0m
[0;32mI (39841) UART: uart[1] event[0m
[0;32mI (39841) UART: uart rx break[0m
[0;32mI (39841) UART: uart[1] event[0m
[0;32mI (39851) UART: uart rx break[0m
[0;32mI (39851) UART: uart[1] event[0m
[0;32mI (39851) UART: uart rx break[0m
[0;32mI (39861) UART: uart[1] event[0m
[0;32mI (39861) UART: uart rx break[0m
[0;32mI (39861) UART: uart[1] event[0m
[0;32mI (39861) UART: uart rx break[0m
[0;32mI (39871) UART: uart[1] event[0m
[0;32mI (39871) UART: uart rx break[0m
[0;32mI (39871) UART: uart[1] event[0m
[0;32mI (39871) UART: uart rx break[0m
[0;32mI (39881) UART: uart[0] event[0m
[0;32mI (39881) UART_READ: : 2[0m
[0;32mI (39881) UART_READ: : : UART_READ_END[0m
[0;32mI (39891) UART READ: [LEN: ]: 832[0m
[0;32mI (39891) UART: uart[1] event[0m
[0;32mI (39891) UART: uart rx break[0m
[0;32mI (39891) UART: uart[1] event[0m
[0;32mI (39901) UART: uart rx break[0m
[0;32mI (39901) UART: uart[1] event[0m
[0;32mI (39901) UART: uart rx break[0m
[0;32mI (39901) UART: uart[1] event[0m
[0;32mI (39911) UART: uart rx break[0m
[0;32mI (39911) UART: uart[1] event[0m
[0;32mI (39911) UART: uart rx break[0m
[0;32mI (39921) UART: uart[1] event[0m
[0;32mI (39921) UART: uart rx break[0m
[0;32mI (39921) UART: uart[1] event[0m
[0;32mI (39921) UART: uart rx break[0m
[0;32mI (39931) UART: uart[0] event[0m
[0;32mI (39931) UART_READ: : 2[0m
[0;32mI (39931) UART_READ: : : UART_READ_END[0m
[0;32mI (39931) UART READ: [LEN: ]: 846[0m
[0;32mI (39941) UART: uart[1] event[0m
[0;32mI (39941) UART: uart rx break[0m
[0;32mI (39941) UART: uart[1] event[0m
[0;32mI (39951) UART: uart rx break[0m
[0;32mI (39951) UART: uart[1] event[0m
[0;32mI (39951) UART: uart rx break[0m
[0;32mI (39951) UART: uart[1] event[0m
[0;32mI (39961) UART: uart rx break[0m
[0;32mI (39961) UART: uart[1] event[0m
[0;32mI (39961) UART: uart rx break[0m
[0;32mI (39961) UART: uart[1] event[0m
[0;32mI (39971) UART: uart rx break[0m
[0;32mI (39971) UART: uart[1] event[0m
[0;32mI (39971) UART: uart rx break[0m
[0;32mI (39981) UART: uart[1] event[0m
[0;32mI (39981) UART: uart rx break[0m
[0;32mI (39981) UART: uart[0] event[0m
[0;32mI (39981) UART_READ: : 2[0m
[0;32mI (39991) UART_READ: : : UART_READ_END[0m
[0;32mI (39991) UART READ: [LEN: ]: 860[0m
[0;32mI (39991) UART: uart[1] event[0m
[0;32mI (39991) UART: uart rx break[0m
[0;32mI (40001) UART: uart[1] event[0m
[0;32mI (40001) UART: uart rx break[0m
[0;32mI (40001) UART: uart[1] event[0m
[0;32mI (40011) UART: uart rx break[0m
[0;32mI (40011) UART: uart[1] event[0m
[0;32mI (40011) UART: uart rx break[0m
[0;32mI (40011) UART: uart[1] event[0m
[0;32mI (40021) UART: uart rx break[0m
[0;32mI (40021) UART: uart[1] event[0m
[0;32mI (40021) UART: uart rx break[0m
[0;32mI (40021) UART: uart[0] event[0m
[0;32mI (40031) UART_READ: : 2[0m
[0;32mI (40031) UART_READ: : : UART_READ_END[0m
[0;32mI (40031) UART READ: [LEN: ]: 876[0m
[0;32mI (40041) UART: uart[1] event[0m
[0;32mI (40041) UART: uart rx break[0m
[0;32mI (40041) UART: uart[1] event[0m
[0;32mI (40041) UART: uart rx break[0m
[0;32mI (40051) UART: uart[1] event[0m
[0;32mI (40051) UART: uart rx break[0m
[0;32mI (40051) UART: uart[1] event[0m
[0;32mI (40051) UART: uart rx break[0m
[0;32mI (40061) UART: uart[1] event[0m
[0;32mI (40061) UART: uart rx break[0m
[0;32mI (40061) UART: uart[0] event[0m
[0;32mI (40071) UART_READ: : 2[0m
[0;32mI (40071) UART_READ: : : UART_READ_END[0m
[0;32mI (40071) UART READ: [LEN: ]: 890[0m
[0;32mI (40071) UART: uart[1] event[0m
[0;32mI (40081) UART: uart rx break[0m
[0;32mI (40081) UART: uart[1] event[0m
[0;32mI (40081) UART: uart rx break[0m
[0;32mI (40081) UART: uart[1] event[0m
[0;32mI (40091) UART: uart rx break[0m
[0;32mI (40091) UART: uart[1] event[0m
[0;32mI (40091) UART: uart rx break[0m
[0;32mI (40101) UART: uart[1] event[0m
[0;32mI (40101) UART: uart rx break[0m
[0;32mI (40101) UART: uart[1] event[0m
[0;32mI (40101) UART: uart rx break[0m
[0;32mI (40111) UART: uart[1] event[0m
[0;32mI (40111) UART: uart rx break[0m
[0;32mI (40111) UART: uart[1] event[0m
[0;32mI (40111) UART: uart rx break[0m
[0;32mI (40121) UART: uart[1] event[0m
[0;32mI (40121) UART: uart rx break[0m
[0;32mI (40121) UART: uart[0] event[0m
[0;32mI (40131) UART_READ: : 2[0m
[0;32mI (40131) UART_READ: : : UART_READ_END[0m
[0;32mI (40131) UART READ: [LEN: ]: 911[0m
[0;32mI (40131) UART: uart[1] event[0m
[0;32mI (40141) UART: uart rx break[0m
[0;32mI (40141) UART: uart[1] event[0m
[0;32mI (40141) UART: uart rx break[0m
[0;32mI (40141) UART: uart[1] event[0m
[0;32mI (40151) UART: uart rx break[0m
[0;32mI (40151) UART: uart[1] event[0m
[0;32mI (40151) UART: uart rx break[0m
[0;32mI (40161) UART: uart[1] event[0m
[0;32mI (40161) UART: uart rx break[0m
[0;32mI (40161) UART: uart[1] event[0m
[0;32mI (40161) UART: uart rx break[0m
[0;32mI (40171) UART: uart[1] event[0m
[0;32mI (40171) UART: uart rx break[0m
[0;32mI (40171) UART: uart[0] event[0m
[0;32mI (40171) UART_READ: : 2[0m
[0;32mI (40181) UART_READ: : : UART_READ_END[0m
[0;32mI (40181) UART READ: [LEN: ]: 923[0m
[0;32mI (40181) UART: uart[1] event[0m
[0;32mI (40191) UART: uart rx break[0m
[0;32mI (40191) UART: uart[1] event[0m
[0;32mI (40191) UART: uart rx break[0m
[0;32mI (40191) UART: uart[1] event[0m
[0;32mI (40201) UART: uart rx break[0m
[0;32mI (40201) UART: uart[1] event[0m
[0;32mI (40201) UART: uart rx break[0m
[0;32mI (40201) UART: uart[1] event[0m
[0;32mI (40211) UART: uart rx break[0m
[0;32mI (40211) UART: uart[1] event[0m
[0;32mI (40211) UART: uart rx break[0m
[0;32mI (40221) UART: uart[1] event[0m
[0;32mI (40221) UART: uart rx break[0m
[0;32mI (40221) UART: uart[1] event[0m
[0;32mI (40221) UART: uart rx break[0m
[0;32mI (40231) UART: uart[1] event[0m
[0;32mI (40231) UART: uart rx break[0m
[0;32mI (40231) UART: uart[1] event[0m
[0;32mI (40231) UART: uart rx break[0m
[0;32mI (40241) UART: uart[0] event[0m
[0;32mI (40241) UART_READ: : 2[0m
[0;32mI (40241) UART_READ: : : UART_READ_END[0m
[0;32mI (40251) UART READ: [LEN: ]: 941[0m
[0;32mI (40251) UART: uart[1] event[0m
[0;32mI (40251) UART: uart rx break[0m
[0;32mI (40251) UART: uart[1] event[0m
[0;32mI (40261) UART: uart rx break[0m
[0;32mI (40261) UART: uart[1] event[0m
[0;32mI (40261) UART: uart rx break[0m
[0;32mI (40261) UART: uart[1] event[0m
[0;32mI (40271) UART: uart rx break[0m
[0;32mI (40271) UART: uart[1] event[0m
[0;32mI (40271) UART: uart rx break[0m
[0;32mI (40281) UART: uart[1] event[0m
[0;32mI (40281) UART: uart rx break[0m
[0;32mI (40281) UART: uart[1] event[0m
[0;32mI (40281) UART: uart rx break[0m
[0;32mI (40291) UART: uart[1] event[0m
[0;32mI (40291) UART: uart rx break[0m
[0;32mI (40291) UART: uart[1] event[0m
[0;32mI (40291) UART: uart rx break[0m
[0;32mI (40301) UART: uart[1] event[0m
[0;32mI (40301) UART: uart rx break[0m
[0;32mI (40301) UART: uart[1] event[0m
[0;32mI (40301) UART: uart rx break[0m
[0;32mI (40311) UART: uart[1] event[0m
[0;32mI (40311) UART: uart rx break[0m
[0;32mI (40311) UART: uart[1] event[0m
[0;32mI (40321) UART: uart rx break[0m
[0;32mI (40321) UART: uart[1] event[0m
[0;32mI (40321) UART: uart rx break[0m
[0;32mI (40321) UART: uart[1] event[0m
[0;32mI (40331) UART: uart rx break[0m
[0;32mI (40331) UART: uart[1] event[0m
[0;32mI (40331) UART: uart rx break[0m
[0;32mI (40331) UART: uart[1] event[0m
[0;32mI (40341) UART: uart rx break[0m
[0;32mI (40341) UART: uart[1] event[0m
[0;32mI (40341) UART: uart rx break[0m
[0;32mI (40351) UART: uart[1] event[0m
[0;32mI (40351) UART: uart rx break[0m
[0;32mI (40351) UART: uart[1] event[0m
[0;32mI (40351) UART: uart rx break[0m
[0;32mI (40361) UART: uart[1] event[0m
[0;32mI (40361) UART: uart rx break[0m
[0;32mI (40361) UART: uart[1] event[0m
[0;32mI (40361) UART: uart rx break[0m
[0;32mI (40371) UART: uart[1] event[0m
[0;32mI (40371) UART: uart rx break[0m
[0;32mI (40371) UART: uart[1] event[0m
[0;32mI (40371) UART: uart rx break[0m
[0;32mI (40381) UART: uart[1] event[0m
[0;32mI (40381) UART: uart rx break[0m
[0;32mI (40381) UART: uart[1] event[0m
[0;32mI (40391) UART: uart rx break[0m
[0;32mI (40391) UART: uart[1] event[0m
[0;32mI (40391) UART: uart rx break[0m
[0;32mI (40391) UART: uart[1] event[0m
[0;32mI (40401) UART: uart rx break[0m
[0;32mI (40401) UART: uart[1] event[0m
[0;32mI (40401) UART: uart rx break[0m
[0;32mI (40401) UART: uart[1] event[0m
[0;32mI (40411) UART: uart rx break[0m
[0;32mI (40411) UART: uart[1] event[0m
[0;32mI (40411) UART: uart rx break[0m
[0;32mI (40421) UART: uart[1] event[0m
[0;32mI (40421) UART: uart rx break[0m
[0;32mI (40421) UART: uart[1] event[0m
[0;32mI (40421) UART: uart rx break[0m
[0;32mI (40431) UART: uart[1] event[0m
[0;32mI (40431) UART: uart rx break[0m
[0;32mI (40431) UART: uart[1] event[0m
[0;32mI (40431) UART: uart rx break[0m
[0;32mI (40441) UART: uart[1] event[0m
[0;32mI (40441) UART: uart rx break[0m
[0;32mI (40441) UART: uart[1] event[0m
[0;32mI (40441) UART: uart rx break[0m
[0;32mI (40451) UART: uart[1] event[0m
[0;32mI (40451) UART: uart rx break[0m
[0;32mI (40451) UART: uart[1] event[0m
[0;32mI (40461) UART: uart rx break[0m
[0;32mI (40461) UART: uart[1] event[0m
[0;32mI (40461) UART: uart rx break[0m
[0;32mI (40461) UART: uart[1] event[0m
[0;32mI (40471) UART: uart rx break[0m
[0;32mI (40471) UART: uart[1] event[0m
[0;32mI (40471) UART: uart rx break[0m
[0;32mI (40471) UART: uart[1] event[0m
[0;32mI (40481) UART: uart rx break[0m
[0;32mI (40481) UART: uart[1] event[0m
[0;32mI (40481) UART: uart rx break[0m
[0;32mI (40491) UART: uart[1] event[0m
[0;32mI (40491) UART: uart rx break[0m
[0;32mI (40491) UART: uart[1] event[0m
[0;32mI (40491) UART: uart rx break[0m
[0;32mI (40501) UART: uart[1] event[0m
[0;32mI (40501) UART: uart rx break[0m
[0;32mI (40501) UART: uart[1] event[0m
[0;32mI (40501) UART: uart rx break[0m
[0;32mI (40511) UART: uart[1] event[0m
[0;32mI (40511) UART: uart rx break[0m
[0;32mI (40511) UART: uart[1] event[0m
[0;32mI (40511) UART: uart rx break[0m
[0;32mI (40521) UART: uart[1] event[0m
[0;32mI (40521) UART: uart rx break[0m
[0;32mI (40521) UART: uart[1] event[0m
[0;32mI (40531) UART: uart rx break[0m
[0;32mI (40531) UART: uart[1] event[0m
[0;32mI (40531) UART: uart rx break[0m
[0;32mI (40531) UART: uart[1] event[0m
[0;32mI (40541) UART: uart rx break[0m
[0;32mI (40541) UART: uart[1] event[0m
[0;32mI (40541) UART: uart rx break[0m
[0;32mI (40541) UART: uart[1] event[0m
[0;32mI (40551) UART: uart rx break[0m
[0;32mI (40551) UART: uart[1] event[0m
[0;32mI (40551) UART: uart rx break[0m
[0;32mI (40561) UART: uart[0] event[0m
[0;32mI (40561) UART_READ: : 2[0m
[0;32mI (40561) UART_READ: : : UART_READ_END[0m
[0;32mI (40561) UART READ: [LEN: ]: 1061[0m
[0;32mI (40571) UART: uart[0] event[0m
[0;32mI (40571) UART_READ: : 2[0m
[0;32mI (40571) UART_READ: : : UART_READ_END[0m
[0;32mI (40581) UART READ: [LEN: ]: 1063[0m
[0;32mI (40581) UART: uart[0] event[0m
[0;32mI (40581) UART_READ: : 2[0m
[0;32mI (40581) UART_READ: : : UART_READ_END[0m
[0;32mI (40591) UART READ: [LEN: ]: 1064[0m
[0;32mI (40591) UART: uart[1] event[0m
[0;32mI (40591) UART: uart rx break[0m
[0;32mI (40601) UART: uart[1] event[0m
[0;32mI (40601) UART: uart rx break[0m
[0;32mI (40601) UART: uart[1] event[0m
[0;32mI (40601) UART: uart rx break[0m
[0;32mI (40611) UART: uart[1] event[0m
[0;32mI (40611) UART: uart rx break[0m
[0;32mI (40611) UART: uart[1] event[0m
[0;32mI (40611) UART: uart rx break[0m
[0;32mI (40621) UART: uart[1] event[0m
[0;32mI (40621) UART: uart rx break[0m
[0;32mI (40621) UART: uart[1] event[0m
[0;32mI (40621) UART: uart rx break[0m
[0;32mI (40631) UART: uart[1] event[0m
[0;32mI (40631) UART: uart rx break[0m
[0;32mI (40631) UART: uart[1] event[0m
[0;32mI (40641) UART: uart rx break[0m
[0;32mI (40641) UART: uart[1] event[0m
[0;32mI (40641) UART: uart rx break[0m
[0;32mI (40641) UART: uart[1] event[0m
[0;32mI (40651) UART: uart rx break[0m
[0;32mI (40651) UART: uart[1] event[0m
[0;32mI (40651) UART: uart rx break[0m
[0;32mI (40651) UART: uart[1] event[0m
[0;32mI (40661) UART: uart rx break[0m
[0;32mI (40661) UART: uart[1] event[0m
[0;32mI (40661) UART: uart rx break[0m
[0;32mI (40671) UART: uart[1] event[0m
[0;32mI (40671) UART: uart rx break[0m
[0;32mI (40671) UART: uart[1] event[0m
[0;32mI (40671) UART: uart rx break[0m
[0;32mI (40681) UART: uart[1] event[0m
[0;32mI (40681) UART: uart rx break[0m
[0;32mI (40681) UART: uart[1] event[0m
[0;32mI (40681) UART: uart rx break[0m
[0;32mI (40691) UART: uart[1] event[0m
[0;32mI (40691) UART: uart rx break[0m
[0;32mI (40691) UART: uart[1] event[0m
[0;32mI (40691) UART: uart rx break[0m
[0;32mI (40701) UART: uart[1] event[0m
[0;32mI (40701) UART: uart rx break[0m
[0;32mI (40701) UART: uart[1] event[0m
[0;32mI (40711) UART: uart rx break[0m
[0;32mI (40711) UART: uart[1] event[0m
[0;32mI (40711) UART: uart rx break[0m
[0;32mI (40711) UART: uart[1] event[0m
[0;32mI (40721) UART: uart rx break[0m
[0;32mI (40721) UART: uart[1] event[0m
[0;32mI (40721) UART: uart rx break[0m
[0;32mI (40721) UART: uart[0] event[0m
[0;32mI (40731) UART_READ: : 2[0m
[0;32mI (40731) UART_READ: : : UART_READ_END[0m
[0;32mI (40731) UART READ: [LEN: ]: 1109[0m
[0;32mI (40741) UART: uart[1] event[0m
[0;32mI (40741) UART: uart rx break[0m
[0;32mI (40741) UART: uart[1] event[0m
[0;32mI (40741) UART: uart rx break[0m
[0;32mI (40751) UART: uart[1] event[0m
[0;32mI (40751) UART: uart rx break[0m
[0;32mI (40751) UART: uart[1] event[0m
[0;32mI (40751) UART: uart rx break[0m
[0;32mI (40761) UART: uart[1] event[0m
[0;32mI (40761) UART: uart rx break[0m
[0;32mI (40761) UART: uart[1] event[0m
[0;32mI (40771) UART: uart rx break[0m
[0;32mI (40771) UART: uart[1] event[0m
[0;32mI (40771) UART: uart rx break[0m
[0;32mI (40771) UART: uart[1] event[0m
[0;32mI (40781) UART: uart rx break[0m
[0;32mI (40781) UART: uart[1] event[0m
[0;32mI (40781) UART: uart rx break[0m
[0;32mI (40781) UART: uart[1] event[0m
[0;32mI (40791) UART: uart rx break[0m
[0;32mI (40791) UART: uart[1] event[0m
[0;32mI (40791) UART: uart rx break[0m
[0;32mI (40791) UART: uart[1] event[0m
[0;32mI (40801) UART: uart rx break[0m
[0;32mI (40801) UART: uart[1] event[0m
[0;32mI (40801) UART: uart rx break[0m
[0;32mI (40811) UART: uart[1] event[0m
[0;32mI (40811) UART: uart rx break[0m
[0;32mI (40811) UART: uart[0] event[0m
[0;32mI (40811) UART_READ: : 2[0m
[0;32mI (40821) UART_READ: : : UART_READ_END[0m
[0;32mI (40821) UART READ: [LEN: ]: 1111[0m
[0;32mI (40821) UART: uart[1] event[0m
[0;32mI (40831) UART: uart rx break[0m
[0;32mI (40831) UART: uart[1] event[0m
[0;32mI (40831) UART: uart rx break[0m
[0;32mI (40831) UART: uart[1] event[0m
[0;32mI (40841) UART: uart rx break[0m
[0;32mI (40841) UART: uart[1] event[0m
[0;32mI (40841) UART: uart rx break[0m
[0;32mI (40841) UART: uart[1] event[0m
[0;32mI (40851) UART: uart rx break[0m
[0;32mI (40851) UART: uart[1] event[0m
[0;32mI (40851) UART: uart rx break[0m
[0;32mI (40851) UART: uart[1] event[0m
[0;32mI (40861) UART: uart rx break[0m
[0;32mI (40861) UART: uart[1] event[0m
[0;32mI (40861) UART: uart rx break[0m
[0;32mI (40871) UART: uart[1] event[0m
[0;32mI (40871) UART: uart rx break[0m
[0;32mI (40871) UART: uart[1] event[0m
[0;32mI (40871) UART: uart rx break[0m
[0;32mI (40881) UART: uart[1] event[0m
[0;32mI (40881) UART: uart rx break[0m
[0;32mI (40881) UART: uart[1] event[0m
[0;32mI (40881) UART: uart rx break[0m
[0;32mI (40891) UART: uart[1] event[0m
[0;32mI (40891) UART: uart rx break[0m
[0;32mI (40891) UART: uart[1] event[0m
[0;32mI (40901) UART: uart rx break[0m
[0;32mI (40901) UART: uart[1] event[0m
[0;32mI (40901) UART: uart rx break[0m
[0;32mI (40901) UART: uart[1] event[0m
[0;32mI (40911) UART: uart rx break[0m
[0;32mI (40911) UART: uart[1] event[0m
[0;32mI (40911) UART: uart rx break[0m
[0;32mI (40911) UART: uart[1] event[0m
[0;32mI (40921) UART: uart rx break[0m
[0;32mI (40921) UART: uart[1] event[0m
[0;32mI (40921) UART: uart rx break[0m
[0;32mI (40921) UART: uart[1] event[0m
[0;32mI (40931) UART: uart rx break[0m
[0;32mI (40931) UART: uart[1] event[0m
[0;32mI (40931) UART: uart rx break[0m
[0;32mI (40941) UART: uart[0] event[0m
[0;32mI (40941) UART_READ: : 2[0m
[0;32mI (40941) UART_READ: : : UART_READ_END[0m
[0;32mI (40941) UART READ: [LEN: ]: 1149[0m
[0;32mI (40951) UART: uart[1] event[0m
[0;32mI (40951) UART: uart rx break[0m
[0;32mI (40951) UART: uart[1] event[0m
[0;32mI (40961) UART: uart rx break[0m
[0;32mI (40961) UART: uart[1] event[0m
[0;32mI (40961) UART: uart rx break[0m
[0;32mI (40961) UART: uart[1] event[0m
[0;32mI (40971) UART: uart rx break[0m
[0;32mI (40971) UART: uart[1] event[0m
[0;32mI (40971) UART: uart rx break[0m
[0;32mI (40971) UART: uart[1] event[0m
[0;32mI (40981) UART: uart rx break[0m
[0;32mI (40981) UART: uart[1] event[0m
[0;32mI (40981) UART: uart rx break[0m
[0;32mI (40981) UART: uart[1] event[0m
[0;32mI (40991) UART: uart rx break[0m
[0;32mI (40991) UART: uart[1] event[0m
[0;32mI (40991) UART: uart rx break[0m
[0;32mI (41001) UART: uart[1] event[0m
[0;32mI (41001) UART: uart rx break[0m
[0;32mI (41001) UART: uart[1] event[0m
[0;32mI (41001) UART: uart rx break[0m
[0;32mI (41011) UART: uart[1] event[0m
[0;32mI (41011) UART: uart rx break[0m
[0;32mI (41011) UART: uart[1] event[0m
[0;32mI (41011) UART: uart rx break[0m
[0;32mI (41021) UART: uart[1] event[0m
[0;32mI (41021) UART: uart rx break[0m
[0;32mI (41021) UART: uart[1] event[0m
[0;32mI (41031) UART: uart rx break[0m
[0;32mI (41031) UART: uart[1] event[0m
[0;32mI (41031) UART: uart rx break[0m
[0;32mI (41031) UART: uart[1] event[0m
[0;32mI (41041) UART: uart rx break[0m
[0;32mI (41041) UART: uart[1] event[0m
[0;32mI (41041) UART: uart rx break[0m
[0;32mI (41041) UART: uart[1] event[0m
[0;32mI (41051) UART: uart rx break[0m
[0;32mI (41051) UART: uart[1] event[0m
[0;32mI (41051) UART: uart rx break[0m
[0;32mI (41051) UART: uart[1] event[0m
[0;32mI (41061) UART: uart rx break[0m
[0;32mI (41061) UART: uart[1] event[0m
[0;32mI (41061) UART: uart rx break[0m
[0;32mI (41071) UART: uart[1] event[0m
[0;32mI (41071) UART: uart rx break[0m
[0;32mI (41071) UART: uart[1] event[0m
[0;32mI (41071) UART: uart rx break[0m
[0;32mI (41081) UART: uart[1] event[0m
[0;32mI (41081) UART: uart rx break[0m
[0;32mI (41081) UART: uart[1] event[0m
[0;32mI (41081) UART: uart rx break[0m
[0;32mI (41091) UART: uart[1] event[0m
[0;32mI (41091) UART: uart rx break[0m
[0;32mI (41091) UART: uart[1] event[0m
[0;32mI (41101) UART: uart rx break[0m
[0;32mI (41101) UART: uart[1] event[0m
[0;32mI (41101) UART: uart rx break[0m
[0;32mI (41101) UART: uart[1] event[0m
[0;32mI (41111) UART: uart rx break[0m
[0;32mI (41111) UART: uart[1] event[0m
[0;32mI (41111) UART: uart rx break[0m
[0;32mI (41111) UART: uart[1] event[0m
[0;32mI (41121) UART: uart rx break[0m
[0;32mI (41121) UART: uart[1] event[0m
[0;32mI (41121) UART: uart rx break[0m
[0;32mI (41121) UART: uart[1] event[0m
[0;32mI (41131) UART: uart rx break[0m
[0;32mI (41131) UART: uart[1] event[0m
[0;32mI (41131) UART: uart rx break[0m
[0;32mI (41141) UART: uart[1] event[0m
[0;32mI (41141) UART: uart rx break[0m
[0;32mI (41141) UART: uart[1] event[0m
[0;32mI (41141) UART: uart rx break[0m
[0;32mI (41151) UART: uart[1] event[0m
[0;32mI (41151) UART: uart rx break[0m
[0;32mI (41151) UART: uart[1] event[0m
[0;32mI (41151) UART: uart rx break[0m
[0;32mI (41161) UART: uart[1] event[0m
[0;32mI (41161) UART: uart rx break[0m
[0;32mI (41161) UART: uart[1] event[0m
[0;32mI (41171) UART: uart rx break[0m
[0;32mI (41171) UART: uart[1] event[0m
[0;32mI (41171) UART: uart rx break[0m
[0;32mI (41171) UART: uart[1] event[0m
[0;32mI (41181) UART: uart rx break[0m
[0;32mI (41181) UART: uart[1] event[0m
[0;32mI (41181) UART: uart rx break[0m
[0;32mI (41181) UART: uart[1] event[0m
[0;32mI (41191) UART: uart rx break[0m
[0;32mI (41191) UART: uart[1] event[0m
[0;32mI (41191) UART: uart rx break[0m
[0;32mI (41191) UART: uart[1] event[0m
[0;32mI (41201) UART: uart rx break[0m
[0;32mI (41201) UART: uart[1] event[0m
[0;32mI (41201) UART: uart rx break[0m
[0;32mI (41211) UART: uart[1] event[0m
[0;32mI (41211) UART: uart rx break[0m
[0;32mI (41211) UART: uart[1] event[0m
[0;32mI (41211) UART: uart rx break[0m
[0;32mI (41221) UART: uart[1] event[0m
[0;32mI (41221) UART: uart rx break[0m
[0;32mI (41221) UART: uart[1] event[0m
[0;32mI (41221) UART: uart rx break[0m
[0;32mI (41231) UART: uart[1] event[0m
[0;32mI (41231) UART: uart rx break[0m
[0;32mI (41231) UART: uart[1] event[0m
[0;32mI (41241) UART: uart rx break[0m
[0;32mI (41241) UART: uart[1] event[0m
[0;32mI (41241) UART: uart rx break[0m
[0;32mI (41241) UART: uart[1] event[0m
[0;32mI (41251) UART: uart rx break[0m
[0;32mI (41251) UART: uart[1] event[0m
[0;32mI (41251) UART: uart rx break[0m
[0;32mI (41251) UART: uart[1] event[0m
[0;32mI (41261) UART: uart rx break[0m
[0;32mI (41261) UART: uart[0] event[0m
[0;32mI (41261) UART_READ: : 2[0m
[0;32mI (41261) UART_READ: : : UART_READ_END[0m
[0;32mI (41271) UART READ: [LEN: ]: 1269[0m
[0;32mI (41271) UART: uart[1] event[0m
[0;32mI (41271) UART: uart rx break[0m
[0;32mI (41281) UART: uart[1] event[0m
[0;32mI (41281) UART: uart rx break[0m
[0;32mI (41281) UART: uart[1] event[0m
[0;32mI (41281) UART: uart rx break[0m
[0;32mI (41291) UART: uart[1] event[0m
[0;32mI (41291) UART: uart rx break[0m
[0;32mI (41291) UART: uart[1] event[0m
[0;32mI (41301) UART: uart rx break[0m
[0;32mI (41301) UART: uart[1] event[0m
[0;32mI (41301) UART: uart rx break[0m
[0;32mI (41301) UART: uart[1] event[0m
[0;32mI (41311) UART: uart rx break[0m
[0;32mI (41311) UART: uart[1] event[0m
[0;32mI (41311) UART: uart rx break[0m
[0;32mI (41311) UART: uart[1] event[0m
[0;32mI (41321) UART: uart rx break[0m
[0;32mI (41321) UART: uart[1] event[0m
[0;32mI (41321) UART: uart rx break[0m
[0;32mI (41321) UART: uart[1] event[0m
[0;32mI (41331) UART: uart rx break[0m
[0;32mI (41331) UART: uart[1] event[0m
[0;32mI (41331) UART: uart rx break[0m
[0;32mI (41341) UART: uart[1] event[0m
[0;32mI (41341) UART: uart rx break[0m
[0;32mI (41341) UART: uart[1] event[0m
[0;32mI (41341) UART: uart rx break[0m
[0;32mI (41351) UART: uart[1] event[0m
[0;32mI (41351) UART: uart rx break[0m
[0;32mI (41351) UART: uart[1] event[0m
[0;32mI (41351) UART: uart rx break[0m
[0;32mI (41361) UART: uart[1] event[0m
[0;32mI (41361) UART: uart rx break[0m
[0;32mI (41361) UART: uart[1] event[0m
[0;32mI (41371) UART: uart rx break[0m
[0;32mI (41371) UART: uart[1] event[0m
[0;32mI (41371) UART: uart rx break[0m
[0;32mI (41371) UART: uart[1] event[0m
[0;32mI (41381) UART: uart rx break[0m
[0;32mI (41381) UART: uart[1] event[0m
[0;32mI (41381) UART: uart rx break[0m
[0;32mI (41381) UART: uart[1] event[0m
[0;32mI (41391) UART: uart rx break[0m
[0;32mI (41391) UART: uart[1] event[0m
[0;32mI (41391) UART: uart rx break[0m
[0;32mI (41391) UART: uart[1] event[0m
[0;32mI (41401) UART: uart rx break[0m
[0;32mI (41401) UART: uart[1] event[0m
[0;32mI (41401) UART: uart rx break[0m
[0;32mI (41411) UART: uart[1] event[0m
[0;32mI (41411) UART: uart rx break[0m
[0;32mI (41411) UART: uart[1] event[0m
[0;32mI (41411) UART: uart rx break[0m
[0;32mI (41421) UART: uart[1] event[0m
[0;32mI (41421) UART: uart rx break[0m
[0;32mI (41421) UART: uart[1] event[0m
[0;32mI (41421) UART: uart rx break[0m
[0;32mI (41431) UART: uart[1] event[0m
[0;32mI (41431) UART: uart rx break[0m
[0;32mI (41431) UART: uart[0] event[0m
[0;32mI (41441) UART_READ: : 2[0m
[0;32mI (41441) UART_READ: : : UART_READ_END[0m
[0;32mI (41441) UART READ: [LEN: ]: 1329[0m
[0;32mI (41441) UART: uart[1] event[0m
[0;32mI (41451) UART: uart rx break[0m
[0;32mI (41451) UART: uart[1] event[0m
[0;32mI (41451) UART: uart rx break[0m
[0;32mI (41451) UART: uart[1] event[0m
[0;32mI (41461) UART: uart rx break[0m
[0;32mI (41461) UART: uart[1] event[0m
[0;32mI (41461) UART: uart rx break[0m
[0;32mI (41471) UART: uart[1] event[0m
[0;32mI (41471) UART: uart rx break[0m
[0;32mI (41471) UART: uart[1] event[0m
[0;32mI (41471) UART: uart rx break[0m
[0;32mI (41481) UART: uart[1] event[0m
[0;32mI (41481) UART: uart rx break[0m
[0;32mI (41481) UART: uart[1] event[0m
[0;32mI (41481) UART: uart rx break[0m
[0;32mI (41491) UART: uart[1] event[0m
[0;32mI (41491) UART: uart rx break[0m
[0;32mI (41491) UART: uart[1] event[0m
[0;32mI (41501) UART: uart rx break[0m
[0;32mI (41501) UART: uart[1] event[0m
[0;32mI (41501) UART: uart rx break[0m
[0;32mI (41501) UART: uart[1] event[0m
[0;32mI (41511) UART: uart rx break[0m
[0;32mI (41511) UART: uart[1] event[0m
[0;32mI (41511) UART: uart rx break[0m
[0;32mI (41511) UART: uart[1] event[0m
[0;32mI (41521) UART: uart rx break[0m
[0;32mI (41521) UART: uart[1] event[0m
[0;32mI (41521) UART: uart rx break[0m
[0;32mI (41521) UART: uart[1] event[0m
[0;32mI (41531) UART: uart rx break[0m
[0;32mI (41531) UART: uart[1] event[0m
[0;32mI (41531) UART: uart rx break[0m
[0;32mI (41541) UART: uart[1] event[0m
[0;32mI (41541) UART: uart rx break[0m
[0;32mI (41541) UART: uart[1] event[0m
[0;32mI (41541) UART: uart rx break[0m
[0;32mI (41551) UART: uart[1] event[0m
[0;32mI (41551) UART: uart rx break[0m
[0;32mI (41551) UART: uart[1] event[0m
[0;32mI (41551) UART: uart rx break[0m
[0;32mI (41561) UART: uart[1] event[0m
[0;32mI (41561) UART: uart rx break[0m
[0;32mI (41561) UART: uart[1] event[0m
[0;32mI (41561) UART: uart rx break[0m
[0;32mI (41571) UART: uart[1] event[0m
[0;32mI (41571) UART: uart rx break[0m
[0;32mI (41571) UART: uart[1] event[0m
[0;32mI (41581) UART: uart rx break[0m
[0;32mI (41581) UART: uart[1] event[0m
[0;32mI (41581) UART: uart rx break[0m
[0;32mI (41581) UART: uart[1] event[0m
[0;32mI (41591) UART: uart rx break[0m
[0;32mI (41591) UART: uart[1] event[0m
[0;32mI (41591) UART: uart rx break[0m
[0;32mI (41591) UART: uart[1] event[0m
[0;32mI (41601) UART: uart rx break[0m
[0;32mI (41601) UART: uart[1] event[0m
[0;32mI (41601) UART: uart rx break[0m
[0;32mI (41611) UART: uart[1] event[0m
[0;32mI (41611) UART: uart rx break[0m
[0;32mI (41611) UART: uart[1] event[0m
[0;32mI (41611) UART: uart rx break[0m
[0;32mI (41621) UART: uart[1] event[0m
[0;32mI (41621) UART: uart rx break[0m
[0;32mI (41621) UART: uart[1] event[0m
[0;32mI (41621) UART: uart rx break[0m
[0;32mI (41631) UART: uart[1] event[0m
[0;32mI (41631) UART: uart rx break[0m
[0;32mI (41631) UART: uart[1] event[0m
[0;32mI (41631) UART: uart rx break[0m
[0;32mI (41641) UART: uart[1] event[0m
[0;32mI (41641) UART: uart rx break[0m
[0;32mI (41641) UART: uart[1] event[0m
[0;32mI (41651) UART: uart rx break[0m
[0;32mI (41651) UART: uart[1] event[0m
[0;32mI (41651) UART: uart rx break[0m
[0;32mI (41651) UART: uart[1] event[0m
[0;32mI (41661) UART: uart rx break[0m
[0;32mI (41661) UART: uart[1] event[0m
[0;32mI (41661) UART: uart rx break[0m
[0;32mI (41661) UART: uart[1] event[0m
[0;32mI (41671) UART: uart rx break[0m
[0;32mI (41671) UART: uart[1] event[0m
[0;32mI (41671) UART: uart rx break[0m
[0;32mI (41681) UART: uart[1] event[0m
[0;32mI (41681) UART: uart rx break[0m
[0;32mI (41681) UART: uart[1] event[0m
[0;32mI (41681) UART: uart rx break[0m
[0;32mI (41691) UART: uart[1] event[0m
[0;32mI (41691) UART: uart rx break[0m
[0;32mI (41691) UART: uart[1] event[0m
[0;32mI (41691) UART: uart rx break[0m
[0;32mI (41701) UART: uart[1] event[0m
[0;32mI (41701) UART: uart rx break[0m
[0;32mI (41701) UART: uart[1] event[0m
[0;32mI (41701) UART: uart rx break[0m
[0;32mI (41711) UART: uart[1] event[0m
[0;32mI (41711) UART: uart rx break[0m
[0;32mI (41711) UART: uart[1] event[0m
[0;32mI (41721) UART: uart rx break[0m
[0;32mI (41721) UART: uart[1] event[0m
[0;32mI (41721) UART: uart rx break[0m
[0;32mI (41721) UART: uart[1] event[0m
[0;32mI (41731) UART: uart rx break[0m
[0;32mI (41731) UART: uart[1] event[0m
[0;32mI (41731) UART: uart rx break[0m
[0;32mI (41731) UART: uart[1] event[0m
[0;32mI (41741) UART: uart rx break[0m
[0;32mI (41741) UART: uart[1] event[0m
[0;32mI (41741) UART: uart rx break[0m
[0;32mI (41751) UART: uart[1] event[0m
[0;32mI (41751) UART: uart rx break[0m
[0;32mI (41751) UART: uart[1] event[0m
[0;32mI (41751) UART: uart rx break[0m
[0;32mI (41761) UART: uart[1] event[0m
[0;32mI (41761) UART: uart rx break[0m
[0;32mI (41761) UART: uart[1] event[0m
[0;32mI (41761) UART: uart rx break[0m
[0;32mI (41771) UART: uart[1] event[0m
[0;32mI (41771) UART: uart rx break[0m
[0;32mI (41771) UART: uart[0] event[0m
[0;32mI (41771) UART_READ: : 2[0m
[0;32mI (41781) UART_READ: : : UART_READ_END[0m
[0;32mI (41781) UART READ: [LEN: ]: 1449[0m
[0;32mI (41781) UART: uart[1] event[0m
[0;32mI (41791) UART: uart rx break[0m
[0;32mI (41791) UART: uart[1] event[0m
[0;32mI (41791) UART: uart rx break[0m
[0;32mI (41791) UART: uart[1] event[0m
[0;32mI (41801) UART: uart rx break[0m
[0;32mI (41801) UART: uart[1] event[0m
[0;32mI (41801) UART: uart rx break[0m
[0;32mI (41811) UART: uart[1] event[0m
[0;32mI (41811) UART: uart rx break[0m
[0;32mI (41811) UART: uart[1] event[0m
[0;32mI (41811) UART: uart rx break[0m
[0;32mI (41821) UART: uart[1] event[0m
[0;32mI (41821) UART: uart rx break[0m
[0;32mI (41821) UART: uart[1] event[0m
[0;32mI (41821) UART: uart rx break[0m
[0;32mI (41831) UART: uart[1] event[0m
[0;32mI (41831) UART: uart rx break[0m
[0;32mI (41831) UART: uart[1] event[0m
[0;32mI (41831) UART: uart rx break[0m
[0;32mI (41841) UART: uart[1] event[0m
[0;32mI (41841) UART: uart rx break[0m
[0;32mI (41841) UART: uart[1] event[0m
[0;32mI (41851) UART: uart rx break[0m
[0;32mI (41851) UART: uart[1] event[0m
[0;32mI (41851) UART: uart rx break[0m
[0;32mI (41851) UART: uart[1] event[0m
[0;32mI (41861) UART: uart rx break[0m
[0;32mI (41861) UART: uart[1] event[0m
[0;32mI (41861) UART: uart rx break[0m
[0;32mI (41861) UART: uart[1] event[0m
[0;32mI (41871) UART: uart rx break[0m
[0;32mI (41871) UART: uart[1] event[0m
[0;32mI (41871) UART: uart rx break[0m
[0;32mI (41881) UART: uart[1] event[0m
[0;32mI (41881) UART: uart rx break[0m
I (41881) UART: uart[1] event
[0;32mI (41881) UART: uart rx break[0m
[0;32mI (41891) UART: uart[1] event[0m
[0;32mI (41891) UART: uart rx break[0m
[0;32mI (41891) UART: uart[1] event[0m
[0;32mI (41891) UART: uart rx break[0m
[0;32mI (41901) UART: uart[1] event[0m
[0;32mI (41901) UART: uart rx break[0m
[0;32mI (41901) UART: uart[1] event[0m
[0;32mI (41901) UART: uart rx break[0m
[0;32mI (41911) UART: uart[1] event[0m
[0;32mI (41911) UART: uart rx break[0m
[0;32mI (41911) UART: uart[1] event[0m
[0;32mI (41921) UART: uart rx break[0m
[0;32mI (41921) UART: uart[1] event[0m
[0;32mI (41921) UART: uart rx break[0m
[0;32mI (41921) UART: uart[1] event[0m
[0;32mI (41931) UART: uart rx break[0m
[0;32mI (41931) UART: uart[1] event[0m
[0;32mI (41931) UART: uart rx break[0m
[0;32mI (41931) UART: uart[1] event[0m
[0;32mI (41941) UART: uart rx break[0m
[0;32mI (41941) UART: uart[1] event[0m
[0;32mI (41941) UART: uart rx break[0m
[0;32mI (41951) UART: uart[1] event[0m
[0;32mI (41951) UART: uart rx break[0m
[0;32mI (41951) UART: uart[1] event[0m
[0;32mI (41951) UART: uart rx break[0m
[0;32mI (41961) UART: uart[1] event[0m
[0;32mI (41961) UART: uart rx break[0m
[0;32mI (41961) UART: uart[1] event[0m
[0;32mI (41961) UART: uart rx break[0m
[0;32mI (41971) UART: uart[1] event[0m
[0;32mI (41971) UART: uart rx break[0m
[0;32mI (41971) UART: uart[1] event[0m
[0;32mI (41971) UART: uart rx break[0m
[0;32mI (41981) UART: uart[1] event[0m
[0;32mI (41981) UART: uart rx break[0m
[0;32mI (41981) UART: uart[1] event[0m
[0;32mI (41991) UART: uart rx break[0m
[0;32mI (41991) UART: uart[1] event[0m
[0;32mI (41991) UART: uart rx break[0m
[0;32mI (41991) UART: uart[1] event[0m
[0;32mI (42001) UART: uart rx break[0m
[0;32mI (42001) UART: uart[1] event[0m
[0;32mI (42001) UART: uart rx break[0m
[0;32mI (42001) UART: uart[1] event[0m
[0;32mI (42011) UART: uart rx break[0m
[0;32mI (42011) UART: uart[1] event[0m
[0;32mI (42011) UART: uart rx break[0m
[0;32mI (42021) UART: uart[1] event[0m
[0;32mI (42021) UART: uart rx break[0m
[0;32mI (42021) UART: uart[1] event[0m
[0;32mI (42021) UART: uart rx break[0m
[0;32mI (42031) UART: uart[1] event[0m
[0;32mI (42031) UART: uart rx break[0m
[0;32mI (42031) UART: uart[1] event[0m
[0;32mI (42031) UART: uart rx break[0m
[0;32mI (42041) UART: uart[1] event[0m
[0;32mI (42041) UART: uart rx break[0m
[0;32mI (42041) UART: uart[1] event[0m
[0;32mI (42041) UART: uart rx break[0m
[0;32mI (42051) UART: uart[0] event[0m
[0;32mI (42051) UART_READ: : 2[0m
[0;32mI (42051) UART_READ: : : UART_READ_END[0m
[0;32mI (42061) UART READ: [LEN: ]: 1473[0m
[0;32mI (42061) UART: uart[1] event[0m
[0;32mI (42061) UART: uart rx break[0m
[0;32mI (42061) UART: uart[1] event[0m
[0;32mI (42071) UART: uart rx break[0m
[0;32mI (42071) UART: uart[1] event[0m
[0;32mI (42071) UART: uart rx break[0m
[0;32mI (42081) UART: uart[1] event[0m
[0;32mI (42081) UART: uart rx break[0m
[0;32mI (42081) UART: uart[1] event[0m
[0;32mI (42081) UART: uart rx break[0m
[0;32mI (42091) UART: uart[1] event[0m
[0;32mI (42091) UART: uart rx break[0m
[0;32mI (42091) UART: uart[1] event[0m
[0;32mI (42091) UART: uart rx break[0m
[0;32mI (42101) UART: uart[1] event[0m
[0;32mI (42101) UART: uart rx break[0m
[0;32mI (42101) UART: uart[1] event[0m
[0;32mI (42101) UART: uart rx break[0m
[0;32mI (42111) UART: uart[1] event[0m
[0;32mI (42111) UART: uart rx break[0m
[0;32mI (42111) UART: uart[1] event[0m
[0;32mI (42121) UART: uart rx break[0m
[0;32mI (42121) UART: uart[1] event[0m
[0;32mI (42121) UART: uart rx break[0m
[0;32mI (42121) UART: uart[1] event[0m
[0;32mI (42131) UART: uart rx break[0m
[0;32mI (42131) UART: uart[1] event[0m
[0;32mI (42131) UART: uart rx break[0m
[0;32mI (42131) UART: uart[1] event[0m
[0;32mI (42141) UART: uart rx break[0m
[0;32mI (42141) UART: uart[1] event[0m
[0;32mI (42141) UART: uart rx break[0m
[0;32mI (42151) UART: uart[1] event[0m
[0;32mI (42151) UART: uart rx break[0m
[0;32mI (42151) UART: uart[1] event[0m
[0;32mI (42151) UART: uart rx break[0m
[0;32mI (42161) UART: uart[1] event[0m
[0;32mI (42161) UART: uart rx break[0m
[0;32mI (42161) UART: uart[0] event[0m
[0;32mI (42161) UART_READ: : 2[0m
[0;32mI (42171) UART_READ: : : UART_READ_END[0m
[0;32mI (42171) UART READ: [LEN: ]: 1475[0m
[0;32mI (42171) UART: uart[1] event[0m
[0;32mI (42181) UART: uart rx break[0m
[0;32mI (42181) UART: uart[1] event[0m
[0;32mI (42181) UART: uart rx break[0m
[0;32mI (42181) UART: uart[1] event[0m
[0;32mI (42191) UART: uart rx break[0m
[0;32mI (42191) UART: uart[1] event[0m
[0;32mI (42191) UART: uart rx break[0m
[0;32mI (42191) UART: uart[1] event[0m
[0;32mI (42201) UART: uart rx break[0m
[0;32mI (42201) UART: uart[1] event[0m
[0;32mI (42201) UART: uart rx break[0m
[0;32mI (42211) UART: uart[1] event[0m
[0;32mI (42211) UART: uart rx break[0m
[0;32mI (42211) UART: uart[1] event[0m
[0;32mI (42211) UART: uart rx break[0m
[0;32mI (42221) UART: uart[1] event[0m
[0;32mI (42221) UART: uart rx break[0m
[0;32mI (42221) UART: uart[1] event[0m
[0;32mI (42221) UART: uart rx break[0m
[0;32mI (42231) UART: uart[1] event[0m
[0;32mI (42231) UART: uart rx break[0m
[0;32mI (42231) UART: uart[1] event[0m
[0;32mI (42231) UART: uart rx break[0m
[0;32mI (42241) UART: uart[1] event[0m
[0;32mI (42241) UART: uart rx break[0m
[0;32mI (42241) UART: uart[1] event[0m
[0;32mI (42251) UART: uart rx break[0m
[0;32mI (42251) UART: uart[1] event[0m
[0;32mI (42251) UART: uart rx break[0m
[0;32mI (42251) UART: uart[1] event[0m
[0;32mI (42261) UART: uart rx break[0m
[0;32mI (42261) UART: uart[1] event[0m
[0;32mI (42261) UART: uart rx break[0m
[0;32mI (42261) UART: uart[1] event[0m
[0;32mI (42271) UART: uart rx break[0m
[0;32mI (42271) UART: uart[1] event[0m
[0;32mI (42271) UART: uart rx break[0m
[0;32mI (42271) UART: uart[1] event[0m
[0;32mI (42281) UART: uart rx break[0m
[0;32mI (42281) UART: uart[1] event[0m
[0;32mI (42281) UART: uart rx break[0m
[0;32mI (42291) UART: uart[1] event[0m
[0;32mI (42291) UART: uart rx break[0m
[0;32mI (42291) UART: uart[1] event[0m
[0;32mI (42291) UART: uart rx break[0m
[0;32mI (42301) UART: uart[1] event[0m
[0;32mI (42301) UART: uart rx break[0m
[0;32mI (42301) UART: uart[1] event[0m
[0;32mI (42301) UART: uart rx break[0m
[0;32mI (42311) UART: uart[0] event[0m
[0;32mI (42311) UART_READ: : 2[0m
[0;32mI (42311) UART_READ: : : UART_READ_END[0m
[0;32mI (42321) UART READ: [LEN: ]: 1495[0m
[0;32mI (42321) UART: uart[1] event[0m
[0;32mI (42321) UART: uart rx break[0m
[0;32mI (42321) UART: uart[1] event[0m
[0;32mI (42331) UART: uart rx break[0m
[0;32mI (42331) UART: uart[1] event[0m
[0;32mI (42331) UART: uart rx break[0m
[0;32mI (42331) UART: uart[1] event[0m
[0;32mI (42341) UART: uart rx break[0m
[0;32mI (42341) UART: uart[1] event[0m
[0;32mI (42341) UART: uart rx break[0m
[0;32mI (42351) UART: uart[1] event[0m
[0;32mI (42351) UART: uart rx break[0m
[0;32mI (42351) UART: uart[1] event[0m
[0;32mI (42351) UART: uart rx break[0m
[0;32mI (42361) UART: uart[1] event[0m
[0;32mI (42361) UART: uart rx break[0m
[0;32mI (42361) UART: uart[1] event[0m
[0;32mI (42361) UART: uart rx break[0m
[0;32mI (42371) UART: uart[1] event[0m
[0;32mI (42371) UART: uart rx break[0m
[0;32mI (42371) UART: uart[1] event[0m
[0;32mI (42381) UART: uart rx break[0m
[0;32mI (42381) UART: uart[1] event[0m
[0;32mI (42381) UART: uart rx break[0m
[0;32mI (42381) UART: uart[1] event[0m
[0;32mI (42391) UART: uart rx break[0m
[0;32mI (42391) UART: uart[1] event[0m
[0;32mI (42391) UART: uart rx break[0m
[0;32mI (42391) UART: uart[1] event[0m
[0;32mI (42401) UART: uart rx break[0m
[0;32mI (42401) UART: uart[1] event[0m
[0;32mI (42401) UART: uart rx break[0m
[0;32mI (42401) UART: uart[1] event[0m
[0;32mI (42411) UART: uart rx break[0m
[0;32mI (42411) UART: uart[1] event[0m
[0;32mI (42411) UART: uart rx break[0m
[0;32mI (42421) UART: uart[1] event[0m
[0;32mI (42421) UART: uart rx break[0m
[0;32mI (42421) UART: uart[1] event[0m
[0;32mI (42421) UART: uart rx break[0m
[0;32mI (42431) UART: uart[1] event[0m
[0;32mI (42431) UART: uart rx break[0m
[0;32mI (42431) UART: uart[1] event[0m
[0;32mI (42431) UART: uart rx break[0m
[0;32mI (42441) UART: uart[1] event[0m
[0;32mI (42441) UART: uart rx break[0m
[0;32mI (42441) UART: uart[1] event[0m
[0;32mI (42451) UART: uart rx break[0m
[0;32mI (42451) UART: uart[1] event[0m
[0;32mI (42451) UART: uart rx break[0m
[0;32mI (42451) UART: uart[1] event[0m
[0;32mI (42461) UART: uart rx break[0m
[0;32mI (42461) UART: uart[1] event[0m
[0;32mI (42461) UART: uart rx break[0m
[0;32mI (42461) UART: uart[1] event[0m
[0;32mI (42471) UART: uart rx break[0m
[0;32mI (42471) UART: uart[1] event[0m
[0;32mI (42471) UART: uart rx break[0m
[0;32mI (42471) UART: uart[1] event[0m
[0;32mI (42481) UART: uart rx break[0m
[0;32mI (42481) UART: uart[1] event[0m
[0;32mI (42481) UART: uart rx break[0m
[0;32mI (42491) UART: uart[1] event[0m
[0;32mI (42491) UART: uart rx break[0m
[0;32mI (42491) UART: uart[1] event[0m
[0;32mI (42491) UART: uart rx break[0m
[0;32mI (42501) UART: uart[1] event[0m
[0;32mI (42501) UART: uart rx break[0m
[0;32mI (42501) UART: uart[1] event[0m
[0;32mI (42501) UART: uart rx break[0m
[0;32mI (42511) UART: uart[1] event[0m
[0;32mI (42511) UART: uart rx break[0m
[0;32mI (42511) UART: uart[1] event[0m
[0;32mI (42521) UART: uart rx break[0m
[0;32mI (42521) UART: uart[1] event[0m
[0;32mI (42521) UART: uart rx break[0m
[0;32mI (42521) UART: uart[1] event[0m
[0;32mI (42531) UART: uart rx break[0m
[0;32mI (42531) UART: uart[1] event[0m
[0;32mI (42531) UART: uart rx break[0m
[0;32mI (42531) UART: uart[0] event[0m
[0;32mI (42541) UART_READ: : 2[0m
[0;32mI (42541) UART_READ: : : UART_READ_END[0m
[0;32mI (42541) UART READ: [LEN: ]: 1515[0m
[0;32mI (42551) UART: uart[1] event[0m
[0;32mI (42551) UART: uart rx break[0m
[0;32mI (42551) UART: uart[1] event[0m
[0;32mI (42551) UART: uart rx break[0m
[0;32mI (42561) UART: uart[1] event[0m
[0;32mI (42561) UART: uart rx break[0m
[0;32mI (42561) UART: uart[1] event[0m
[0;32mI (42561) UART: uart rx break[0m
[0;32mI (42571) UART: uart[1] event[0m
[0;32mI (42571) UART: uart rx break[0m
[0;32mI (42571) UART: uart[1] event[0m
[0;32mI (42581) UART: uart rx break[0m
[0;32mI (42581) UART: uart[1] event[0m
[0;32mI (42581) UART: uart rx break[0m
[0;32mI (42581) UART: uart[1] event[0m
[0;32mI (42591) UART: uart rx break[0m
[0;32mI (42591) UART: uart[1] event[0m
[0;32mI (42591) UART: uart rx break[0m
[0;32mI (42591) UART: uart[0] event[0m
[0;32mI (42601) UART_READ: : 2[0m
[0;32mI (42601) UART_READ: : : UART_READ_END[0m
[0;32mI (42601) UART READ: [LEN: ]: 1535[0m
[0;32mI (42611) UART: uart[1] event[0m
[0;32mI (42611) UART: uart rx break[0m
[0;32mI (42611) UART: uart[1] event[0m
[0;32mI (42611) UART: uart rx break[0m
[0;32mI (42621) UART: uart[1] event[0m
[0;32mI (42621) UART: uart rx break[0m
[0;32mI (42621) UART: uart[1] event[0m
[0;32mI (42621) UART: uart rx break[0m
[0;32mI (42631) UART: uart[1] event[0m
[0;32mI (42631) UART: uart rx break[0m
[0;32mI (42631) UART: uart[1] event[0m
[0;32mI (42641) UART: uart rx break[0m
[0;32mI (42641) UART: uart[1] event[0m
[0;32mI (42641) UART: uart rx break[0m
[0;32mI (42641) UART: uart[0] event[0m
[0;32mI (42651) UART_READ: : 2[0m
[0;32mI (42651) UART_READ: : : UART_READ_END[0m
[0;32mI (42651) UART READ: [LEN: ]: 1558[0m
[0;32mI (42651) UART: uart[1] event[0m
[0;32mI (42661) UART: uart rx break[0m
[0;32mI (42661) UART: uart[1] event[0m
[0;32mI (42661) UART: uart rx break[0m
[0;32mI (42671) UART: uart[1] event[0m
[0;32mI (42671) UART: uart rx break[0m
[0;32mI (42671) UART: uart[1] event[0m
[0;32mI (42671) UART: uart rx break[0m
[0;32mI (42681) UART: uart[1] event[0m
[0;32mI (42681) UART: uart rx break[0m
[0;32mI (42681) UART: uart[1] event[0m
[0;32mI (42681) UART: uart rx break[0m
[0;32mI (42691) UART: uart[1] event[0m
[0;32mI (42691) UART: uart rx break[0m
[0;32mI (42691) UART: uart[1] event[0m
[0;32mI (42701) UART: uart rx break[0m
[0;32mI (42701) UART: uart[1] event[0m
[0;32mI (42701) UART: uart rx break[0m
[0;32mI (42701) UART: uart[1] event[0m
[0;32mI (42711) UART: uart rx break[0m
[0;32mI (42711) UART: uart[1] event[0m
[0;32mI (42711) UART: uart rx break[0m
[0;32mI (42711) UART: uart[1] event[0m
[0;32mI (42721) UART: uart rx break[0m
[0;32mI (42721) UART: uart[1] event[0m
[0;32mI (42721) UART: uart rx break[0m
[0;32mI (42721) UART: uart[1] event[0m
[0;32mI (42731) UART: uart rx break[0m
[0;32mI (42731) UART: uart[1] event[0m
[0;32mI (42731) UART: uart rx break[0m
[0;32mI (42741) UART: uart[1] event[0m
[0;32mI (42741) UART: uart rx break[0m
[0;32mI (42741) UART: uart[1] event[0m
[0;32mI (42741) UART: uart rx break[0m
[0;32mI (42751) UART: uart[1] event[0m
[0;32mI (42751) UART: uart rx break[0m
[0;32mI (42751) UART: uart[1] event[0m
[0;32mI (42751) UART: uart rx break[0m
[0;32mI (42761) UART: uart[1] event[0m
[0;32mI (42761) UART: uart rx break[0m
[0;32mI (42761) UART: uart[1] event[0m
[0;32mI (42771) UART: uart rx break[0m
[0;32mI (42771) UART: uart[1] event[0m
[0;32mI (42771) UART: uart rx break[0m
[0;32mI (42771) UART: uart[1] event[0m
[0;32mI (42781) UART: uart rx break[0m
[0;32mI (42781) UART: uart[1] event[0m
[0;32mI (42781) UART: uart rx break[0m
[0;32mI (42781) UART: uart[0] event[0m
[0;32mI (42791) UART_READ: : 2[0m
[0;32mI (42791) UART_READ: : : UART_READ_END[0m
[0;32mI (42791) UART READ: [LEN: ]: 1599[0m
[0;32mI (42801) UART: uart[1] event[0m
[0;32mI (42801) UART: uart rx break[0m
[0;32mI (42801) UART: uart[1] event[0m
[0;32mI (42801) UART: uart rx break[0m
[0;32mI (42811) UART: uart[1] event[0m
[0;32mI (42811) UART: uart rx break[0m
[0;32mI (42811) UART: uart[1] event[0m
[0;32mI (42811) UART: uart rx break[0m
[0;32mI (42821) UART: uart[1] event[0m
[0;32mI (42821) UART: uart rx break[0m
[0;32mI (42821) UART: uart[1] event[0m
[0;32mI (42831) UART: uart rx break[0m
[0;32mI (42831) UART: uart[1] event[0m
[0;32mI (42831) UART: uart rx break[0m
[0;32mI (42831) UART: uart[1] event[0m
[0;32mI (42841) UART: uart rx break[0m
[0;32mI (42841) UART: uart[1] event[0m
[0;32mI (42841) UART: uart rx break[0m
[0;32mI (42841) UART: uart[1] event[0m
[0;32mI (42851) UART: uart rx break[0m
[0;32mI (42851) UART: uart[1] event[0m
[0;32mI (42851) UART: uart rx break[0m
[0;32mI (42851) UART: uart[1] event[0m
[0;32mI (42861) UART: uart rx break[0m
[0;32mI (42861) UART: uart[1] event[0m
[0;32mI (42861) UART: uart rx break[0m
[0;32mI (42871) UART: uart[1] event[0m
[0;32mI (42871) UART: uart rx break[0m
[0;32mI (42871) UART: uart[1] event[0m
[0;32mI (42871) UART: uart rx break[0m
[0;32mI (42881) UART: uart[1] event[0m
[0;32mI (42881) UART: uart rx break[0m
[0;32mI (42881) UART: uart[1] event[0m
[0;32mI (42881) UART: uart rx break[0m
[0;32mI (42891) UART: uart[1] event[0m
[0;32mI (42891) UART: uart rx break[0m
[0;32mI (42891) UART: uart[1] event[0m
[0;32mI (42901) UART: uart rx break[0m
[0;32mI (42901) UART: uart[1] event[0m
[0;32mI (42901) UART: uart rx break[0m
[0;32mI (42901) UART: uart[1] event[0m
[0;32mI (42911) UART: uart rx break[0m
[0;32mI (42911) UART: uart[1] event[0m
[0;32mI (42911) UART: uart rx break[0m
[0;32mI (43301) UART: uart[1] event[0m
[0;32mI (43301) UART: uart rx break[0m
[0;32mI (43301) UART: uart[1] event[0m
[0;32mI (43301) UART: uart rx break[0m
[0;32mI (43301) UART: uart[1] event[0m
[0;32mI (43301) UART: uart rx break[0m
[0;32mI (43301) UART: uart[1] event[0m
[0;32mI (43301) UART: uart rx break[0m
[0;32mI (43311) UART: uart[1] event[0m
[0;32mI (43311) UART: uart rx break[0m
[0;32mI (43311) UART: uart[1] event[0m
[0;32mI (43311) UART: uart rx break[0m
[0;32mI (43321) UART: uart[1] event[0m
[0;32mI (43321) UART: uart rx break[0m
[0;32mI (43321) UART: uart[1] event[0m
[0;32mI (43321) UART: uart rx break[0m
[0;32mI (43331) UART: uart[1] event[0m
[0;32mI (43331) UART: uart rx break[0m
[0;32mI (43331) UART: uart[1] event[0m
[0;32mI (43341) UART: uart rx break[0m
[0;32mI (43341) UART: uart[1] event[0m
[0;32mI (43341) UART: uart rx break[0m
[0;32mI (43341) UART: uart[1] event[0m
[0;32mI (43351) UART: uart rx break[0m
[0;32mI (43351) UART: uart[1] event[0m
[0;32mI (43351) UART: uart rx break[0m
[0;32mI (43351) UART: uart[1] event[0m
[0;32mI (43361) UART: uart rx break[0m
[0;32mI (43361) UART: uart[1] event[0m
[0;32mI (43361) UART: uart rx break[0m
[0;32mI (43361) UART: uart[1] event[0m
[0;32mI (43371) UART: uart rx break[0m
[0;32mI (43371) UART: uart[1] event[0m
[0;32mI (43371) UART: uart rx break[0m
[0;32mI (43381) UART: uart[1] event[0m
[0;32mI (43381) UART: uart rx break[0m
[0;32mI (43381) UART: uart[1] event[0m
[0;32mI (43381) UART: uart rx break[0m
[0;32mI (43391) UART: uart[1] event[0m
[0;32mI (43391) UART: uart rx break[0m
[0;32mI (43391) UART: uart[1] event[0m
[0;32mI (43391) UART: uart rx break[0m
[0;32mI (43401) UART: uart[1] event[0m
[0;32mI (43401) UART: uart rx break[0m
[0;32mI (43401) UART: uart[1] event[0m
[0;32mI (43411) UART: uart rx break[0m
[0;32mI (43411) UART: uart[1] event[0m
[0;32mI (43411) UART: uart rx break[0m
[0;32mI (43411) UART: uart[1] event[0m
[0;32mI (43421) UART: uart rx break[0m
[0;32mI (43421) UART: uart[1] event[0m
[0;32mI (43421) UART: uart rx break[0m
[0;32mI (43421) UART: uart[1] event[0m
[0;32mI (43431) UART: uart rx break[0m
[0;32mI (43431) UART: uart[1] event[0m
[0;32mI (43431) UART: uart rx break[0m
[0;32mI (43431) UART: uart[1] event[0m
[0;32mI (43441) UART: uart rx break[0m
[0;32mI (43441) UART: uart[1] event[0m
[0;32mI (43441) UART: uart rx break[0m
[0;32mI (43451) UART: uart[1] event[0m
[0;32mI (43451) UART: uart rx break[0m
[0;32mI (43451) UART: uart[1] event[0m
[0;32mI (43451) UART: uart rx break[0m
[0;32mI (43461) UART: uart[1] event[0m
[0;32mI (43461) UART: uart rx break[0m
[0;32mI (43461) UART: uart[1] event[0m
[0;32mI (43461) UART: uart rx break[0m
[0;32mI (43471) UART: uart[1] event[0m
[0;32mI (43471) UART: uart rx break[0m
[0;32mI (43471) UART: uart[1] event[0m
[0;32mI (43481) UART: uart rx break[0m
[0;32mI (43481) UART: uart[1] event[0m
[0;32mI (43481) UART: uart rx break[0m
[0;32mI (43481) UART: uart[1] event[0m
[0;32mI (43491) UART: uart rx break[0m
[0;32mI (43491) UART: uart[1] event[0m
[0;32mI (43491) UART: uart rx break[0m
[0;32mI (43491) UART: uart[0] event[0m
[0;32mI (43501) UART_READ: : 2[0m
[0;32mI (43501) UART_READ: : : UART_READ_END[0m
[0;32mI (43501) UART READ: [LEN: ]: 1719[0m
[0;32mI (43511) UART: uart[1] event[0m
[0;32mI (43511) UART: uart rx break[0m
[0;32mI (43511) UART: uart[1] event[0m
[0;32mI (43511) UART: uart rx break[0m
[0;32mI (43521) UART: uart[1] event[0m
[0;32mI (43521) UART: uart rx break[0m
[0;32mI (43521) UART: uart[1] event[0m
[0;32mI (43521) UART: uart rx break[0m
[0;32mI (43531) UART: uart[1] event[0m
[0;32mI (43531) UART: uart rx break[0m
[0;32mI (43531) UART: uart[1] event[0m
[0;32mI (43541) UART: uart rx break[0m
[0;32mI (43541) UART: uart[1] event[0m
[0;32mI (43541) UART: uart rx break[0m
[0;32mI (43541) UART: uart[1] event[0m
[0;32mI (43551) UART: uart rx break[0m
[0;32mI (43551) UART: uart[1] event[0m
[0;32mI (43551) UART: uart rx break[0m
[0;32mI (43551) UART: uart[1] event[0m
[0;32mI (43561) UART: uart rx break[0m
[0;32mI (43561) UART: uart[1] event[0m
[0;32mI (43561) UART: uart rx break[0m
[0;32mI (43561) UART: uart[1] event[0m
[0;32mI (43571) UART: uart rx break[0m
[0;32mI (43571) UART: uart[1] event[0m
[0;32mI (43571) UART: uart rx break[0m
[0;32mI (43581) UART: uart[1] event[0m
[0;32mI (43581) UART: uart rx break[0m
[0;32mI (43581) UART: uart[1] event[0m
[0;32mI (43581) UART: uart rx break[0m
[0;32mI (43591) UART: uart[1] event[0m
[0;32mI (43591) UART: uart rx break[0m
[0;32mI (43591) UART: uart[1] event[0m
[0;32mI (43591) UART: uart rx break[0m
[0;32mI (43601) UART: uart[1] event[0m
[0;32mI (43601) UART: uart rx break[0m
[0;32mI (43601) UART: uart[1] event[0m
[0;32mI (43611) UART: uart rx break[0m
[0;32mI (43611) UART: uart[1] event[0m
[0;32mI (43611) UART: uart rx break[0m
[0;32mI (43611) UART: uart[1] event[0m
[0;32mI (43621) UART: uart rx break[0m
[0;32mI (43621) UART: uart[1] event[0m
[0;32mI (43621) UART: uart rx break[0m
[0;32mI (43621) UART: uart[1] event[0m
[0;32mI (43631) UART: uart rx break[0m
[0;32mI (43631) UART: uart[1] event[0m
[0;32mI (43631) UART: uart rx break[0m
[0;32mI (43631) UART: uart[1] event[0m
[0;32mI (43641) UART: uart rx break[0m
[0;32mI (43641) UART: uart[1] event[0m
[0;32mI (43641) UART: uart rx break[0m
[0;32mI (43651) UART: uart[1] event[0m
[0;32mI (43651) UART: uart rx break[0m
[0;32mI (43651) UART: uart[1] event[0m
[0;32mI (43651) UART: uart rx break[0m
[0;32mI (43661) UART: uart[1] event[0m
[0;32mI (43661) UART: uart rx break[0m
[0;32mI (43661) UART: uart[1] event[0m
[0;32mI (43661) UART: uart rx break[0m
[0;32mI (43671) UART: uart[1] event[0m
[0;32mI (43671) UART: uart rx break[0m
[0;32mI (43671) UART: uart[1] event[0m
[0;32mI (43681) UART: uart rx break[0m
[0;32mI (43681) UART: uart[1] event[0m
[0;32mI (43681) UART: uart rx break[0m
[0;32mI (43681) UART: uart[1] event[0m
[0;32mI (43691) UART: uart rx break[0m
[0;32mI (43691) UART: uart[1] event[0m
[0;32mI (43691) UART: uart rx break[0m
[0;32mI (43691) UART: uart[1] event[0m
[0;32mI (43701) UART: uart rx break[0m
[0;32mI (43701) UART: uart[1] event[0m
[0;32mI (43701) UART: uart rx break[0m
[0;32mI (43701) UART: uart[1] event[0m
[0;32mI (43711) UART: uart rx break[0m
[0;32mI (43711) UART: uart[1] event[0m
[0;32mI (43711) UART: uart rx break[0m
[0;32mI (43721) UART: uart[1] event[0m
[0;32mI (43721) UART: uart rx break[0m
[0;32mI (43721) UART: uart[1] event[0m
[0;32mI (43721) UART: uart rx break[0m
[0;32mI (43731) UART: uart[1] event[0m
[0;32mI (43731) UART: uart rx break[0m
[0;32mI (43731) UART: uart[1] event[0m
[0;32mI (43731) UART: uart rx break[0m
[0;32mI (43741) UART: uart[1] event[0m
[0;32mI (43741) UART: uart rx break[0m
[0;32mI (43741) UART: uart[1] event[0m
[0;32mI (43751) UART: uart rx break[0m
[0;32mI (43751) UART: uart[1] event[0m
[0;32mI (43751) UART: uart rx break[0m
[0;32mI (43751) UART: uart[1] event[0m
[0;32mI (43761) UART: uart rx break[0m
[0;32mI (43761) UART: uart[1] event[0m
[0;32mI (43761) UART: uart rx break[0m
[0;32mI (43761) UART: uart[1] event[0m
[0;32mI (43771) UART: uart rx break[0m
[0;32mI (43771) UART: uart[1] event[0m
[0;32mI (43771) UART: uart rx break[0m
[0;32mI (43771) UART: uart[1] event[0m
[0;32mI (43781) UART: uart rx break[0m
[0;32mI (43781) UART: uart[1] event[0m
[0;32mI (43781) UART: uart rx break[0m
[0;32mI (43791) UART: uart[1] event[0m
[0;32mI (43791) UART: uart rx break[0m
[0;32mI (43791) UART: uart[1] event[0m
[0;32mI (43791) UART: uart rx break[0m
[0;32mI (43801) UART: uart[1] event[0m
[0;32mI (43801) UART: uart rx break[0m
[0;32mI (43801) UART: uart[1] event[0m
[0;32mI (43801) UART: uart rx break[0m
[0;32mI (43811) UART: uart[1] event[0m
[0;32mI (43811) UART: uart rx break[0m
[0;32mI (43811) UART: uart[1] event[0m
[0;32mI (43821) UART: uart rx break[0m
[0;32mI (43821) UART: uart[1] event[0m
[0;32mI (43821) UART: uart rx break[0m
[0;32mI (43821) UART: uart[1] event[0m
[0;32mI (43831) UART: uart rx break[0m
[0;32mI (43831) UART: uart[1] event[0m
[0;32mI (43831) UART: uart rx break[0m
[0;32mI (43831) UART: uart[0] event[0m
[0;32mI (43841) UART_READ: : 2[0m
[0;32mI (43841) UART_READ: : : UART_READ_END[0m
[0;32mI (43841) UART READ: [LEN: ]: 1839[0m
[0;32mI (43851) UART: uart[1] event[0m
[0;32mI (43851) UART: uart rx break[0m
[0;32mI (43851) UART: uart[1] event[0m
[0;32mI (43851) UART: uart rx break[0m
[0;32mI (43861) UART: uart[1] event[0m
[0;32mI (43861) UART: uart rx break[0m
[0;32mI (43861) UART: uart[1] event[0m
[0;32mI (43861) UART: uart rx break[0m
[0;32mI (43871) UART: uart[1] event[0m
[0;32mI (43871) UART: uart rx break[0m
[0;32mI (43871) UART: uart[1] event[0m
[0;32mI (43881) UART: uart rx break[0m
[0;32mI (43881) UART: uart[1] event[0m
[0;32mI (43881) UART: uart rx break[0m
[0;32mI (43881) UART: uart[1] event[0m
[0;32mI (43891) UART: uart rx break[0m
[0;32mI (43891) UART: uart[1] event[0m
[0;32mI (43891) UART: uart rx break[0m
[0;32mI (43891) UART: uart[1] event[0m
[0;32mI (43901) UART: uart rx break[0m
[0;32mI (43901) UART: uart[1] event[0m
[0;32mI (43901) UART: uart rx break[0m
[0;32mI (43901) UART: uart[1] event[0m
[0;32mI (43911) UART: uart rx break[0m
[0;32mI (43911) UART: uart[1] event[0m
[0;32mI (43911) UART: uart rx break[0m
[0;32mI (43921) UART: uart[1] event[0m
[0;32mI (43921) UART: uart rx break[0m
[0;32mI (43921) UART: uart[1] event[0m
[0;32mI (43921) UART: uart rx break[0m
[0;32mI (43931) UART: uart[1] event[0m
[0;32mI (43931) UART: uart rx break[0m
[0;32mI (43931) UART: uart[1] event[0m
[0;32mI (43931) UART: uart rx break[0m
[0;32mI (43941) UART: uart[1] event[0m
[0;32mI (43941) UART: uart rx break[0m
[0;32mI (43941) UART: uart[1] event[0m
[0;32mI (43951) UART: uart rx break[0m
[0;32mI (43951) UART: uart[1] event[0m
[0;32mI (43951) UART: uart rx break[0m
[0;32mI (43951) UART: uart[1] event[0m
[0;32mI (43961) UART: uart rx break[0m
[0;32mI (43961) UART: uart[1] event[0m
[0;32mI (43961) UART: uart rx break[0m
[0;32mI (43961) UART: uart[1] event[0m
[0;32mI (43971) UART: uart rx break[0m
[0;32mI (43971) UART: uart[1] event[0m
[0;32mI (43971) UART: uart rx break[0m
[0;32mI (43971) UART: uart[1] event[0m
[0;32mI (43981) UART: uart rx break[0m
[0;32mI (43981) UART: uart[1] event[0m
[0;32mI (43981) UART: uart rx break[0m
[0;32mI (43991) UART: uart[1] event[0m
[0;32mI (43991) UART: uart rx break[0m
[0;32mI (43991) UART: uart[0] event[0m
[0;32mI (43991) UART_READ: : 2[0m
[0;32mI (44001) UART_READ: : : UART_READ_END[0m
[0;32mI (44001) UART READ: [LEN: ]: 1878[0m
[0;32mI (44001) UART: uart[1] event[0m
[0;32mI (44011) UART: uart rx break[0m
[0;32mI (44011) UART: uart[1] event[0m
[0;32mI (44011) UART: uart rx break[0m
[0;32mI (44011) UART: uart[1] event[0m
[0;32mI (44021) UART: uart rx break[0m
[0;32mI (44021) UART: uart[1] event[0m
[0;32mI (44021) UART: uart rx break[0m
[0;32mI (44021) UART: uart[1] event[0m
[0;32mI (44031) UART: uart rx break[0m
[0;32mI (44031) UART: uart[1] event[0m
[0;32mI (44031) UART: uart rx break[0m
[0;32mI (44031) UART: uart[1] event[0m
[0;32mI (44041) UART: uart rx break[0m
[0;32mI (44041) UART: uart[1] event[0m
[0;32mI (44041) UART: uart rx break[0m
[0;32mI (44051) UART: uart[1] event[0m
[0;32mI (44051) UART: uart rx break[0m
[0;32mI (44051) UART: uart[1] event[0m
[0;32mI (44051) UART: uart rx break[0m
[0;32mI (44061) UART: uart[1] event[0m
[0;32mI (44061) UART: uart rx break[0m
[0;32mI (44061) UART: uart[1] event[0m
[0;32mI (44061) UART: uart rx break[0m
[0;32mI (44071) UART: uart[1] event[0m
[0;32mI (44071) UART: uart rx break[0m
[0;32mI (44071) UART: uart[1] event[0m
[0;32mI (44071) UART: uart rx break[0m
[0;32mI (44081) UART: uart[1] event[0m
[0;32mI (44081) UART: uart rx break[0m
[0;32mI (44081) UART: uart[1] event[0m
[0;32mI (44091) UART: uart rx break[0m
[0;32mI (44091) UART: uart[1] event[0m
[0;32mI (44091) UART: uart rx break[0m
[0;32mI (44091) UART: uart[1] event[0m
[0;32mI (44101) UART: uart rx break[0m
[0;32mI (44101) UART: uart[1] event[0m
[0;32mI (44101) UART: uart rx break[0m
[0;32mI (44101) UART: uart[1] event[0m
[0;32mI (44111) UART: uart rx break[0m
[0;32mI (44111) UART: uart[1] event[0m
[0;32mI (44111) UART: uart rx break[0m
[0;32mI (44121) UART: uart[1] event[0m
[0;32mI (44121) UART: uart rx break[0m
[0;32mI (44121) UART: uart[1] event[0m
[0;32mI (44121) UART: uart rx break[0m
[0;32mI (44131) UART: uart[1] event[0m
[0;32mI (44131) UART: uart rx break[0m
[0;32mI (44131) UART: uart[1] event[0m
[0;32mI (44131) UART: uart rx break[0m
[0;32mI (44141) UART: uart[1] event[0m
[0;32mI (44141) UART: uart rx break[0m
[0;32mI (44141) UART: uart[1] event[0m
[0;32mI (44141) UART: uart rx break[0m
[0;32mI (44151) UART: uart[1] event[0m
[0;32mI (44151) UART: uart rx break[0m
[0;32mI (44151) UART: uart[0] event[0m
[0;32mI (44161) UART_READ: : 2[0m
[0;32mI (44161) UART_READ: : : UART_READ_END[0m
[0;32mI (44161) UART READ: [LEN: ]: 1904[0m
[0;32mI (44161) UART: uart[1] event[0m
[0;32mI (44171) UART: uart rx break[0m
[0;32mI (44171) UART: uart[1] event[0m
[0;32mI (44171) UART: uart rx break[0m
[0;32mI (44181) UART: uart[1] event[0m
[0;32mI (44181) UART: uart rx break[0m
[0;32mI (44181) UART: uart[1] event[0m
[0;32mI (44181) UART: uart rx break[0m
[0;32mI (44191) UART: uart[1] event[0m
[0;32mI (44191) UART: uart rx break[0m
[0;32mI (44191) UART: uart[1] event[0m
[0;32mI (44191) UART: uart rx break[0m
[0;32mI (44201) UART: uart[1] event[0m
[0;32mI (44201) UART: uart rx break[0m
[0;32mI (44201) UART: uart[1] event[0m
[0;32mI (44201) UART: uart rx break[0m
[0;32mI (44211) UART: uart[1] event[0m
[0;32mI (44211) UART: uart rx break[0m
[0;32mI (44211) UART: uart[1] event[0m
[0;32mI (44221) UART: uart rx break[0m
[0;32mI (44221) UART: uart[0] event[0m
[0;32mI (44221) UART_READ: : 2[0m
[0;32mI (44221) UART_READ: : : UART_READ_END[0m
[0;32mI (44231) UART READ: [LEN: ]: 1923[0m
[0;32mI (44231) UART: uart[1] event[0m
[0;32mI (44231) UART: uart rx break[0m
[0;32mI (44241) UART: uart[1] event[0m
[0;32mI (44241) UART: uart rx break[0m
[0;32mI (44241) UART: uart[1] event[0m
[0;32mI (44241) UART: uart rx break[0m
[0;32mI (44251) UART: uart[1] event[0m
[0;32mI (44251) UART: uart rx break[0m
[0;32mI (44251) UART: uart[1] event[0m
[0;32mI (44251) UART: uart rx break[0m
[0;32mI (44261) UART: uart[1] event[0m
[0;32mI (44261) UART: uart rx break[0m
[0;32mI (44261) UART: uart[1] event[0m
[0;32mI (44261) UART: uart rx break[0m
[0;32mI (44271) UART: uart[1] event[0m
[0;32mI (44271) UART: uart rx break[0m
[0;32mI (44271) UART: uart[1] event[0m
[0;32mI (44281) UART: uart rx break[0m
[0;32mI (44281) UART: uart[1] event[0m
[0;32mI (44281) UART: uart rx break[0m
[0;32mI (44281) UART: uart[0] event[0m
[0;32mI (44291) UART_READ: : 2[0m
[0;32mI (44291) UART_READ: : : UART_READ_END[0m
[0;32mI (44291) UART READ: [LEN: ]: 1940[0m
[0;32mI (44301) UART: uart[1] event[0m
[0;32mI (44301) UART: uart rx break[0m
[0;32mI (44301) UART: uart[1] event[0m
[0;32mI (44301) UART: uart rx break[0m
[0;32mI (44311) UART: uart[1] event[0m
[0;32mI (44311) UART: uart rx break[0m
[0;32mI (44311) UART: uart[1] event[0m
[0;32mI (44311) UART: uart rx break[0m
[0;32mI (44321) UART: uart[1] event[0m
[0;32mI (44321) UART: uart rx break[0m
[0;32mI (44321) UART: uart[1] event[0m
[0;32mI (44321) UART: uart rx break[0m
[0;32mI (44331) UART: uart[1] event[0m
[0;32mI (44331) UART: uart rx break[0m
[0;32mI (44331) UART: uart[1] event[0m
[0;32mI (44341) UART: uart rx break[0m
[0;32mI (44341) UART: uart[1] event[0m
[0;32mI (44341) UART: uart rx break[0m
[0;32mI (44341) UART: uart[1] event[0m
[0;32mI (44351) UART: uart rx break[0m
[0;32mI (44351) UART: uart[1] event[0m
[0;32mI (44351) UART: uart rx break[0m
[0;32mI (44351) UART: uart[1] event[0m
[0;32mI (44361) UART: uart rx break[0m
[0;32mI (44361) UART: uart[0] event[0m
[0;32mI (44361) UART_READ: : 2[0m
[0;32mI (44361) UART_READ: : : UART_READ_END[0m
[0;32mI (44371) UART READ: [LEN: ]: 1960[0m
[0;32mI (44371) UART: uart[1] event[0m
[0;32mI (44371) UART: uart rx break[0m
[0;32mI (44381) UART: uart[1] event[0m
[0;32mI (44381) UART: uart rx break[0m
[0;32mI (44381) UART: uart[1] event[0m
[0;32mI (44381) UART: uart rx break[0m
[0;32mI (44391) UART: uart[1] event[0m
[0;32mI (44391) UART: uart rx break[0m
[0;32mI (44391) UART: uart[1] event[0m
[0;32mI (44401) UART: uart rx break[0m
[0;32mI (44401) UART: uart[1] event[0m
[0;32mI (44401) UART: uart rx break[0m
[0;32mI (44401) UART: uart[1] event[0m
[0;32mI (44411) UART: uart rx break[0m
[0;32mI (44411) UART: uart[1] event[0m
[0;32mI (44411) UART: uart rx break[0m
[0;32mI (44411) UART: uart[1] event[0m
[0;32mI (44421) UART: uart rx break[0m
[0;32mI (44421) UART: uart[1] event[0m
[0;32mI (44421) UART: uart rx break[0m
[0;32mI (44431) UART: uart[1] event[0m
[0;32mI (44431) UART: uart rx break[0m
[0;32mI (44431) UART: uart[1] event[0m
[0;32mI (44431) UART: uart rx break[0m
[0;32mI (44441) UART: uart[1] event[0m
[0;32mI (44441) UART: uart rx break[0m
[0;32mI (44441) UART: uart[1] event[0m
[0;32mI (44441) UART: uart rx break[0m
[0;32mI (44451) UART: uart[1] event[0m
[0;32mI (44451) UART: uart rx break[0m
[0;32mI (44451) UART: uart[1] event[0m
[0;32mI (44451) UART: uart rx break[0m
[0;32mI (44461) UART: uart[1] event[0m
[0;32mI (44461) UART: uart rx break[0m
[0;32mI (44461) UART: uart[1] event[0m
[0;32mI (44471) UART: uart rx break[0m
[0;32mI (44471) UART: uart[1] event[0m
[0;32mI (44471) UART: uart rx break[0m
[0;32mI (44471) UART: uart[1] event[0m
[0;32mI (44481) UART: uart rx break[0m
[0;32mI (44481) UART: uart[1] event[0m
[0;32mI (44481) UART: uart rx break[0m
[0;32mI (44481) UART: uart[1] event[0m
[0;32mI (44491) UART: uart rx break[0m
[0;32mI (44491) UART: uart[1] event[0m
[0;32mI (44491) UART: uart rx break[0m
[0;32mI (44501) UART: uart[1] event[0m
[0;32mI (44501) UART: uart rx break[0m
[0;32mI (44501) UART: uart[1] event[0m
[0;32mI (44501) UART: uart rx break[0m
[0;32mI (44511) UART: uart[1] event[0m
[0;32mI (44511) UART: uart rx break[0m
[0;32mI (44511) UART: uart[1] event[0m
[0;32mI (44511) UART: uart rx break[0m
[0;32mI (44521) UART: uart[1] event[0m
[0;32mI (44521) UART: uart rx break[0m
[0;32mI (44521) UART: uart[1] event[0m
[0;32mI (44521) UART: uart rx break[0m
[0;32mI (44531) UART: uart[1] event[0m
[0;32mI (44531) UART: uart rx break[0m
[0;32mI (44531) UART: uart[1] event[0m
[0;32mI (44541) UART: uart rx break[0m
[0;32mI (44541) UART: uart[1] event[0m
[0;32mI (44541) UART: uart rx break[0m
[0;32mI (44541) UART: uart[1] event[0m
[0;32mI (44551) UART: uart rx break[0m
[0;32mI (44551) UART: uart[1] event[0m
[0;32mI (44551) UART: uart rx break[0m
[0;32mI (44551) UART: uart[1] event[0m
[0;32mI (44561) UART: uart rx break[0m
[0;32mI (44561) UART: uart[1] event[0m
[0;32mI (44561) UART: uart rx break[0m
[0;32mI (44571) UART: uart[1] event[0m
[0;32mI (44571) UART: uart rx break[0m
[0;32mI (44571) UART: uart[1] event[0m
[0;32mI (44571) UART: uart rx break[0m
[0;32mI (44581) UART: uart[1] event[0m
[0;32mI (44581) UART: uart rx break[0m
[0;32mI (44581) UART: uart[1] event[0m
[0;32mI (44581) UART: uart rx break[0m
[0;32mI (44591) UART: uart[1] event[0m
[0;32mI (44591) UART: uart rx break[0m
[0;32mI (44591) UART: uart[1] event[0m
[0;32mI (44591) UART: uart rx 