import asyncio
from bleak import BleakClient
import sys

# BLE device MAC address
BLE_DEVICE_ADDRESS = "A0:85:E3:F0:76:16"

# UUIDs
WRITE_CHAR_UUID = "A2BD0013-AD84-44BE-94BB-B289C6D34F32"
READ_CHAR_UUID = "A2BD0012-AD84-44BE-94BB-B289C6D34F32"

CHUNK_SIZE = 500
TOTAL_SIZE = 5312

# Shared data and event
ack_event = asyncio.Event()
received_data = bytearray()


def reflect(value, bits):
    result = 0
    for i in range(bits):
        if value & (1 << i):
            result |= 1 << (bits - 1 - i)
    return result


def crc32_iso(data: bytes) -> int:
    poly = 0x04C11DB7
    crc = 0xFFFFFFFF

    for byte in data:
        byte = reflect(byte, 8)
        crc ^= byte << 24

        for _ in range(8):
            if crc & 0x80000000:
                crc = (crc << 1) ^ poly
            else:
                crc <<= 1
            crc &= 0xFFFFFFFF

    crc = reflect(crc, 32)
    return crc ^ 0xFFFFFFFF


def handle_notification(sender, data):
    global received_data
    received_data.extend(data)
    print(f"Received {len(data)} bytes, Total: {len(received_data)} bytes")
    ack_event.set()  # Notify that data has been received


async def send_request(byte_array, crc):
    byte_array += crc.to_bytes(4, byteorder='big')
    print(f"Sending: {byte_array.hex()}")

    async with BleakClient(BLE_DEVICE_ADDRESS) as client:
        await client.start_notify(READ_CHAR_UUID, handle_notification)

        # Send the initial request
        await client.write_gatt_char(WRITE_CHAR_UUID, byte_array)
        print("Request sent.")

        # Wait for all the data to be received
        while len(received_data) < TOTAL_SIZE:
            ack_event.clear()
            await ack_event.wait()  # Wait until notification handler sets the event

        await client.stop_notify(READ_CHAR_UUID)
        print("All data received.")

        # Print received data in hex (skip first 6 bytes and last 4 bytes)
        new_list = received_data[6:-4]
        hex_list = [hex(b) for b in new_list]
        for i in range(0, len(hex_list), 30):
            print(hex_list[i:i+30])
        print(f"Total received data (excluding header/footer): {len(new_list)} bytes")


if __name__ == "__main__":
    byte_array = bytes([0xA5, 0x50, 0x00, 0x00, 0x00, 0x00, 0xC0, 0x00, 0xEE, 0x35])
    asyncio.run(send_request(byte_array, crc32_iso(byte_array)))
