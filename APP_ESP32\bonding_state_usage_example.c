/*******************************************************************************
 * @file           : bonding_state_usage_example.c
 * @brief          : Example usage of BLE bonding state management functions
 * @details        : Demonstrates how to use the bonding state NVS functions
 *******************************************************************************/

#include "ble_app.h"
#include "esp_log.h"

static const char *TAG = "BONDING_EXAMPLE";

/**
 * @brief Example function showing how to enable bonding
 */
void enable_ble_bonding_example(void)
{
    ESP_LOGI(TAG, "=== Enabling BLE Bonding ===");
    
    // Set bonding state to enabled and save to NVS
    set_bonding_state_to_nvs(true);
    
    ESP_LOGI(TAG, "BLE bonding has been enabled and saved to NVS");
}

/**
 * @brief Example function showing how to disable bonding
 */
void disable_ble_bonding_example(void)
{
    ESP_LOGI(TAG, "=== Disabling BLE Bonding ===");
    
    // Set bonding state to disabled and save to NVS
    set_bonding_state_to_nvs(false);
    
    ESP_LOGI(TAG, "BLE bonding has been disabled and saved to NVS");
}

/**
 * @brief Example function showing how to clear bonding state
 */
void clear_ble_bonding_example(void)
{
    ESP_LOGI(TAG, "=== Clearing BLE Bonding State ===");
    
    // Clear bonding state from NVS and get previous state
    bool was_enabled = clear_bonding_state_from_nvs();
    
    if (was_enabled) {
        ESP_LOGI(TAG, "Bonding was previously enabled - now cleared");
    } else {
        ESP_LOGI(TAG, "Bonding was already disabled or not set");
    }
}

/**
 * @brief Example function showing how to load bonding state on startup
 */
void load_ble_bonding_on_startup_example(void)
{
    ESP_LOGI(TAG, "=== Loading BLE Bonding State on Startup ===");
    
    // Load bonding state from NVS
    bool bonding_enabled = load_bonding_state_from_nvs();
    
    if (bonding_enabled) {
        ESP_LOGI(TAG, "Bonding is enabled - device will maintain pairing info");
        // Here you would configure your BLE stack for bonding
        // Example: ble_hs_cfg.sm_bonding = 1;
    } else {
        ESP_LOGI(TAG, "Bonding is disabled - device will not store pairing info");
        // Here you would configure your BLE stack without bonding
        // Example: ble_hs_cfg.sm_bonding = 0;
    }
}

/**
 * @brief Example function showing complete bonding state management workflow
 */
void bonding_state_workflow_example(void)
{
    ESP_LOGI(TAG, "=== Complete Bonding State Workflow ===");
    
    // 1. Load current state on startup
    bool current_state = load_bonding_state_from_nvs();
    ESP_LOGI(TAG, "Current bonding state: %s", current_state ? "enabled" : "disabled");
    
    // 2. Enable bonding (e.g., when user wants to pair devices)
    ESP_LOGI(TAG, "User requests to enable bonding...");
    set_bonding_state_to_nvs(true);
    
    // 3. Verify the state was saved
    bool new_state = load_bonding_state_from_nvs();
    ESP_LOGI(TAG, "Bonding state after enabling: %s", new_state ? "enabled" : "disabled");
    
    // 4. Later, clear bonding state (e.g., factory reset or user request)
    ESP_LOGI(TAG, "User requests to clear bonding...");
    bool previous_state = clear_bonding_state_from_nvs();
    ESP_LOGI(TAG, "Previous state was: %s", previous_state ? "enabled" : "disabled");
    
    // 5. Verify bonding is now cleared
    bool final_state = load_bonding_state_from_nvs();
    ESP_LOGI(TAG, "Final bonding state: %s", final_state ? "enabled" : "disabled");
}

/**
 * @brief Example integration with BLE security initialization
 */
void integrate_bonding_with_security_example(void)
{
    ESP_LOGI(TAG, "=== Integrating Bonding State with BLE Security ===");
    
    // Load bonding state from NVS
    bool bonding_enabled = load_bonding_state_from_nvs();
    
    // Configure BLE security based on saved state
    if (bonding_enabled) {
        ESP_LOGI(TAG, "Configuring BLE with bonding enabled");
        // ble_hs_cfg.sm_bonding = 1;      // Enable bonding
        // ble_hs_cfg.sm_mitm = 1;         // Enable MITM protection
        // ble_hs_cfg.sm_sc = 1;           // Enable Secure Connections
    } else {
        ESP_LOGI(TAG, "Configuring BLE with bonding disabled");
        // ble_hs_cfg.sm_bonding = 0;      // Disable bonding
        // ble_hs_cfg.sm_mitm = 0;         // Disable MITM protection
        // ble_hs_cfg.sm_sc = 0;           // Disable Secure Connections
    }
}

/*
 * USAGE NOTES:
 * 
 * 1. Call load_bonding_state_from_nvs() during BLE initialization to restore state
 * 2. Call set_bonding_state_to_nvs(true) when user enables bonding
 * 3. Call set_bonding_state_to_nvs(false) when user disables bonding
 * 4. Call clear_bonding_state_from_nvs() for factory reset or clearing all bonding data
 * 
 * NVS Key: "ble_bonding_state" (stored as uint8_t: 0=disabled, 1=enabled)
 * NVS Namespace: "storage" (same as other BLE states)
 * 
 * Integration Points:
 * - Call in app_main() during startup
 * - Call in UART command handlers for bonding control
 * - Call in BLE security initialization
 * - Call in factory reset procedures
 */
