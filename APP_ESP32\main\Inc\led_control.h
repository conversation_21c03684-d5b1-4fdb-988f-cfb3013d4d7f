/******************************************************************************
 * @Copyright (C) 2025 by Vantage Elevation
 * @file           : led_control.h
 * @version        : 1.0.0
 * @brief          : LED control interface for status indication
 * @details        : Provides API for LED management:
 *                   - RED: System health/error indication
 *                   - AMBER: UART activity
 *                   - BLUE: BLE connection state
 *                   - GREEN: Heartbeat
 *                   Should be paired with FreeRTOS task-based implementation.
 *
 * @change-log     :
 *   07/23/2025, [1.0.0] : [*] Initial ver with API declarations and LED states
 ******************************************************************************/

#pragma once

#include <stdbool.h>
#include "driver/gpio.h"

/******************************************************************************
 * LED State Enum
 ******************************************************************************/

/**
 * @enum led_state_t
 * @brief States used for LED behavior control
 */
typedef enum {
    LED_STATE_OFF,           /*!< LED is off */
    LED_STATE_ON,            /*!< LED is constantly on */
    LED_STATE_BLINK_FAST,    /*!< LED blinks fast (500ms cycle) */
    LED_STATE_BLINK_SLOW,    /*!< LED blinks slow (1s cycle) */
    LED_STATE_BLINK_NORMAL   /*!< LED blinks normal (used for error, 1Hz) */
} led_state_t;

/******************************************************************************
 * Initialization
 ******************************************************************************/

/**
 * @brief Initialize all LED GPIOs.
 *
 * @note This must be called before any other LED function.
 */
void led_init_all(void);

/**
 * @brief Start LED control tasks.
 *
 * @note Creates FreeRTOS tasks for RED, BLUE, and GREEN LEDs.
 */
void led_start_tasks(void);

/******************************************************************************
 * Runtime Activity
 ******************************************************************************/

/**
 * @brief Pulse AMBER LED to indicate UART RX/TX activity.
 *
 * @note This function toggles the AMBER LED briefly. Call from UART handler.
 */
void led_uart_activity(void);

/******************************************************************************
 * State Control APIs
 ******************************************************************************/

/**
 * @brief Set RED LED state based on system error and comm status.
 *
 * @param has_error Set true if system has general error.
 * @param comm_loss Set true if comm loss detected (overrides blink).
 *
 * @note Error LED logic:
 *       - OFF if no error
 *       - Solid ON if comm loss
 *       - Blink (normal) on general error
 */
void led_set_error(bool has_error, bool comm_loss);

/**
 * @brief Set BLE status LED (BLUE) to desired state.
 *
 * @param state One of led_state_t values (OFF, ON, BLINK_FAST, etc.)
 */
void led_set_ble_status(led_state_t state);

/**
 * @brief Common LED function used for blink
 * @param pin GPIO number of the LED
 * @param on_time_ms ON duration in milliseconds
 * @param off_time_ms OFF duration in milliseconds
 */
void led_blink(gpio_num_t, int, int);

