/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : led_control.h
* @version        : 1.0.0
* @brief          : LED control interface for status indication
* @details        : LED control interface for status indication
********************************************************************************
* @version 1.0.0                                         				Date : 23/07/2025
* Initial ver with API declarations and LED states
********************************************************************************/

#pragma once

#include <stdbool.h>
#include "driver/gpio.h"

typedef enum {
    LED_STATE_OFF,
    LED_STATE_ON,
    LED_STATE_BLINK_FAST,
    LED_STATE_BLINK_SLOW,
    LED_STATE_BLINK_NORMAL
} led_state_t;

void led_init_all(void);
void led_start_tasks(void);
void led_uart_activity(void);
void led_set_error(bool has_error, bool comm_loss);
void led_set_ble_status(led_state_t state);
void led_blink(gpio_num_t, int, int);

