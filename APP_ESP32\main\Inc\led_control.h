/******************************************************************************
 * @Copyright (C) 2025 by Vantage Elevation
 * @file           : led_control.h
 * @version        : 1.0.0
 * @brief          : LED control interface for status indication
 * @details        : Provides API for LED management:
 *                   - RED: System health/error indication
 *                   - AMBER: UART activity
 *                   - BLUE: BLE connection state
 *                   - GREEN: Heartbeat
 *                   Should be paired with FreeRTOS task-based implementation.
 *
 * @change-log     :
 *   07/23/2025, [1.0.0] : [*] Initial ver with API declarations and LED states
 ******************************************************************************/

#pragma once

#include <stdbool.h>
#include "driver/gpio.h"

typedef enum {
    LED_STATE_OFF,
    LED_STATE_ON,
    LED_STATE_BLINK_FAST,
    LED_STATE_BLINK_SLOW,
    LED_STATE_BLINK_NORMAL
} led_state_t;

void led_init_all(void);
void led_start_tasks(void);
void led_uart_activity(void);
void led_set_error(bool has_error, bool comm_loss);
void led_set_ble_status(led_state_t state);
void led_blink(gpio_num_t, int, int);

