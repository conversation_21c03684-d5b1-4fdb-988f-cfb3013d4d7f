/*******************************************************************************
 * @Copyright (C) 2022 by Vantage Elevation
 * @file           : version.h
 * @version 		  : 1.0.0
 * @brief          : Current software version number
 * @details		  : Current software version number
 ********************************************************************************/
#ifndef _VERSION_H_
#define _VERSION_H_
/*******************************************************************************
 * Includes
 ********************************************************************************/

#ifdef __cplusplus
extern "C" {
#endif
/*******************************************************************************
 * Preprocessor Constants
 ********************************************************************************/
#define SOFTWARE_VERSION_MAJOR       (1) // Valid 1 to 65535
#define SOFTWARE_VERSION_MINOR	     (2) // Valid 0 to 65535
#define SOFTWARE_VERSION_PATCH       (0) // Valid 0 to 65535
#define SOFTWARE_VERSION_CUSTOM      (0) // 0 is standard production software, valid 0 to 65535

#define SW_VERSION_BITMASK           (0xFFFF)
/*******************************************************************************
 * Configuration Constants
 ********************************************************************************/

/*******************************************************************************
 * Macros
 ********************************************************************************/

/*******************************************************************************
 * Typedefs
 ********************************************************************************/

/*******************************************************************************
 * Global Variables
 ********************************************************************************/

/*******************************************************************************
 * Function Prototypes
 ********************************************************************************/

#ifdef __cplusplus
}
#endif

#endif /* _VERSION_H_ */
