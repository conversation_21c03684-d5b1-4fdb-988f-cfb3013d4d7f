/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : crc.h
* @version        : 1.0.1
* @brief          : Header for crc.c file
* @details        : Header for crc.c file
********************************************************************************
* @version 1.0.1                                         				Date : 09/07/2025
* Added CRC Init. Moved from bitwise to table approach
********************************************************************************/

#ifndef CRC_H
#define CRC_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C"
{
#endif

void crc32_init(void);
uint32_t crc32(const uint8_t *data, uint32_t length);
bool validate_crc(const uint8_t *data, uint32_t length, uint32_t crc_in_packet);

#ifdef __cplusplus
}
#endif

#endif /* END OF CRC_H */
