import os

def count_lines_in_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
            return sum(1 for _ in file)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return 0

def count_lines_in_directory(directory, file_extensions=None):
    total_lines = 0
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file_extensions:
                if not any(file.endswith(ext) for ext in file_extensions):
                    continue
            file_path = os.path.join(root, file)
            lines = count_lines_in_file(file_path)
            total_lines += lines
            print(f"{file_path}: {lines} lines")
    print(f"\nTotal lines of code: {total_lines}")
    return total_lines

if __name__ == "__main__":
    # Example: count only .c, .h, .cpp, .py files
    folder_path = "C:/Espressif/frameworks/esp-idf-v5.4.1/components/spiffs/"
    extensions = [".c", ".h", ".cpp", ".py"]  # Change or set to None to include all files
    count_lines_in_directory(folder_path, file_extensions=extensions)
