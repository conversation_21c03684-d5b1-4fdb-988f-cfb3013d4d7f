/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : uart_handler.h
* @version 		  : 1.0.3
* @brief          : Header for uart_handler.c file
* @details		  : Header for uart_handler.c file
********************************************************************************
 * @version 1.0.1                                        				Date : 18/07/2025
 * Modified payload len to 0x200
 * Added return uart initalization
 * ******************************************************************************************
 * @version 1.0.2                                        				Date : 21/07/2025
 * Added BLE_START_ADV and BLE_STOP_ADV command support
 * Added NVS state management for BLE advertising persistence
 * ******************************************************************************************
 * @version 1.0.3                                        				Date : 25/07/2025
 * Added UART Variable initialization
 * *******************************************************************************************/

#ifndef UART_HANDLER_H
#define UART_HANDLER_H

/* HEADER FILES */
#include <stdint.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/uart.h"
#include "esp_err.h"

#ifdef __cplusplus
extern "C"
{
#endif

/* MACROS */
#define PAYLOAD_LEN (0x200)

/* STRUCTS */
#pragma pack(1)
typedef struct
{
    uint8_t start_byte;
    uint8_t packet_type;
    uint32_t length;
    uint8_t payload[PAYLOAD_LEN];
    uint32_t crc;
} uart_data_pack_t;

typedef enum
{
    UART_READ_START,
    UART_READ_DATA,
    UART_READ_END
} uart_write_state_t;

/* FUNCTION PROTOTYPES */
esp_err_t uart_initialization(void);
uint8_t* uart_read_data(uint32_t* length);
void uart_event_task(void *pvParameters);
int32_t write_data(const void *src, uint32_t size);
void set_state_to_nvs(bool state);
void initialize_uart_vars(void);

#ifdef __cplusplus
}
#endif

#endif /* END OF UART_HANDLER_H */
