# Special partition table for unit test app
#
# Name,     Type, SubType, Offset,   Size, Flags
# Note: if you change the phy_init or app partition offset, make sure to change the offset in Kconfig.projbuild
nvs,        data, nvs,     0x9000,  0x4000
otadata,    data, ota,     0xd000,  0x2000
phy_init,   data, phy,     0xf000,  0x1000
factory,    0,    0,       0x10000, 2M
# these OTA partitions are used for tests, but can't fit real OTA apps in them
# (done this way so tests can run in 2MB of flash.)
ota_0,      0,    ota_0,   ,        64K
ota_1,      0,    ota_1,   ,        64K
# flash_test partition used for SPI flash tests, WL FAT tests, and SPIFFS tests
fat_store, data, fat,     ,        528K
spiffs_store,  data, spiffs,    ,        512K
flash_test,  data, spiffs,    ,        512K
