/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_request_handler.h
* @version 		  : 1.0.0
* @brief          : Header for ble_request_handler.c file
* @details		  : Header for ble_request_handler.c file
********************************************************************************/

#ifndef CONFIG_HANDLE_H
#define CONFIG_HANDLE_H

#include <stdint.h>
#include "uart_handler.h"

#ifdef __cplusplus
extern "C"
{
#endif

int32_t ble_request_handler(const uint8_t *data);

#ifdef __cplusplus
}
#endif

#endif /* END OF CONFIG_HANDLE_H*/
