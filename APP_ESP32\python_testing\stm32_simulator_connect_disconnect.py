import serial
import time

# Serial port configuration
SERIAL_PORT = 'COM15'
BAUD_RATE = 1000000

# Packet type definitions
BLE_CONNECTED = 0x12
BLE_DISCONNECTED = 0x13
BLE_ACK = 0x01
START_BYTE = 0xA5

def crc32_iso(data: bytes) -> int:
    poly = 0x04C11DB7
    crc = 0xFFFFFFFF

    for byte in data:
        byte = reflect(byte, 8)
        crc ^= byte << 24

        for _ in range(8):
            if crc & 0x80000000:
                crc = (crc << 1) ^ poly
            else:
                crc <<= 1
            crc &= 0xFFFFFFFF

    crc = reflect(crc, 32)
    return crc ^ 0xFFFFFFFF

def reflect(value, bits):
    result = 0
    for i in range(bits):
        if value & (1 << i):
            result |= 1 << (bits - 1 - i)
    return result

def send_ack(ser):
    ack_frame = bytearray([START_BYTE, BLE_ACK, 0, 0, 0, 0])
    crc = crc32_iso(ack_frame)
    ack_frame.extend(crc.to_bytes(4, 'big'))
    ser.write(ack_frame)
    print(f"Sent ACK: {ack_frame.hex()}")

def main():
    try:
        ser = serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1)
        print(f"Listening on {SERIAL_PORT}...")

        while True:
            if ser.in_waiting > 0:
                data = ser.read(ser.in_waiting)
                if data:
                    print(f"Received data: {data.hex()}")
                    if len(data) > 1:
                        if data[0] == START_BYTE:
                            packet_type = data[1]
                            if packet_type == BLE_CONNECTED:
                                print("Received BLE_CONNECTED command.")
                                send_ack(ser)
                            elif packet_type == BLE_DISCONNECTED:
                                print("Received BLE_DISCONNECTED command.")
                                send_ack(ser)
            time.sleep(0.1)

    except serial.SerialException as e:
        print(f"Error: {e}")
    finally:
        if 'ser' in locals() and ser.is_open:
            ser.close()
            print("Serial port closed.")

if __name__ == "__main__":
    main()
