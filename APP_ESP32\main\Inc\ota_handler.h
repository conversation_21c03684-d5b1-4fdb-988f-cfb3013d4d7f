/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ota_handler.h
* @version        : 1.0.0
* @brief          : Header for OTA firmware update over BLE
* @details        : Header for OTA firmware update over BLE
********************************************************************************/

#ifndef OTA_HANDLER_H
#define OTA_HANDLER_H

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <stdbool.h>
#include "esp_ota_ops.h"
#include "esp_partition.h"

// OTA packet types
#define OTA_START_REQUEST       0x10
#define OTA_DATA_CHUNK          0x11
#define OTA_END_REQUEST         0x12
#define OTA_STATUS_REQUEST      0x13

// OTA response types
#define OTA_START_RESPONSE      0x20
#define OTA_DATA_ACK            0x21
#define OTA_END_RESPONSE        0x22
#define OTA_STATUS_RESPONSE     0x23
#define OTA_ERROR_RESPONSE      0x2F

// OTA status codes
#define OTA_STATUS_IDLE         0x00
#define OTA_STATUS_IN_PROGRESS  0x01
#define OTA_STATUS_SUCCESS      0x02
#define OTA_STATUS_ERROR        0x03

// OTA error codes
#define OTA_ERROR_NONE          0x00
#define OTA_ERROR_INVALID_SIZE  0x01
#define OTA_ERROR_WRITE_FAILED  0x02
#define OTA_ERROR_VERIFY_FAILED 0x03
#define OTA_ERROR_NO_PARTITION  0x04
#define OTA_ERROR_INVALID_IMAGE 0x05

// OTA configuration
#define OTA_MAX_CHUNK_SIZE      500
#define OTA_HEADER_SIZE         10
#define OTA_MAX_RETRIES         3
#define OTA_TIMEOUT_MS          5000

// OTA state structure
typedef struct {
    esp_ota_handle_t ota_handle;
    const esp_partition_t *ota_partition;
    uint32_t total_size;
    uint32_t received_size;
    uint32_t chunk_count;
    uint32_t last_chunk_id;
    uint8_t status;
    uint8_t error_code;
    bool ota_active;
    uint32_t start_time;
} ota_state_t;

// Function declarations
esp_err_t ota_init(void);
esp_err_t ota_start(uint32_t firmware_size);
esp_err_t ota_write_chunk(uint32_t chunk_id, const uint8_t *data, uint32_t length);
esp_err_t ota_end(void);
esp_err_t ota_abort(void);
ota_state_t* ota_get_state(void);
void ota_reset_state(void);

// BLE handler functions
int ota_request_handler(const uint8_t *data, uint16_t length);
void ota_send_response(uint8_t response_type, uint8_t status, uint32_t data);
void ota_send_error(uint8_t error_code);

// Utility functions
uint32_t ota_calculate_progress_percent(void);
void ota_print_status(void);

#ifdef __cplusplus
}
#endif

#endif /* END OF OTA_HANDLER_H */
