/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_response_handler.h
* @version 		  : 1.0.2
* @brief          : Header for ble_response_handler.c file
* @details		  : Header for ble_response_handler.c file
********************************************************************************
* @version 1.0.1                                        				Date : 21/07/2025
* Added comprehensive documentation for ble_response_handler() function
* Enhanced function declarations with detailed parameter descriptions
********************************************************************************
* @version 1.0.2                                        				Date : 21/07/2025
* Renamed ble_response_handler() to send_ble_status_to_nexus()
* Updated function declaration to reflect ESP32 to Nexus board communication
********************************************************************************/

#ifndef BLE_RESPONSE_HANDLER_H
#define BLE_RESPONSE_HANDLER_H

#ifdef __cplusplus
extern "C"
{
#endif

void set_data_read_length(uint32_t length);
uint32_t get_data_read_length(void);
void store_config_file(uint8_t *data, uint32_t length);
void reset_buffer_variables(void);
void set_connection_handle(uint16_t hndl);
uint16_t get_connection_handle(void);
void send_ble_status_to_nexus(uint8_t packet_type, uint8_t *payload, uint32_t payload_len);

#ifdef __cplusplus
}
#endif

#endif /* END OF BLE_RESPONSE_HANDLER_H */
