/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ota_handler.c
* @version 		  : 1.0.0
* @brief          : OTA firmware update over BLE implementation
* @details		  : Handles OTA updates using ESP32 OTA APIs and BLE transport
********************************************************************************/

#include "ota_handler.h"
#include "ble_response_handler.h"
#include "esp_log.h"
#include "esp_ota_ops.h"
#include "esp_partition.h"
#include "esp_app_format.h"
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char* TAG = "OTA_HANDLER";
static ota_state_t ota_state = {0};

/**
 * @brief Initialize OTA subsystem
 */
esp_err_t ota_init(void)
{
    ESP_LOGI(TAG, "Initializing OTA handler");
    ota_reset_state();
    return ESP_OK;
}

/**
 * @brief Reset OTA state to initial values
 */
void ota_reset_state(void)
{
    memset(&ota_state, 0, sizeof(ota_state_t));
    ota_state.status = OTA_STATUS_IDLE;
    ota_state.error_code = OTA_ERROR_NONE;
    ota_state.ota_active = false;
}

/**
 * @brief Start OTA update process
 */
esp_err_t ota_start(uint32_t firmware_size)
{
    ESP_LOGI(TAG, "Starting OTA update, firmware size: %ld bytes", firmware_size);
    
    if (ota_state.ota_active) {
        ESP_LOGW(TAG, "OTA already in progress");
        return ESP_ERR_INVALID_STATE;
    }
    
    // Find OTA partition
    ota_state.ota_partition = esp_ota_get_next_update_partition(NULL);
    if (ota_state.ota_partition == NULL) {
        ESP_LOGE(TAG, "No OTA partition found");
        ota_state.error_code = OTA_ERROR_NO_PARTITION;
        return ESP_ERR_NOT_FOUND;
    }
    
    ESP_LOGI(TAG, "OTA partition: %s, size: %ld bytes", 
             ota_state.ota_partition->label, ota_state.ota_partition->size);
    
    // Check if firmware size fits in partition
    if (firmware_size > ota_state.ota_partition->size) {
        ESP_LOGE(TAG, "Firmware too large: %ld > %ld", firmware_size, ota_state.ota_partition->size);
        ota_state.error_code = OTA_ERROR_INVALID_SIZE;
        return ESP_ERR_INVALID_SIZE;
    }
    
    // Begin OTA
    esp_err_t err = esp_ota_begin(ota_state.ota_partition, firmware_size, &ota_state.ota_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_begin failed: %s", esp_err_to_name(err));
        ota_state.error_code = OTA_ERROR_WRITE_FAILED;
        return err;
    }
    
    // Initialize state
    ota_state.total_size = firmware_size;
    ota_state.received_size = 0;
    ota_state.chunk_count = 0;
    ota_state.last_chunk_id = 0;
    ota_state.status = OTA_STATUS_IN_PROGRESS;
    ota_state.ota_active = true;
    ota_state.start_time = xTaskGetTickCount();
    
    ESP_LOGI(TAG, "OTA started successfully");
    return ESP_OK;
}

/**
 * @brief Write firmware chunk to OTA partition
 */
esp_err_t ota_write_chunk(uint32_t chunk_id, const uint8_t *data, uint32_t length)
{
    if (!ota_state.ota_active) {
        ESP_LOGE(TAG, "OTA not active");
        return ESP_ERR_INVALID_STATE;
    }
    
    // Check for duplicate chunks
    if (chunk_id <= ota_state.last_chunk_id && ota_state.chunk_count > 0) {
        ESP_LOGW(TAG, "Duplicate chunk %ld, ignoring", chunk_id);
        return ESP_OK;
    }
    
    // Check if we're exceeding expected size
    if (ota_state.received_size + length > ota_state.total_size) {
        ESP_LOGE(TAG, "Chunk would exceed firmware size");
        ota_state.error_code = OTA_ERROR_INVALID_SIZE;
        return ESP_ERR_INVALID_SIZE;
    }
    
    // Write chunk to OTA partition
    esp_err_t err = esp_ota_write(ota_state.ota_handle, data, length);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_write failed: %s", esp_err_to_name(err));
        ota_state.error_code = OTA_ERROR_WRITE_FAILED;
        return err;
    }
    
    // Update state
    ota_state.received_size += length;
    ota_state.chunk_count++;
    ota_state.last_chunk_id = chunk_id;
    
    uint32_t progress = ota_calculate_progress_percent();
    ESP_LOGI(TAG, "Chunk %ld written (%ld bytes), progress: %ld%%", 
             chunk_id, length, progress);
    
    return ESP_OK;
}

/**
 * @brief Finalize OTA update
 */
esp_err_t ota_end(void)
{
    if (!ota_state.ota_active) {
        ESP_LOGE(TAG, "OTA not active");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "Finalizing OTA update");
    
    // Check if we received all data
    if (ota_state.received_size != ota_state.total_size) {
        ESP_LOGW(TAG, "Size mismatch: received %ld, expected %ld", 
                 ota_state.received_size, ota_state.total_size);
    }
    
    // End OTA
    esp_err_t err = esp_ota_end(ota_state.ota_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_end failed: %s", esp_err_to_name(err));
        ota_state.error_code = OTA_ERROR_VERIFY_FAILED;
        ota_state.status = OTA_STATUS_ERROR;
        return err;
    }
    
    // Set boot partition
    err = esp_ota_set_boot_partition(ota_state.ota_partition);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "esp_ota_set_boot_partition failed: %s", esp_err_to_name(err));
        ota_state.error_code = OTA_ERROR_VERIFY_FAILED;
        ota_state.status = OTA_STATUS_ERROR;
        return err;
    }
    
    ota_state.status = OTA_STATUS_SUCCESS;
    ota_state.ota_active = false;
    
    uint32_t total_time = pdTICKS_TO_MS(xTaskGetTickCount() - ota_state.start_time);
    ESP_LOGI(TAG, "OTA completed successfully in %ld ms", total_time);
    ESP_LOGI(TAG, "Firmware will be active after reboot");
    
    return ESP_OK;
}

/**
 * @brief Abort OTA update
 */
esp_err_t ota_abort(void)
{
    if (!ota_state.ota_active) {
        return ESP_OK;
    }
    
    ESP_LOGW(TAG, "Aborting OTA update");
    
    esp_ota_abort(ota_state.ota_handle);
    ota_state.status = OTA_STATUS_ERROR;
    ota_state.ota_active = false;
    
    return ESP_OK;
}

/**
 * @brief Get current OTA state
 */
ota_state_t* ota_get_state(void)
{
    return &ota_state;
}

/**
 * @brief Calculate progress percentage
 */
uint32_t ota_calculate_progress_percent(void)
{
    if (ota_state.total_size == 0) {
        return 0;
    }
    return (ota_state.received_size * 100) / ota_state.total_size;
}

/**
 * @brief Print current OTA status
 */
void ota_print_status(void)
{
    ESP_LOGI(TAG, "OTA Status:");
    ESP_LOGI(TAG, "  Active: %s", ota_state.ota_active ? "YES" : "NO");
    ESP_LOGI(TAG, "  Status: %d", ota_state.status);
    ESP_LOGI(TAG, "  Progress: %ld/%ld bytes (%ld%%)", 
             ota_state.received_size, ota_state.total_size, 
             ota_calculate_progress_percent());
    ESP_LOGI(TAG, "  Chunks: %ld", ota_state.chunk_count);
    ESP_LOGI(TAG, "  Error: %d", ota_state.error_code);
}

/**
 * @brief Send OTA response via BLE
 */
void ota_send_response(uint8_t response_type, uint8_t status, uint32_t data)
{
    uint8_t response[16];
    response[0] = 0xAA;  // Start byte
    response[1] = response_type;
    response[2] = status;
    response[3] = (data >> 0) & 0xFF;
    response[4] = (data >> 8) & 0xFF;
    response[5] = (data >> 16) & 0xFF;
    response[6] = (data >> 24) & 0xFF;
    
    // Use existing BLE infrastructure to send response
    store_config_file(response, 7);
    //send_config_file();
}

/**
 * @brief Send OTA error response
 */
void ota_send_error(uint8_t error_code)
{
    ESP_LOGE(TAG, "Sending OTA error: %d", error_code);
    ota_send_response(OTA_ERROR_RESPONSE, error_code, 0);
}

/**
 * @brief Handle OTA requests from BLE
 */
int ota_request_handler(const uint8_t *data, uint16_t length)
{
    if (length < 10) {  // Minimum packet size
        ESP_LOGE(TAG, "OTA packet too short: %d bytes", length);
        return -1;
    }

    uint8_t start_byte = data[0];
    uint8_t packet_type = data[1];
    uint32_t data_length = *(uint32_t*)&data[2];  // Little endian
    uint32_t chunk_id = *(uint32_t*)&data[6];     // Little endian

    if (start_byte != 0xAA) {
        ESP_LOGE(TAG, "Invalid OTA start byte: 0x%02X", start_byte);
        ota_send_error(OTA_ERROR_INVALID_IMAGE);
        return -1;
    }

    ESP_LOGI(TAG, "OTA packet: type=0x%02X, length=%ld, chunk_id=%ld",
             packet_type, data_length, chunk_id);

    switch (packet_type) {
        case OTA_START_REQUEST: {
            uint32_t firmware_size = data_length;
            ESP_LOGI(TAG, "OTA Start Request: firmware size = %ld bytes", firmware_size);

            esp_err_t err = ota_start(firmware_size);
            if (err == ESP_OK) {
                ota_send_response(OTA_START_RESPONSE, OTA_STATUS_IN_PROGRESS, firmware_size);
                ESP_LOGI(TAG, "OTA started successfully");
            } else {
                ota_send_error(ota_state.error_code);
                ESP_LOGE(TAG, "OTA start failed: %s", esp_err_to_name(err));
            }
            break;
        }

        case OTA_DATA_CHUNK: {
            if (!ota_state.ota_active) {
                ESP_LOGE(TAG, "OTA not active, ignoring data chunk");
                ota_send_error(OTA_ERROR_INVALID_IMAGE);
                return -1;
            }

            const uint8_t *chunk_data = &data[10];  // Skip header
            uint32_t chunk_size = data_length;

            if (chunk_size > OTA_MAX_CHUNK_SIZE) {
                ESP_LOGE(TAG, "Chunk too large: %ld bytes", chunk_size);
                ota_send_error(OTA_ERROR_INVALID_SIZE);
                return -1;
            }

            esp_err_t err = ota_write_chunk(chunk_id, chunk_data, chunk_size);
            if (err == ESP_OK) {
                uint32_t progress = ota_calculate_progress_percent();
                ota_send_response(OTA_DATA_ACK, OTA_STATUS_IN_PROGRESS, progress);
            } else {
                ota_send_error(ota_state.error_code);
                ESP_LOGE(TAG, "OTA write chunk failed: %s", esp_err_to_name(err));
            }
            break;
        }

        case OTA_END_REQUEST: {
            ESP_LOGI(TAG, "OTA End Request");

            if (!ota_state.ota_active) {
                ESP_LOGE(TAG, "OTA not active");
                ota_send_error(OTA_ERROR_INVALID_IMAGE);
                return -1;
            }

            esp_err_t err = ota_end();
            if (err == ESP_OK) {
                ota_send_response(OTA_END_RESPONSE, OTA_STATUS_SUCCESS, 100);
                ESP_LOGI(TAG, "OTA completed successfully");

                // Schedule reboot after a delay
                vTaskDelay(pdMS_TO_TICKS(2000));
                esp_restart();
            } else {
                ota_send_error(ota_state.error_code);
                ESP_LOGE(TAG, "OTA end failed: %s", esp_err_to_name(err));
            }
            break;
        }

        case OTA_STATUS_REQUEST: {
            ESP_LOGI(TAG, "OTA Status Request");
            uint32_t progress = ota_calculate_progress_percent();
            ota_send_response(OTA_STATUS_RESPONSE, ota_state.status, progress);
            ota_print_status();
            break;
        }

        default:
            ESP_LOGE(TAG, "Unknown OTA packet type: 0x%02X", packet_type);
            ota_send_error(OTA_ERROR_INVALID_IMAGE);
            return -1;
    }

    return 0;
}
