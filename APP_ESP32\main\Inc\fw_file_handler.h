/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : fw_file_handler.h
* @version 		  : 1.0.1
* @brief          : Header for fw_file_handler.c file
* @details		  : Header for fw_file_handler.c file
********************************************************************************
* @version 1.0.1                                         				Date : 24/07/2025
* Modified the return type of send_flash_task 
********************************************************************************/
#ifndef FW_FILE_HANDLE_H
#define FW_FILE_HANDLE_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C"
{
#endif

/* DEFINES */
#define BUF_SIZE (1024)
#define FW_FILE_CHUNK (256)
#define BUFFER_SIZE ((FW_FILE_CHUNK * BUF_SIZE) + HEADER_SIZE + CRC_LENGTH) // 256KB
#define CHUNK_SIZE (500)
#define HALF_SIZE (1024 * 128)

void ble_data_write_task(void *param);
void set_last_packet(uint8_t half, uint32_t offset);
int32_t erase_flash(void);
int32_t start_fw_bundle_transfer(void);

#ifdef __cplusplus
}
#endif

#endif /* END OF FW_FILE_HANDLE_H */