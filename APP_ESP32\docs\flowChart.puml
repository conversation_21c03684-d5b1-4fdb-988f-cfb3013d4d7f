@startuml read_configuration
title Read Configuration file

participant Mobile as A
participant ESP32 as B
participant "Nexus" as M

A -> B : Read Config File Request (0x50)
B -> M : Forward Config File Request to Nexus
M -> B : Configuration File (Whole File) (0x51)
B --> A : Notify Config. File in Chunk (in 500 bytes payload)

@enduml

@startuml write_configuration
title Write Configuration file

participant Mobile as A
participant ESP32 as B
participant "Nexus" as M

A -> B : Configuration File will be sent in chunk of 500 bytes(0x56)
B -> B : Store it in buffer.
B -> M : Once all packet is received, forward Config File to Nexus (0x56)
M -> B : Acknowledge byte sent from 
B --> A : Notify Config. File in Chunk (in 500 bytes payload)

@enduml

@startuml "FW File Transfer Sequence Diagram"
title Firmware File Transfer via BLE (Mobile App ↔ ESP32 with NACK on CRC Failure)

participant "Mobile App" as App
participant "ESP32" as ESP

App -> ESP : Write Start Packet\n(0xA5 0x20 ... Start CRC)
ESP --> App : ACK Notification\n(0x01)

loop For each chunk
    App -> ESP : Write Chunk Packet\n(0xA5 0x21 + Payload + CRC)
    alt CRC Valid
        note right of ESP : No ACK sent
    else CRC Invalid
        ESP --> App : NACK Notification\n(0x00)
        note right of APP : "Restart from start"
    end
end

App -> ESP : Write End Packet\n(0xA5 0x22 ... End CRC)
ESP --> App : ACK Notification\n(0x01)

@enduml
