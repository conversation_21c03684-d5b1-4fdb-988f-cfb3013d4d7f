[cases.test_attrs_get_set]
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "hello") => 0;
    lfs_file_t file;
    lfs_file_open(&lfs, &file, "hello/hello", LFS_O_WRONLY | LFS_O_CREAT) => 0;
    lfs_file_write(&lfs, &file, "hello", strlen("hello")) => strlen("hello");
    lfs_file_close(&lfs, &file);
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    uint8_t buffer[1024];
    memset(buffer, 0, sizeof(buffer));
    lfs_setattr(&lfs, "hello", 'A', "aaaa",   4) => 0;
    lfs_setattr(&lfs, "hello", 'B', "bbbbbb", 6) => 0;
    lfs_setattr(&lfs, "hello", 'C', "ccccc",  5) => 0;
    lfs_getattr(&lfs, "hello", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "hello", 'B', buffer+4,  6) => 6;
    lfs_getattr(&lfs, "hello", 'C', buffer+10, 5) => 5;
    memcmp(buffer,    "aaaa",   4) => 0;
    memcmp(buffer+4,  "bbbbbb", 6) => 0;
    memcmp(buffer+10, "ccccc",  5) => 0;

    lfs_setattr(&lfs, "hello", 'B', "", 0) => 0;
    lfs_getattr(&lfs, "hello", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "hello", 'B', buffer+4,  6) => 0;
    lfs_getattr(&lfs, "hello", 'C', buffer+10, 5) => 5;
    memcmp(buffer,    "aaaa",         4) => 0;
    memcmp(buffer+4,  "\0\0\0\0\0\0", 6) => 0;
    memcmp(buffer+10, "ccccc",        5) => 0;

    lfs_removeattr(&lfs, "hello", 'B') => 0;
    lfs_getattr(&lfs, "hello", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "hello", 'B', buffer+4,  6) => LFS_ERR_NOATTR;
    lfs_getattr(&lfs, "hello", 'C', buffer+10, 5) => 5;
    memcmp(buffer,    "aaaa",         4) => 0;
    memcmp(buffer+4,  "\0\0\0\0\0\0", 6) => 0;
    memcmp(buffer+10, "ccccc",        5) => 0;

    lfs_setattr(&lfs, "hello", 'B', "dddddd", 6) => 0;
    lfs_getattr(&lfs, "hello", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "hello", 'B', buffer+4,  6) => 6;
    lfs_getattr(&lfs, "hello", 'C', buffer+10, 5) => 5;
    memcmp(buffer,    "aaaa",   4) => 0;
    memcmp(buffer+4,  "dddddd", 6) => 0;
    memcmp(buffer+10, "ccccc",  5) => 0;

    lfs_setattr(&lfs, "hello", 'B', "eee", 3) => 0;
    lfs_getattr(&lfs, "hello", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "hello", 'B', buffer+4,  6) => 3;
    lfs_getattr(&lfs, "hello", 'C', buffer+10, 5) => 5;
    memcmp(buffer,    "aaaa",      4) => 0;
    memcmp(buffer+4,  "eee\0\0\0", 6) => 0;
    memcmp(buffer+10, "ccccc",     5) => 0;

    lfs_setattr(&lfs, "hello", 'A', buffer, LFS_ATTR_MAX+1) => LFS_ERR_NOSPC;
    lfs_setattr(&lfs, "hello", 'B', "fffffffff", 9) => 0;
    lfs_getattr(&lfs, "hello", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "hello", 'B', buffer+4,  6) => 9;
    lfs_getattr(&lfs, "hello", 'C', buffer+10, 5) => 5;

    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    memset(buffer, 0, sizeof(buffer));
    lfs_getattr(&lfs, "hello", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "hello", 'B', buffer+4,  9) => 9;
    lfs_getattr(&lfs, "hello", 'C', buffer+13, 5) => 5;
    memcmp(buffer,    "aaaa",      4) => 0;
    memcmp(buffer+4,  "fffffffff", 9) => 0;
    memcmp(buffer+13, "ccccc",     5) => 0;

    lfs_file_open(&lfs, &file, "hello/hello", LFS_O_RDONLY) => 0;
    lfs_file_read(&lfs, &file, buffer, sizeof(buffer)) => strlen("hello");
    memcmp(buffer, "hello", strlen("hello")) => 0;
    lfs_file_close(&lfs, &file);
    lfs_unmount(&lfs) => 0;
'''

[cases.test_attrs_get_set_root]
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "hello") => 0;
    lfs_file_t file;
    lfs_file_open(&lfs, &file, "hello/hello", LFS_O_WRONLY | LFS_O_CREAT) => 0;
    lfs_file_write(&lfs, &file, "hello", strlen("hello")) => strlen("hello");
    lfs_file_close(&lfs, &file);
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    uint8_t buffer[1024];
    memset(buffer, 0, sizeof(buffer));
    lfs_setattr(&lfs, "/", 'A', "aaaa",   4) => 0;
    lfs_setattr(&lfs, "/", 'B', "bbbbbb", 6) => 0;
    lfs_setattr(&lfs, "/", 'C', "ccccc",  5) => 0;
    lfs_getattr(&lfs, "/", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "/", 'B', buffer+4,  6) => 6;
    lfs_getattr(&lfs, "/", 'C', buffer+10, 5) => 5;
    memcmp(buffer,    "aaaa",   4) => 0;
    memcmp(buffer+4,  "bbbbbb", 6) => 0;
    memcmp(buffer+10, "ccccc",  5) => 0;

    lfs_setattr(&lfs, "/", 'B', "", 0) => 0;
    lfs_getattr(&lfs, "/", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "/", 'B', buffer+4,  6) => 0;
    lfs_getattr(&lfs, "/", 'C', buffer+10, 5) => 5;
    memcmp(buffer,    "aaaa",         4) => 0;
    memcmp(buffer+4,  "\0\0\0\0\0\0", 6) => 0;
    memcmp(buffer+10, "ccccc",        5) => 0;

    lfs_removeattr(&lfs, "/", 'B') => 0;
    lfs_getattr(&lfs, "/", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "/", 'B', buffer+4,  6) => LFS_ERR_NOATTR;
    lfs_getattr(&lfs, "/", 'C', buffer+10, 5) => 5;
    memcmp(buffer,    "aaaa",         4) => 0;
    memcmp(buffer+4,  "\0\0\0\0\0\0", 6) => 0;
    memcmp(buffer+10, "ccccc",        5) => 0;

    lfs_setattr(&lfs, "/", 'B', "dddddd", 6) => 0;
    lfs_getattr(&lfs, "/", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "/", 'B', buffer+4,  6) => 6;
    lfs_getattr(&lfs, "/", 'C', buffer+10, 5) => 5;
    memcmp(buffer,    "aaaa",   4) => 0;
    memcmp(buffer+4,  "dddddd", 6) => 0;
    memcmp(buffer+10, "ccccc",  5) => 0;

    lfs_setattr(&lfs, "/", 'B', "eee", 3) => 0;
    lfs_getattr(&lfs, "/", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "/", 'B', buffer+4,  6) => 3;
    lfs_getattr(&lfs, "/", 'C', buffer+10, 5) => 5;
    memcmp(buffer,    "aaaa",      4) => 0;
    memcmp(buffer+4,  "eee\0\0\0", 6) => 0;
    memcmp(buffer+10, "ccccc",     5) => 0;

    lfs_setattr(&lfs, "/", 'A', buffer, LFS_ATTR_MAX+1) => LFS_ERR_NOSPC;
    lfs_setattr(&lfs, "/", 'B', "fffffffff", 9) => 0;
    lfs_getattr(&lfs, "/", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "/", 'B', buffer+4,  6) => 9;
    lfs_getattr(&lfs, "/", 'C', buffer+10, 5) => 5;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    memset(buffer, 0, sizeof(buffer));
    lfs_getattr(&lfs, "/", 'A', buffer,    4) => 4;
    lfs_getattr(&lfs, "/", 'B', buffer+4,  9) => 9;
    lfs_getattr(&lfs, "/", 'C', buffer+13, 5) => 5;
    memcmp(buffer,    "aaaa",      4) => 0;
    memcmp(buffer+4,  "fffffffff", 9) => 0;
    memcmp(buffer+13, "ccccc",     5) => 0;

    lfs_file_open(&lfs, &file, "hello/hello", LFS_O_RDONLY) => 0;
    lfs_file_read(&lfs, &file, buffer, sizeof(buffer)) => strlen("hello");
    memcmp(buffer, "hello", strlen("hello")) => 0;
    lfs_file_close(&lfs, &file);
    lfs_unmount(&lfs) => 0;
'''

[cases.test_attrs_get_set_file]
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "hello") => 0;
    lfs_file_t file;
    lfs_file_open(&lfs, &file, "hello/hello", LFS_O_WRONLY | LFS_O_CREAT) => 0;
    lfs_file_write(&lfs, &file, "hello", strlen("hello")) => strlen("hello");
    lfs_file_close(&lfs, &file);
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    uint8_t buffer[1024];
    memset(buffer, 0, sizeof(buffer));
    struct lfs_attr attrs1[] = {
        {'A', buffer,    4},
        {'B', buffer+4,  6},
        {'C', buffer+10, 5},
    };
    struct lfs_file_config cfg1 = {.attrs=attrs1, .attr_count=3};

    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_WRONLY, &cfg1) => 0;
    memcpy(buffer,    "aaaa",   4);
    memcpy(buffer+4,  "bbbbbb", 6);
    memcpy(buffer+10, "ccccc",  5);
    lfs_file_close(&lfs, &file) => 0;
    memset(buffer, 0, 15);
    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_RDONLY, &cfg1) => 0;
    lfs_file_close(&lfs, &file) => 0;
    memcmp(buffer,    "aaaa",   4) => 0;
    memcmp(buffer+4,  "bbbbbb", 6) => 0;
    memcmp(buffer+10, "ccccc",  5) => 0;

    attrs1[1].size = 0;
    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_WRONLY, &cfg1) => 0;
    lfs_file_close(&lfs, &file) => 0;
    memset(buffer, 0, 15);
    attrs1[1].size = 6;
    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_RDONLY, &cfg1) => 0;
    lfs_file_close(&lfs, &file) => 0;
    memcmp(buffer,    "aaaa",         4) => 0;
    memcmp(buffer+4,  "\0\0\0\0\0\0", 6) => 0;
    memcmp(buffer+10, "ccccc",        5) => 0;

    attrs1[1].size = 6;
    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_WRONLY, &cfg1) => 0;
    memcpy(buffer+4,  "dddddd", 6);
    lfs_file_close(&lfs, &file) => 0;
    memset(buffer, 0, 15);
    attrs1[1].size = 6;
    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_RDONLY, &cfg1) => 0;
    lfs_file_close(&lfs, &file) => 0;
    memcmp(buffer,    "aaaa",   4) => 0;
    memcmp(buffer+4,  "dddddd", 6) => 0;
    memcmp(buffer+10, "ccccc",  5) => 0;

    attrs1[1].size = 3;
    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_WRONLY, &cfg1) => 0;
    memcpy(buffer+4,  "eee", 3);
    lfs_file_close(&lfs, &file) => 0;
    memset(buffer, 0, 15);
    attrs1[1].size = 6;
    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_RDONLY, &cfg1) => 0;
    lfs_file_close(&lfs, &file) => 0;
    memcmp(buffer,    "aaaa",      4) => 0;
    memcmp(buffer+4,  "eee\0\0\0", 6) => 0;
    memcmp(buffer+10, "ccccc",     5) => 0;

    attrs1[0].size = LFS_ATTR_MAX+1;
    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_WRONLY, &cfg1)
        => LFS_ERR_NOSPC;

    struct lfs_attr attrs2[] = {
        {'A', buffer,    4},
        {'B', buffer+4,  9},
        {'C', buffer+13, 5},
    };
    struct lfs_file_config cfg2 = {.attrs=attrs2, .attr_count=3};
    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_RDWR, &cfg2) => 0;
    memcpy(buffer+4,  "fffffffff", 9);
    lfs_file_close(&lfs, &file) => 0;
    attrs1[0].size = 4;
    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_RDONLY, &cfg1) => 0;
    lfs_file_close(&lfs, &file) => 0;

    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    memset(buffer, 0, sizeof(buffer));
    struct lfs_attr attrs3[] = {
        {'A', buffer,    4},
        {'B', buffer+4,  9},
        {'C', buffer+13, 5},
    };
    struct lfs_file_config cfg3 = {.attrs=attrs3, .attr_count=3};

    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_RDONLY, &cfg3) => 0;
    lfs_file_close(&lfs, &file) => 0;
    memcmp(buffer,    "aaaa",      4) => 0;
    memcmp(buffer+4,  "fffffffff", 9) => 0;
    memcmp(buffer+13, "ccccc",     5) => 0;

    lfs_file_open(&lfs, &file, "hello/hello", LFS_O_RDONLY) => 0;
    lfs_file_read(&lfs, &file, buffer, sizeof(buffer)) => strlen("hello");
    memcmp(buffer, "hello", strlen("hello")) => 0;
    lfs_file_close(&lfs, &file);
    lfs_unmount(&lfs) => 0;
'''

[cases.test_attrs_deferred_file]
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "hello") => 0;
    lfs_file_t file;
    lfs_file_open(&lfs, &file, "hello/hello", LFS_O_WRONLY | LFS_O_CREAT) => 0;
    lfs_file_write(&lfs, &file, "hello", strlen("hello")) => strlen("hello");
    lfs_file_close(&lfs, &file);
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_setattr(&lfs, "hello/hello", 'B', "fffffffff",  9) => 0;
    lfs_setattr(&lfs, "hello/hello", 'C', "ccccc",      5) => 0;

    uint8_t buffer[1024];
    memset(buffer, 0, sizeof(buffer));
    struct lfs_attr attrs1[] = {
        {'B', "gggg", 4},
        {'C', "",     0},
        {'D', "hhhh", 4},
    };
    struct lfs_file_config cfg1 = {.attrs=attrs1, .attr_count=3};

    lfs_file_opencfg(&lfs, &file, "hello/hello", LFS_O_WRONLY, &cfg1) => 0;

    lfs_getattr(&lfs, "hello/hello", 'B', buffer,    9) => 9;
    lfs_getattr(&lfs, "hello/hello", 'C', buffer+9,  9) => 5;
    lfs_getattr(&lfs, "hello/hello", 'D', buffer+18, 9) => LFS_ERR_NOATTR;
    memcmp(buffer,    "fffffffff",          9) => 0;
    memcmp(buffer+9,  "ccccc\0\0\0\0",      9) => 0;
    memcmp(buffer+18, "\0\0\0\0\0\0\0\0\0", 9) => 0;

    lfs_file_sync(&lfs, &file) => 0;
    lfs_getattr(&lfs, "hello/hello", 'B', buffer,    9) => 4;
    lfs_getattr(&lfs, "hello/hello", 'C', buffer+9,  9) => 0;
    lfs_getattr(&lfs, "hello/hello", 'D', buffer+18, 9) => 4;
    memcmp(buffer,    "gggg\0\0\0\0\0",     9) => 0;
    memcmp(buffer+9,  "\0\0\0\0\0\0\0\0\0", 9) => 0;
    memcmp(buffer+18, "hhhh\0\0\0\0\0",     9) => 0;

    lfs_file_close(&lfs, &file) => 0;
    lfs_unmount(&lfs) => 0;
'''
